{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/app/favicon--route-entry.js"], "sourcesContent": ["import { NextResponse } from 'next/server'\n\nconst contentType = \"image/x-icon\"\nconst cacheControl = \"public, max-age=0, must-revalidate\"\nconst buffer = Buffer.from(\"AAABAAEAEBAAAAEAIABoBAAAFgAAACgAAAAQAAAAIAAAAAEAIAAAAAAAAAQAABMLAAATCwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/yXBNv8kwYT/JcGI/yXBiP8lwYj/JcGI/yXBiP8lwYj/JMGE/yXBNgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP8rs5T/VaoD/ym0H/8qqgYAAAAAAAAAAAAAAAAAAAAA/1WqA/8rs5QAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD/L6eAAAAAAP8wpsT/L6ZlAAAAAP8qqhL/L6Zw/yqqEgAAAAD/L6eAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/zWZigAAAAD/N5cg/ySRBwAAAAD/NZhy/zeXSv82mXEAAAAA/zWZigAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP88ijv/O4uF/z9/CAAAAAAAAAAA/zWGE/88i3H/PY4Z/zuLhf89jDoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/z9/HP9BfZT/RH8eAAAAAAAAAAD/RH8e/0F9lP8/fxwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD/VX8G/0hxhQAAAAAAAAAA/0hxhf9VfwYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP9NY4AAAAAA/01kWf9MZJ0AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD/U1WAAAAAAP9VVR7/UladAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/1lJgAAAAAD/WEdZ/1lJnQAAAAAAAAAA/6rfMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/186MP9fO5b/Xjx3/148d/9fO5b/Xzow/7arLv+4reD/tqsuAAAAAAAAAAAAAAAAAAAAAAAAAAD/scuK/7PPG/9pLRH/ZC2Y/2Qumf9kLpn/ZC2Y/2ktEQAAAAD/xHowAAAAAAAAAAAAAAAAAAAAAAAAAAD/uJ0v/7uf2/+6nooAAAAA/2keEf9pIHb/aSB2/2keEQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP/DcS8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA//8AAOAHAADh5wAA6RcAAOkXAADjBwAA8Y8AAPmfAAD9PwAA/T8AAP03AAD4AwAA4BcAAMQ/AADv/wAA//8AAA==\", 'base64')\n\nif (false || false) {\n    const fileSizeInMB = buffer.byteLength / 1024 / 1024\n    if (fileSizeInMB > 8) {\n        throw new Error('File size for Open Graph image \"[project]/src/app/favicon.ico\" exceeds 8MB. ' +\n        `(Current: ${fileSizeInMB.toFixed(2)}MB)\\n` +\n        'Read more: https://nextjs.org/docs/app/api-reference/file-conventions/metadata/opengraph-image#image-files-jpg-png-gif'\n        )\n    }\n}\n\nexport function GET() {\n    return new NextResponse(buffer, {\n        headers: {\n            'Content-Type': contentType,\n            'Cache-Control': cacheControl,\n        },\n    })\n}\n\nexport const dynamic = 'force-static'\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,cAAc;AACpB,MAAM,eAAe;AACrB,MAAM,SAAS,OAAO,IAAI,CAAC,ogDAAogD;AAE/hD,uCAAoB;;AAQpB;AAEO,SAAS;IACZ,OAAO,IAAI,8HAAA,CAAA,eAAY,CAAC,QAAQ;QAC5B,SAAS;YACL,gBAAgB;YAChB,iBAAiB;QACrB;IACJ;AACJ;AAEO,MAAM,UAAU", "debugId": null}}]}