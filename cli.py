#!/usr/bin/env python
"""
CLI para ejecutar tests  desde la línea de comandos.
Permite ejecutar smoke tests y full tests sin necesidad de la interfaz web.
"""

import os
import sys
import json
import asyncio
import argparse
from datetime import datetime
from dotenv import load_dotenv

# Asegurarse de que la API key de Google Gemini esté disponible
load_dotenv()

# Verificar que la API key esté configurada
if not os.environ.get("GOOGLE_API_KEY"):
    print("Error: No se encontró la API key de Google Gemini. Por favor, configura la variable de entorno GOOGLE_API_KEY.")
    sys.exit(1)

# Importar dependencias después de verificar la API key
from src.Utilities.browser_helper import Browser
from src.Utilities.utils import controller
from langchain_google_genai import ChatGoogleGenerativeAI
from src.Core.prompt_service import PromptService

# Initialize PromptService
prompt_service = PromptService()

# Wrapper functions to replace legacy prompt functions
def generate_browser_task(**kwargs):
    """Generate browser automation task using PromptService"""
    return prompt_service.get_prompt("browser-automation", "task-generation", **kwargs)

def generate_selenium_pytest_bdd(**kwargs):
    """Generate Selenium PyTest BDD code using PromptService"""
    return prompt_service.get_prompt("code-generation", "selenium-pytest", **kwargs)

def generate_playwright_python(**kwargs):
    """Generate Playwright Python code using PromptService"""
    return prompt_service.get_prompt("code-generation", "playwright", **kwargs)

def generate_cypress_js(**kwargs):
    """Generate Cypress JavaScript code using PromptService"""
    return prompt_service.get_prompt("code-generation", "cypress", **kwargs)

def generate_robot_framework(**kwargs):
    """Generate Robot Framework code using PromptService"""
    return prompt_service.get_prompt("code-generation", "robot-framework", **kwargs)

def generate_java_selenium(**kwargs):
    """Generate Java Selenium code using PromptService"""
    return prompt_service.get_prompt("code-generation", "java-selenium", **kwargs)

def enhance_user_story(**kwargs):
    """Enhance user story using PromptService"""
    return prompt_service.get_prompt("user-story", "enhance", **kwargs)

# Diccionario de frameworks disponibles
FRAMEWORK_GENERATORS = {
    "selenium": generate_selenium_pytest_bdd,
    "playwright": generate_playwright_python,
    "cypress": generate_cypress_js,
    "robot": generate_robot_framework,
    "java": generate_java_selenium
}

# Importar el servicio central de pruebas
from src.Core.test_service import TestService

# Función principal para ejecutar desde la línea de comandos
def main():
    parser = argparse.ArgumentParser(description="CLI para ejecutar tests")

    # Subparsers para diferentes comandos
    subparsers = parser.add_subparsers(dest="command", help="Comando a ejecutar")

    # Comando para ejecutar un smoke test
    smoke_parser = subparsers.add_parser("smoke", help="Ejecutar un smoke test")
    smoke_parser.add_argument("--url", help="URL para ejecutar el test")
    smoke_parser.add_argument("--instructions", required=True, help="Instrucciones para el test")
    smoke_parser.add_argument("--user-story", help="Historia de usuario (opcional)")
    smoke_parser.add_argument("--generate-code", choices=list(FRAMEWORK_GENERATORS.keys()), help="Generar código de automatización para el framework especificado")
    
    # Comando para ejecutar un full test
    full_parser = subparsers.add_parser("full", help="Ejecutar un test completo con escenarios Gherkin")
    full_parser.add_argument("--url", help="URL para ejecutar el test")
    full_parser.add_argument("--gherkin", required=True, help="Escenario Gherkin para ejecutar")
    full_parser.add_argument("--gherkin-file", help="Archivo con escenario Gherkin para ejecutar (alternativa a --gherkin)")
    full_parser.add_argument("--generate-code", choices=list(FRAMEWORK_GENERATORS.keys()), help="Generar código de automatización para el framework especificado")

    # Comando para generar código desde un historial existente
    code_parser = subparsers.add_parser("generate-code", help="Generar código de automatización desde un historial existente")
    code_parser.add_argument("--history-path", required=True, help="Ruta al archivo JSON de historial")
    code_parser.add_argument("--framework", required=True, choices=list(FRAMEWORK_GENERATORS.keys()), help="Framework para generar el código")
    code_parser.add_argument("--output", help="Ruta de salida para el código generado")

    args = parser.parse_args()

    # Manejar el comando smoke
    if args.command == "smoke":
        print("Ejecutando Smoke Test...")

        # Inicializar el servicio de tests
        test_service = TestService(api_key=os.environ.get("GOOGLE_API_KEY"))

        # Crear el escenario Gherkin
        gherkin_scenario = test_service.create_gherkin_scenario(
            instructions=args.instructions,
            url=args.url,
            user_story=args.user_story
        )

        print(f"Escenario Gherkin generado:\n{gherkin_scenario}")

        # Ejecutar el test
        result = test_service.run_smoke_test(
            instructions=args.instructions,
            url=args.url,
            user_story=args.user_story
        )

        # Mostrar resultados
        if result["success"]:
            print(f"Test ejecutado exitosamente. ID: {result['test_id']}")
            print(f"Historial guardado en: {result['history_path']}")

            # Generar código si se solicitó
            if args.generate_code:
                try:
                    # Cargar el historial
                    with open(result["history_path"], "r") as f:
                        history_data = json.load(f)

                    # Preparar los datos para la generación de código
                    history_for_code = {
                        "urls": [],
                        "action_names": [],
                        "detailed_actions": [],
                        "element_xpaths": {},
                        "extracted_content": [],
                        "errors": [],
                        "model_actions": history_data.get("model_actions", []),
                        "execution_date": datetime.now().strftime("%d/%m/%Y %H:%M:%S"),
                        "test_id": result["test_id"],
                        "screenshot_paths": result["screenshot_paths"]
                    }

                    # Usar el servicio para generar código
                    automation_code = test_service.generate_code(
                        framework=args.generate_code,
                        gherkin_scenario=gherkin_scenario,
                        test_history=history_for_code
                    )

                    # Guardar el código generado
                    output_file = f"smoke_test_{result['test_id']}_automation.py"
                    if args.generate_code == "cypress":
                        output_file = f"smoke_test_{result['test_id']}_automation.js"
                    elif args.generate_code == "java":
                        output_file = f"smoke_test_{result['test_id']}_automation.java"

                    with open(output_file, "w") as f:
                        f.write(automation_code)

                    print(f"Código de automatización generado y guardado en: {output_file}")

                except Exception as e:
                    print(f"Error al generar código: {str(e)}")
        else:
            print(f"Error al ejecutar el test: {result.get('error', 'Error desconocido')}")

    # Manejar el comando generate-code
    elif args.command == "generate-code":
        try:
            # Verificar que el archivo de historial existe
            if not os.path.exists(args.history_path):
                print(f"Error: El archivo de historial {args.history_path} no existe.")
                return

            # Cargar el historial
            with open(args.history_path, "r") as f:
                history_data = json.load(f)

            # Extraer el escenario Gherkin del historial si está disponible
            gherkin_scenario = ""
            if "gherkin_scenario" in history_data:
                gherkin_scenario = history_data["gherkin_scenario"]
            else:
                # Crear un escenario Gherkin básico si no está disponible
                gherkin_scenario = """
Feature: Test Automatizado

  Scenario: Ejecutar prueba
    Given el usuario navega a la página
    When el usuario realiza acciones
    Then la prueba se completa exitosamente
"""

            # Preparar los datos para la generación de código
            history_for_code = {
                "urls": [],
                "action_names": [],
                "detailed_actions": [],
                "element_xpaths": {},
                "extracted_content": [],
                "errors": [],
                "model_actions": history_data.get("model_actions", []),
                "execution_date": datetime.now().strftime("%d/%m/%Y %H:%M:%S"),
                "test_id": os.path.basename(os.path.dirname(args.history_path)),
                "screenshot_paths": []
            }

            # Inicializar el servicio de tests
            test_service = TestService(api_key=os.environ.get("GOOGLE_API_KEY"))
            
            # Usar el servicio para generar código
            automation_code = test_service.generate_code(
                framework=args.framework,
                gherkin_scenario=gherkin_scenario,
                test_history=history_for_code
            )

            # Determinar la ruta de salida
            output_file = args.output
            if not output_file:
                if args.framework == "cypress":
                    output_file = f"test_automation_{datetime.now().strftime('%Y%m%d%H%M%S')}.js"
                elif args.framework == "java":
                    output_file = f"test_automation_{datetime.now().strftime('%Y%m%d%H%M%S')}.java"
                else:
                    output_file = f"test_automation_{datetime.now().strftime('%Y%m%d%H%M%S')}.py"

            # Guardar el código generado
            with open(output_file, "w") as f:
                f.write(automation_code)

            print(f"Código de automatización generado y guardado en: {output_file}")

        except Exception as e:
            print(f"Error al generar código: {str(e)}")

    # Manejar el comando full test
    elif args.command == "full":
        print("Ejecutando Full Test...")
        
        # Obtener el escenario Gherkin
        gherkin_scenario = ""
        if args.gherkin_file:
            # Leer el escenario Gherkin desde un archivo
            try:
                with open(args.gherkin_file, "r") as f:
                    gherkin_scenario = f.read()
            except Exception as e:
                print(f"Error al leer el archivo Gherkin: {str(e)}")
                return
        else:
            # Usar el escenario Gherkin proporcionado directamente
            gherkin_scenario = args.gherkin
        
        # Inicializar el servicio de tests
        test_service = TestService(api_key=os.environ.get("GOOGLE_API_KEY"))
        
        # Ejecutar el test
        result = test_service.run_full_test(
            gherkin_scenario=gherkin_scenario,
            url=args.url
        )
        
        # Mostrar resultados
        if result["success"]:
            print(f"Test ejecutado exitosamente. ID: {result['test_id']}")
            
            # Generar código si se solicitó
            if args.generate_code:
                try:
                    # Preparar los datos para la generación de código
                    history_for_code = {
                        "urls": [],
                        "action_names": [],
                        "detailed_actions": result.get("detailed_actions", []),
                        "element_xpaths": result.get("element_xpaths", {}),
                        "extracted_content": result.get("extracted_content", []),
                        "errors": [],
                        "execution_date": datetime.now().strftime("%d/%m/%Y %H:%M:%S"),
                        "test_id": result["test_id"],
                        "screenshot_paths": result["screenshot_paths"]
                    }
                    
                    # Usar el servicio para generar código
                    automation_code = test_service.generate_code(
                        framework=args.generate_code,
                        gherkin_scenario=gherkin_scenario,
                        test_history=history_for_code
                    )
                    
                    # Guardar el código generado
                    output_file = f"full_test_{result['test_id']}_automation.py"
                    if args.generate_code == "cypress":
                        output_file = f"full_test_{result['test_id']}_automation.js"
                    elif args.generate_code == "java":
                        output_file = f"full_test_{result['test_id']}_automation.java"
                    
                    with open(output_file, "w") as f:
                        f.write(automation_code)
                    
                    print(f"Código de automatización generado y guardado en: {output_file}")
                    
                except Exception as e:
                    print(f"Error al generar código: {str(e)}")
        else:
            print(f"Error al ejecutar el test: {result.get('error', 'Error desconocido')}")
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
