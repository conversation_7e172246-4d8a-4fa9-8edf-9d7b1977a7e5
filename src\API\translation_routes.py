"""
API endpoint for translating prompts from Spanish to English
Maintains technical precision while enabling natural Spanish input
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Optional
import os
import logging
from langchain_google_genai import ChatGoogleGenerativeAI

logger = logging.getLogger(__name__)

router = APIRouter()

class TranslationRequest(BaseModel):
    text: str
    source: str = "es"
    target: str = "en"
    type: str = "prompt"  # prompt, general, technical

class TranslationResponse(BaseModel):
    translatedText: str
    sourceLanguage: str
    targetLanguage: str
    type: str

class PromptTranslationService:
    """Service for translating prompts with technical precision."""
    
    def __init__(self):
        """Initialize the translation service."""
        self.llm = ChatGoogleGenerativeAI(
            model=os.getenv("LLM_MODEL", "gemini-2.0-flash"),
            api_key=os.environ.get("GOOGLE_API_KEY"),
            temperature=0.0  # Consistent translations
        )
    
    def translate_prompt(self, text: str, source: str, target: str, prompt_type: str) -> str:
        """
        Translate prompt maintaining technical accuracy and structure.
        
        Args:
            text: Text to translate
            source: Source language code
            target: Target language code  
            prompt_type: Type of prompt (prompt, general, technical)
            
        Returns:
            Translated text
        """
        if source == target:
            return text
            
        translation_prompt = self._build_prompt_translation_prompt(
            text, source, target, prompt_type
        )
        
        try:
            response = self.llm.invoke(translation_prompt)
            return response.content.strip()
        except Exception as e:
            logger.error(f"Translation failed: {e}")
            raise HTTPException(status_code=500, detail=f"Translation failed: {str(e)}")
    
    def _build_prompt_translation_prompt(self, text: str, source: str, target: str, prompt_type: str) -> str:
        """Build specialized translation prompt for technical content."""
        
        language_names = {
            "es": "Spanish",
            "en": "English",
            "fr": "French",
            "de": "German"
        }
        
        source_name = language_names.get(source, source)
        target_name = language_names.get(target, target)
        
        if prompt_type == "prompt":
            instruction_type = "AI prompt instructions"
            special_instructions = """
CRITICAL REQUIREMENTS for AI Prompt Translation:
1. Preserve ALL technical terms (APIs, frameworks, file names, etc.)
2. Maintain the exact structure and formatting
3. Keep placeholders like {variable_name} unchanged
4. Preserve markdown formatting (##, -, *, etc.)
5. Maintain imperative tone for instructions
6. Keep technical specifications precise
7. Preserve any code examples or snippets exactly
8. Maintain the logical flow and step-by-step structure
"""
        else:
            instruction_type = "technical content"
            special_instructions = """
REQUIREMENTS:
1. Maintain technical accuracy
2. Preserve key terminology
3. Keep professional tone
4. Ensure clarity and precision
"""

        return f"""You are a professional technical translator specializing in AI and software development content.

Translate the following {instruction_type} from {source_name} to {target_name}.

{special_instructions}

Original {source_name} text:
{text}

Translated {target_name} text:"""

# Initialize the service
translation_service = PromptTranslationService()

@router.post("/translate-prompt", response_model=TranslationResponse)
async def translate_prompt(request: TranslationRequest):
    """
    Translate text maintaining technical precision.
    
    Especially useful for translating Spanish prompts to English
    while preserving technical accuracy and structure.
    """
    try:
        translated_text = translation_service.translate_prompt(
            request.text,
            request.source, 
            request.target,
            request.type
        )
        
        return TranslationResponse(
            translatedText=translated_text,
            sourceLanguage=request.source,
            targetLanguage=request.target,
            type=request.type
        )
        
    except Exception as e:
        logger.error(f"Translation endpoint error: {e}")
        raise HTTPException(
            status_code=500, 
            detail=f"Translation failed: {str(e)}"
        )
