"""
Utilidades para análisis de resultados de tests y gestión de archivos.
Separado de TestExecutor para mejorar la organización y mantenibilidad del código.
"""

import os
import json
import base64
import re
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple


class TestAnalyzer:
    """Utilidades estáticas para analizar resultados de tests."""

    @staticmethod
    def analyze_test_result(history, gherkin_scenario: str) -> Dict[str, Any]:
        """
        Analiza el resultado del test y determina si fue exitoso o falló.

        Args:
            history: Historial de la ejecución del test
            gherkin_scenario: <PERSON>sce<PERSON><PERSON> Gherkin ejecutado

        Returns:
            Dict con información detallada del resultado
        """
        try:
            # Obtener el resultado final del historial
            final_result = history.final_result()

            # Obtener errores del historial
            errors = history.errors() if hasattr(history, 'errors') else []

            # Obtener contenido extraído para análisis adicional
            extracted_content = history.extracted_content() if hasattr(history, 'extracted_content') else []

            # Contar errores no nulos
            error_count = len([error for error in errors if error is not None])

            # Determinar si el test fue exitoso basado en múltiples factores
            is_successful = TestAnalyzer.determine_test_success(final_result, errors, extracted_content)

            if is_successful:
                return {
                    "status": "completed",
                    "message": final_result if final_result else "Test completed successfully",
                    "success": True,
                    "error_count": error_count,
                    "details": {
                        "final_result": final_result,
                        "errors_encountered": error_count,
                        "execution_summary": "Test executed successfully with expected results"
                    }
                }
            else:
                # Test falló - proporcionar información detallada del fallo
                failure_reason = TestAnalyzer.determine_failure_reason(final_result, errors, extracted_content)

                return {
                    "status": "failed",
                    "message": failure_reason,
                    "success": False,
                    "error_count": error_count,
                    "details": {
                        "final_result": final_result,
                        "errors_encountered": error_count,
                        "failure_reason": failure_reason,
                        "execution_summary": "Test failed to meet expected criteria",
                        "errors": [error for error in errors if error is not None]
                    }
                }

        except Exception as e:
            return {
                "status": "error",
                "message": f"Error analyzing test result: {str(e)}",
                "success": False,
                "error_count": 0,
                "details": {
                    "final_result": None,
                    "errors_encountered": 0,
                    "failure_reason": f"Analysis error: {str(e)}",
                    "execution_summary": "Test result analysis failed"
                }
            }

    @staticmethod
    def determine_test_success(final_result, errors, extracted_content) -> bool:
        """
        Determina si el test fue exitoso basado en el resultado final, errores y contenido extraído.
        """
        # Si no hay resultado final, considerar como fallo
        if not final_result:
            return False

        # Convertir a string para análisis
        result_text = str(final_result).lower()

        # Palabras clave que indican éxito
        success_keywords = [
            "successful", "success", "completed", "found", "verified",
            "exitoso", "exitosa", "completado", "encontrado", "verificado",
            "passed", "correct", "valid", "working"
        ]

        # Palabras clave que indican fallo
        failure_keywords = [
            "failed", "error", "not found", "unable", "could not", "cannot",
            "falló", "error", "no encontrado", "no se pudo", "no puede",
            "timeout", "exception", "invalid", "incorrect", "broken"
        ]

        # Verificar palabras clave de éxito
        has_success_keywords = any(keyword in result_text for keyword in success_keywords)

        # Verificar palabras clave de fallo
        has_failure_keywords = any(keyword in result_text for keyword in failure_keywords)

        # Si hay palabras de éxito y no hay palabras de fallo, considerar exitoso
        if has_success_keywords and not has_failure_keywords:
            return True

        # Si hay palabras de fallo, considerar como fallo
        if has_failure_keywords:
            return False

        # Si el resultado es muy corto o genérico, verificar errores
        if len(result_text.strip()) < 10:
            # Si hay muchos errores, probablemente falló
            error_count = len([error for error in errors if error is not None])
            return error_count < 3  # Tolerancia de hasta 2 errores

        # Por defecto, si hay un resultado final descriptivo, considerar exitoso
        return len(result_text.strip()) > 20

    @staticmethod
    def determine_failure_reason(final_result, errors, extracted_content) -> str:
        """
        Determina la razón específica del fallo del test.
        """
        # Si hay un resultado final, usarlo como base
        if final_result:
            result_text = str(final_result)
            if len(result_text) > 10:
                return f"Test failed: {result_text}"

        # Analizar errores para encontrar la causa principal
        non_null_errors = [error for error in errors if error is not None]

        if not non_null_errors:
            return TestAnalyzer.handle_no_errors_case(final_result)

        error_types, element_index_errors = TestAnalyzer.categorize_errors(non_null_errors)
        most_common_error = max(error_types.items(), key=lambda x: x[1])

        return TestAnalyzer.format_error_message(most_common_error, element_index_errors, non_null_errors)

    @staticmethod
    def handle_no_errors_case(final_result) -> str:
        """Maneja el caso cuando no hay errores específicos."""
        if not final_result:
            return "Test failed: No final result was generated. The test may have been interrupted or the automation agent was unable to complete the scenario."
        return "Test failed for unknown reasons. Please check the execution logs for more details."

    @staticmethod
    def categorize_errors(non_null_errors) -> Tuple[Dict[str, int], List[str]]:
        """Categoriza los errores por tipo."""
        error_types = {}
        element_index_errors = []

        for error in non_null_errors:
            error_str = str(error).lower()
            error_type = TestAnalyzer.identify_error_type(error_str)
            error_types[error_type] = error_types.get(error_type, 0) + 1

            if error_type == "element_index_not_found":
                element_index_errors.append(error_str)

        return error_types, element_index_errors

    @staticmethod
    def identify_error_type(error_str: str) -> str:
        """Identifica el tipo de error basado en el mensaje."""
        if ("element with index" in error_str or "element index" in error_str) and "does not exist" in error_str:
            return "element_index_not_found"
        elif "timeout" in error_str:
            return "timeout"
        elif "not found" in error_str or "element" in error_str:
            return "element_not_found"
        elif "network" in error_str or "connection" in error_str:
            return "network"
        elif "max_failures" in error_str or "maximum failures" in error_str:
            return "max_failures_reached"
        else:
            return "general"

    @staticmethod
    def format_error_message(most_common_error: Tuple[str, int], element_index_errors: List[str], non_null_errors: List) -> str:
        """Formatea el mensaje de error basado en el tipo más común."""
        error_type, count = most_common_error

        if error_type == "element_index_not_found":
            return TestAnalyzer.format_element_index_error(element_index_errors, count)
        elif error_type == "max_failures_reached":
            return f"Test failed because the browser agent reached the maximum number of consecutive failures ({count} occurrences). This often happens when the agent cannot find the expected elements or perform required actions. Check if the page structure matches expectations or if additional waiting time is needed."
        elif error_type == "timeout":
            return f"Test failed due to timeout errors ({count} occurrences). The page or elements took too long to load."
        elif error_type == "element_not_found":
            return f"Test failed because required elements were not found ({count} occurrences). The page structure may have changed."
        elif error_type == "network":
            return f"Test failed due to network/connection issues ({count} occurrences)."
        else:
            return f"Test failed with {len(non_null_errors)} errors. Last error: {non_null_errors[-1]}"

    @staticmethod
    def format_element_index_error(element_index_errors: List[str], count: int) -> str:
        """Formatea el mensaje de error específico para errores de índice de elementos."""
        unique_indexes = set()
        for error in element_index_errors:
            match = re.search(r'index (\d+)', error)
            if match:
                unique_indexes.add(match.group(1))

        index_list = ", ".join(sorted(unique_indexes))
        return f"Test failed due to element detection issues. Elements with indexes [{index_list}] were not found on the page ({count} occurrences). This usually indicates that the page structure has changed, elements loaded dynamically after detection, or the page layout is different than expected. Consider checking if the page loaded completely or if authentication is required."


class TestFileManager:
    """Utilidades estáticas para gestión de archivos de tests."""

    @staticmethod
    def save_screenshots_to_files(screenshots: List[str], test_id: Optional[str] = None, test_type: str = "test") -> List[str]:
        """
        Guarda las capturas de pantalla como archivos separados y devuelve una lista de rutas.

        Args:
            screenshots: Lista de capturas de pantalla en formato base64
            test_id: Identificador único para la prueba (opcional)
            test_type: Tipo de prueba (test, smoke_test, etc.)

        Returns:
            List[str]: Lista de rutas a los archivos de capturas de pantalla
        """
        if not test_id:
            test_id = datetime.now().strftime("%Y%m%d%H%M%S")

        # Crear directorio para las capturas de pantalla
        tests_dir = os.path.join("tests")
        os.makedirs(tests_dir, exist_ok=True)

        test_dir = os.path.join(tests_dir, f"{test_type}_{test_id}")
        os.makedirs(test_dir, exist_ok=True)

        screenshots_dir = os.path.join(test_dir, "screenshots")
        os.makedirs(screenshots_dir, exist_ok=True)

        screenshot_paths = []
        for i, screenshot in enumerate(screenshots):
            if isinstance(screenshot, str) and screenshot.startswith("data:image"):
                # Extraer datos base64 de la URL de datos
                try:
                    # Formato típico: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...
                    image_data = screenshot.split(",")[1]
                    image_bytes = base64.b64decode(image_data)

                    # Guardar imagen
                    screenshot_path = os.path.join(screenshots_dir, f"screenshot_{i+1}.png")
                    with open(screenshot_path, "wb") as f:
                        f.write(image_bytes)

                    # Guardar ruta relativa
                    screenshot_paths.append(os.path.relpath(screenshot_path))
                except Exception as e:
                    print(f"Error al guardar captura de pantalla {i+1}: {str(e)}")
                    continue

        return screenshot_paths

    @staticmethod
    def save_history_json(history: Any, test_id: str, test_type: str = "test", screenshot_paths: Optional[List[str]] = None, current_gherkin_scenario: Optional[str] = None) -> Optional[str]:
        """
        Guarda el historial de prueba en un archivo JSON y modifica las referencias a capturas de pantalla.

        Args:
            history: Objeto de historial de la prueba
            test_id: Identificador único para la prueba
            test_type: Tipo de prueba (test, smoke_test, etc.)
            screenshot_paths: Lista de rutas a los archivos de capturas de pantalla
            current_gherkin_scenario: Escenario Gherkin actual para guardar

        Returns:
            str: Ruta al archivo JSON guardado
        """
        try:
            # Crear directorio principal de tests si no existe
            tests_dir = os.path.join("tests")
            os.makedirs(tests_dir, exist_ok=True)

            # Crear directorio específico para este test
            test_dir = os.path.join(tests_dir, f"{test_type}_{test_id}")
            os.makedirs(test_dir, exist_ok=True)

            # Definir la ruta del archivo JSON
            history_json_path = os.path.join(test_dir, "history.json")

            # **NUEVA SOLUCIÓN**: Crear datos limpios antes de la serialización
            try:
                # Intentar crear una representación limpia del historial
                clean_history_data = TestFileManager._create_clean_history_data(history)
                
                # Guardar datos limpios directamente
                with open(history_json_path, "w") as f:
                    json.dump(clean_history_data, f, indent=2, default=TestFileManager._json_serializer)
                    
            except Exception as clean_error:
                print(f"⚠️ Error al crear datos limpios, usando método fallback: {str(clean_error)}")
                # Fallback: intentar el método original con mejor manejo de errores
                try:
                    history.save_to_file(history_json_path)
                except Exception as save_error:
                    print(f"❌ Error en save_to_file: {str(save_error)}")
                    # Último fallback: crear JSON básico manualmente
                    basic_data = {
                        "error": "Could not serialize history due to non-serializable objects",
                        "error_details": str(save_error),
                        "test_id": test_id,
                        "test_type": test_type,
                        "timestamp": datetime.now().isoformat()
                    }
                    with open(history_json_path, "w") as f:
                        json.dump(basic_data, f, indent=2)
                    print(f"💾 Guardado JSON básico como fallback en: {history_json_path}")
                    return history_json_path

            # **PROCESAMIENTO POST-GUARDADO**: Modificar el JSON si es necesario
            # Si hay capturas de pantalla, modificar el JSON para usar referencias a archivos
            if screenshot_paths:
                try:
                    # Leer el archivo JSON recién guardado
                    with open(history_json_path, "r") as f:
                        history_data = json.load(f)

                    # Guardar el escenario Gherkin en el historial para referencia futura
                    if current_gherkin_scenario:
                        history_data["gherkin_scenario"] = current_gherkin_scenario

                    # Contador para las capturas de pantalla
                    screenshot_index = 0

                    # Recorrer las acciones del modelo y reemplazar las capturas de pantalla
                    for action in history_data.get("model_actions", []):
                        if "screenshot" in action and screenshot_index < len(screenshot_paths):
                            # Reemplazar la captura de pantalla con la ruta al archivo
                            action["screenshot_path"] = screenshot_paths[screenshot_index]
                            # Eliminar completamente la captura base64 para reducir el tamaño del JSON
                            action.pop("screenshot", None)
                            screenshot_index += 1

                    # Eliminar también las capturas base64 del estado si existen
                    if "state" in history_data:
                        if "screenshot" in history_data["state"]:
                            history_data["state"].pop("screenshot", None)

                    # Guardar el archivo JSON modificado con el serializer personalizado
                    with open(history_json_path, "w") as f:
                        json.dump(history_data, f, indent=2, default=TestFileManager._json_serializer)
                        
                except Exception as post_process_error:
                    print(f"⚠️ Error al post-procesar JSON (capturas): {str(post_process_error)}")
                    # Continuar aunque falle el post-procesamiento
            else:
                # Agregar escenario Gherkin incluso sin capturas
                if current_gherkin_scenario:
                    try:
                        with open(history_json_path, "r") as f:
                            history_data = json.load(f)
                        history_data["gherkin_scenario"] = current_gherkin_scenario
                        with open(history_json_path, "w") as f:
                            json.dump(history_data, f, indent=2, default=TestFileManager._json_serializer)
                    except Exception as gherkin_error:
                        print(f"⚠️ Error al agregar escenario Gherkin: {str(gherkin_error)}")

            print(f"✅ Historial guardado exitosamente en: {history_json_path}")
            return history_json_path

        except Exception as e:
            print(f"❌ Error crítico al guardar el historial de prueba: {str(e)}")
            return None

    @staticmethod
    def _create_clean_history_data(history) -> Dict[str, Any]:
        """
        Crea una representación limpia del historial que es serializable a JSON.
        Maneja específicamente objetos DOMHistoryElement y otros objetos problemáticos.
        """
        clean_data = {
            "test_id": getattr(history, 'test_id', None),
            "timestamp": datetime.now().isoformat(),
            "history": [],
            "metadata": {}
        }
        
        try:
            # Extraer historial paso a paso
            if hasattr(history, 'history') and history.history:
                for step in history.history:
                    clean_step = TestFileManager._clean_step_data(step)
                    clean_data["history"].append(clean_step)
            
            # Extraer otros datos útiles del historial
            if hasattr(history, 'final_result'):
                try:
                    clean_data["final_result"] = TestFileManager._clean_any_value(history.final_result())
                except:
                    clean_data["final_result"] = None
                    
            if hasattr(history, 'urls'):
                try:
                    clean_data["urls"] = [str(url) for url in history.urls()]
                except:
                    clean_data["urls"] = []
                    
            if hasattr(history, 'errors'):
                try:
                    clean_data["errors"] = [TestFileManager._clean_any_value(error) for error in history.errors()]
                except:
                    clean_data["errors"] = []
                    
            if hasattr(history, 'model_actions'):
                try:
                    clean_data["model_actions"] = [TestFileManager._clean_any_value(action) for action in history.model_actions()]
                except:
                    clean_data["model_actions"] = []
                    
        except Exception as e:
            print(f"⚠️ Error al procesar historial: {str(e)}")
            clean_data["processing_error"] = str(e)
        
        return clean_data
    
    @staticmethod
    def _clean_step_data(step) -> Dict[str, Any]:
        """
        Limpia los datos de un paso del historial para que sean serializables.
        """
        clean_step = {}
        
        try:
            if isinstance(step, dict):
                for key, value in step.items():
                    clean_step[key] = TestFileManager._clean_any_value(value)
            else:
                # Si no es un diccionario, intentar extraer atributos útiles
                if hasattr(step, '__dict__'):
                    for key, value in step.__dict__.items():
                        clean_step[key] = TestFileManager._clean_any_value(value)
                else:
                    clean_step = {"raw_value": str(step)}
        except Exception as e:
            clean_step = {"error": f"Could not process step: {str(e)}"}
            
        return clean_step
    
    @staticmethod
    def _clean_any_value(value) -> Any:
        """
        Limpia cualquier valor para que sea serializable a JSON.
        Maneja específicamente objetos DOMHistoryElement y otros tipos problemáticos.
        """
        if value is None:
            return None
        elif isinstance(value, (str, int, float, bool)):
            return value
        elif isinstance(value, (list, tuple)):
            return [TestFileManager._clean_any_value(item) for item in value]
        elif isinstance(value, dict):
            return {key: TestFileManager._clean_any_value(val) for key, val in value.items()}
        elif hasattr(value, '__class__') and 'DOMHistoryElement' in str(value.__class__):
            # Manejar específicamente objetos DOMHistoryElement
            return TestFileManager._clean_dom_element(value)
        elif hasattr(value, '__dict__'):
            # Para objetos con atributos, convertir a diccionario limpio
            try:
                return {key: TestFileManager._clean_any_value(val) for key, val in value.__dict__.items()}
            except:
                return {"string_representation": str(value), "type": str(type(value))}
        else:
            # Para cualquier otro tipo, convertir a string
            return str(value)
    
    @staticmethod
    def _clean_dom_element(element) -> Dict[str, Any]:
        """
        Extrae información útil de un objeto DOMHistoryElement de manera segura.
        """
        try:
            element_data = {
                "type": "DOMHistoryElement",
                "string_representation": str(element)
            }
            
            # Intentar extraer información específica del elemento
            try:
                element_str = str(element)
                
                # Buscar patrones comunes en la representación string
                xpath_match = re.search(r"xpath=['\"]([^'\"]+)['\"]", element_str)
                if xpath_match:
                    element_data["xpath"] = xpath_match.group(1)
                
                tag_match = re.search(r"tag=['\"]([^'\"]+)['\"]", element_str)
                if tag_match:
                    element_data["tag"] = tag_match.group(1)
                
                # Intentar extraer atributos si están disponibles
                if hasattr(element, 'tag_name'):
                    element_data["tag_name"] = str(element.tag_name)
                if hasattr(element, 'xpath'):
                    element_data["xpath"] = str(element.xpath)
                if hasattr(element, 'attributes'):
                    element_data["attributes"] = TestFileManager._clean_any_value(element.attributes)
                    
            except Exception as extract_error:
                element_data["extraction_error"] = str(extract_error)
            
            return element_data
            
        except Exception as e:
            return {
                "type": "DOMHistoryElement", 
                "error": f"Could not process DOMHistoryElement: {str(e)}"
            }
    
    @staticmethod
    def _json_serializer(obj) -> str:
        """
        Serializer personalizado para objetos que no son nativamente serializables a JSON.
        """
        if hasattr(obj, '__class__') and 'DOMHistoryElement' in str(obj.__class__):
            return TestFileManager._clean_dom_element(obj)
        elif hasattr(obj, '__dict__'):
            return {key: TestFileManager._json_serializer(val) if not isinstance(val, (str, int, float, bool, type(None))) else val 
                   for key, val in obj.__dict__.items()}
        else:
            return str(obj)
