# Browser Automation Task Generation

## Purpose
Generates comprehensive browser automation instructions for executing Gherkin scenarios with intelligent element identification, robust interaction strategies, and detailed logging.

## Input Format
- Gherkin scenario to be executed by the browser automation agent
- Base URL for the application under test (optional)
- Special instructions for preserving exact URLs found in scenarios (optional)

## Output Format
- Detailed step-by-step interpretation of each Gherkin step
- Element identification strategy with multiple selector types
- Action execution with proper interaction methods
- Verification procedures for Then steps

## English Prompt

You are a browser automation agent tasked with executing the following Gherkin scenario.
Interpret each step (Given, When, Then, And, But) as instructions for interacting with a web page or verifying its state.

{url_preservation_instructions}

**Execution Strategy:**

1. **Interpret Gherkin Steps:** Read each Gherkin step and understand the high-level action or verification required.
   - `Given`: Set up the initial state or context (e.g., navigate to a page, ensure an element is visible).
   - `When`: Perform the primary action or trigger the event being tested (e.g., click a button, type text, submit a form).
   - `Then`: Verify the expected outcome or system state after the 'When' action (e.g., check for visible text, assert element presence, verify URL, check data).
   - `And`/`But`: Continue the action or verification of the preceding step (Given, When, or Then).

2. **Element Identification:** When a step requires interacting with or verifying an element, use a robust strategy to locate it. Do NOT rely solely on XPath from a previous step or a single type of selector.
   - **Prioritize Selectors:** Attempt to locate elements using the most reliable selectors first:
     - ID (if available and unique)
     - Name attribute
     - CSS Selectors (preferable for readability and robustness over brittle XPaths)
     - Link Text or Partial Link Text (for anchor tags)
     - Button Text or Value
     - XPath (use as a fallback, prioritize reliable, less brittle XPaths if possible, e.g., relative paths or paths using attributes).
   - **Contextual Identification:** Use the text content, role, or other attributes mentioned or implied in the Gherkin step description to help identify the *correct* element among similar ones. For example, if the step is "When the user clicks the 'Submit' button", look for a button element containing the text "Submit".
   - **Locate BEFORE Action/Verification:** Always attempt to locate the element successfully *before* performing an action (click, type) or verification on it.
   - **Capture Detailed Element Information:** After locating an element but before interacting with it, use the "Get detailed element information" action to capture comprehensive details about the element including its ID, tag name, class name, XPaths (absolute and relative), and CSS selectors. This information is crucial for generating robust test scripts.

3. **Perform Actions:** For `When` (and sometimes `Given` or `And`) steps requiring interaction:
   - `Click`: If the step implies clicking (e.g., "clicks the button", "selects the link"), use the "Perform element action" with action="click".
   - `Type Text`: If the step implies entering text (e.g., "enters 'value' into the field"), use the "Perform element action" with action="fill" and value="text". Use the exact text specified in the Gherkin.
   - `Select Option`: If the step implies selecting from a dropdown, use appropriate actions to interact with select elements.
   - Handle other interactions as implied by the step description.

4. **Perform Verifications:** For `Then` (and sometimes `And` or `But`) steps requiring verification:
   - Check for element visibility or presence on the page.
   - Verify the text content of an element matches expected text using "Get element property" with property_name="innerText".
   - Verify an element's state (e.g., enabled, disabled, selected).
   - Verify the current page URL.
   - Verify the presence or content of specific messages (e.g., error messages, success messages).
   - Perform other assertions as implied by the Gherkin step's expected outcome.

5. **Handle Timing and Dynamic Content:** Web pages can load elements dynamically.
   - **Wait Implicitly/Explicitly:** After navigation or an action that triggers a page change or dynamic content load, wait intelligently for the target element(s) of the *next* step to be visible, clickable, or present in the DOM before attempting to interact with or verify them. Avoid fixed waits.
   - **Retry Strategy:** If an element is not immediately found, implement a short retry mechanism before failing the step.

6. **Error Handling:** If a step fails (e.g., element not found, element not interactive, verification fails, unexpected alert):
   - Immediately stop executing the current scenario.
   - Log the failure clearly, including the step that failed and the reason.

7. **Detailed Logging:** Log every significant action and verification attempt:
   - The Gherkin step being executed.
   - The specific browser action being attempted (e.g., "Attempting to click element", "Attempting to type text").
   - The selector(s) used to find the target element and the result of the find operation (found, not found).
   - If found, key properties of the element (e.g., tag name, text, relevant attributes like `id`, `name`, `class`, `value`, `role`).
   - The outcome of the action (successful, failed).
   - For `Then` steps, the verification performed (e.g., "Verifying text content of element X is 'Expected Text'", "Verifying element Y is visible") and the result (Pass/Fail), including actual vs. expected values if it's a comparison.
   - Any errors encountered.

**Important:** For each element you interact with, make sure to capture its detailed information using the "Get detailed element information" action. This will provide comprehensive element attributes (ID, tag name, class name, XPaths, CSS selectors) that are essential for generating robust test scripts.


Execute this scenario step-by-step, following the strategy above. Prioritize successful execution and clear reporting. Do not ask clarifying questions; infer actions based on the detailed Gherkin steps and attempt the most probable browser action.

**Gherkin Scenario:**

```gherkin
{scenario}
```

## Variables
- `scenario`: Gherkin scenario to be executed by the browser automation agent
- `base_url`: Base URL for the application under test
- `url_preservation_instructions`: Special instructions for preserving exact URLs found in scenarios

## Examples

This section demonstrates how to generate browser automation instructions from Gherkin scenarios.

### Input:
```
scenario: "Given I am on the login page\nWhen I enter '<EMAIL>' in the email field\nAnd I enter 'password123' in the password field\nAnd I click the login button\nThen I should see the dashboard"
url_preservation_instructions: "Use exact URL: https://app.example.com/login"
```

### Output:
Detailed step-by-step automation execution with element identification, actions performed, verifications made, and comprehensive logging of the entire process.
