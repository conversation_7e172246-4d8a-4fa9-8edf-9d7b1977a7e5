"""API web para exponer servicios de pruebas y automatización."""

import os
from dotenv import load_dotenv

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles

# Importar todas las rutas modulares
from src.API.project_routes import router as project_router
from src.API.suite_routes import router as suite_router
from src.API.testcase_routes import router as testcase_router
from src.API.prompt_routes import router as prompt_router
from src.API.config_routes import router as config_router
from src.API.translation_routes import router as translation_router
from src.API.test_execution_routes import router as test_execution_router
from src.API.generation_routes import router as generation_router
from src.API.story_routes import router as story_router
from src.API.history_routes import router as history_router
from src.API.health_routes import router as health_router
from src.API.codegen_routes import router as codegen_router
from src.API.codegen_execution_routes import router as codegen_execution_router
from src.API.pr_analysis_routes import router as pr_analysis_router

# Cargar variables de entorno
load_dotenv()

# Verificar que la API key esté configurada
if not os.environ.get("GOOGLE_API_KEY"):
    raise ValueError("No se encontró la API key de Google Gemini. Por favor, configura la variable de entorno GOOGLE_API_KEY.")

# Crear la aplicación FastAPI
app = FastAPI(
    title="QAK API",
    description="API para automatización de pruebas, generación de casos de prueba y gestión de proyectos.",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configurar CORS para permitir solicitudes desde cualquier origen (especialmente Vercel)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Permite todos los orígenes para Vercel
    allow_credentials=False,  # Debe ser False con allow_origins=["*"]
    allow_methods=["*"],  # Permite todos los métodos HTTP
    allow_headers=["*"],  # Permite todos los headers
    expose_headers=["*"],  # Expone todos los headers
    max_age=86400,  # Cache preflight requests for 24 hours
)

# Incluir todas las rutas modulares
app.include_router(project_router)
app.include_router(suite_router)
app.include_router(testcase_router)
app.include_router(prompt_router)
app.include_router(config_router)
app.include_router(translation_router)
app.include_router(test_execution_router)
app.include_router(generation_router)
app.include_router(story_router)
app.include_router(history_router)
app.include_router(health_router)
app.include_router(codegen_router)
app.include_router(codegen_execution_router)
app.include_router(health_router)
app.include_router(pr_analysis_router)

# Montar archivos estáticos para capturas de pantalla
# Crear directorio de tests si no existe
tests_dir = "tests"
if not os.path.exists(tests_dir):
    os.makedirs(tests_dir, exist_ok=True)

# Crear directorio de codegen screenshots si no existe
import tempfile
codegen_screenshots_dir = os.path.join(tempfile.gettempdir(), "qak_codegen", "screenshots")
if not os.path.exists(codegen_screenshots_dir):
    os.makedirs(codegen_screenshots_dir, exist_ok=True)

# Montar el directorio de tests para servir capturas de pantalla
app.mount("/api/screenshots", StaticFiles(directory=tests_dir), name="screenshots")

# Montar el directorio de screenshots de codegen
app.mount("/api/codegen-screenshots", StaticFiles(directory=codegen_screenshots_dir), name="codegen-screenshots")


# Punto de entrada para ejecutar la aplicación
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("api:app", host="0.0.0.0", port=8000, reload=True)
