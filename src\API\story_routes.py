"""Rutas de API para gestión de historias de usuario."""

import json
import re
from fastapi import APIRouter, HTTPException, Depends

from src.API.models import (
    EnhanceStoryRequest,
    GenerateManualTestsRequest,
    GenerateGherkinRequest,
    SaveHistoryRequest
)
from src.Core.prompt_service import PromptService
from src.Core.test_service import TestService
import os

# Router para historias de usuario
router = APIRouter(prefix="/api/stories", tags=["user-stories"])


def get_prompt_service():
    """Crea y devuelve una instancia del servicio de prompts."""
    return PromptService()


def get_test_service():
    """Crea y devuelve una instancia del servicio de pruebas."""
    return TestService(api_key=os.environ.get("GOOGLE_API_KEY"))


@router.post("/enhance", summary="Mejorar historia de usuario")
async def enhance_story(
    request: EnhanceStoryRequest,
    prompt_service: PromptService = Depends(get_prompt_service)
):
    """Mejora una historia de usuario."""
    try:
        from src.Utilities.response_cleaner import clean_user_story_response

        # Use the new PromptService for user story enhancement
        enhanced_story = prompt_service.enhance_user_story(
            user_story=request.user_story,
            language=request.language
        )

        # Clean the response
        clean_story = clean_user_story_response(enhanced_story)

        return {"enhanced_story": clean_story}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/generate-manual-tests", summary="Generar casos de prueba manuales")
async def generate_manual_tests(
    request: GenerateManualTestsRequest,
    prompt_service: PromptService = Depends(get_prompt_service)
):
    """Genera casos de prueba manuales a partir de una historia mejorada."""
    try:
        # Use the new PromptService for manual test generation
        manual_tests = prompt_service.generate_manual_test_cases(
            enhanced_story=request.enhanced_story,
            language=request.language
        )

        # First, try to extract JSON if it's wrapped in code blocks
        json_match = re.search(r'```json\s*(.*?)\s*```', manual_tests, re.DOTALL)
        if json_match:
            json_content = json_match.group(1)
            try:
                test_cases_json = json.loads(json_content)
                return {"manual_tests": test_cases_json}
            except json.JSONDecodeError as e:
                # If JSON parsing fails, return the error details for debugging
                return {"manual_tests": json_content, "error": f"JSON parsing error: {str(e)}"}
        
        # If no JSON code block found, try to parse the whole response as JSON
        try:
            test_cases_json = json.loads(manual_tests)
            return {"manual_tests": test_cases_json}
        except json.JSONDecodeError:
            # If all JSON parsing fails, return as string
            return {"manual_tests": manual_tests}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/generate-gherkin", summary="Generar escenarios Gherkin desde casos manuales")
async def generate_gherkin_from_manual(
    request: GenerateGherkinRequest,
    prompt_service: PromptService = Depends(get_prompt_service)
):
    """Genera escenarios Gherkin a partir de casos de prueba manuales."""
    try:
        from src.Utilities.response_cleaner import clean_gherkin_response

        # Use the new PromptService for Gherkin generation
        gherkin = prompt_service.generate_gherkin(
            test_cases=request.manual_tests,
            language=request.language
        )

        # Clean the response
        clean_gherkin = clean_gherkin_response(gherkin)

        return {"gherkin": clean_gherkin}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
