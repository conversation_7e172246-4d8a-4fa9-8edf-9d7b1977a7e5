"""Rutas de API para ejecutar tests generados por CodeGen usando browser-use."""

from fastapi import APIRouter, HTTPException
from typing import Dict, Any, Optional, List
from pydantic import BaseModel

from src.Core.service_container import get_executor_service

router = APIRouter(prefix="/api/codegen", tags=["codegen-execution"])

class CodegenExecutionRequest(BaseModel):
    """Request para ejecutar un test de CodeGen."""
    session_id: str
    config_id: Optional[str] = None
    configuration: Optional[Dict[str, Any]] = None

class CodegenExecutionResponse(BaseModel):
    """Response de ejecución de test."""
    execution_id: str
    status: str
    message: str

@router.post("/execute", operation_id="execute_codegen_test")
async def execute_codegen_test(request: CodegenExecutionRequest) -> CodegenExecutionResponse:
    """Ejecuta un test generado por CodeGen usando browser-use."""
    
    try:
        result = await get_executor_service().execute_codegen_test(
            session_id=request.session_id,
            config_id=request.config_id,
            configuration=request.configuration
        )
        
        return CodegenExecutionResponse(**result)
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error ejecutando test: {str(e)}")

@router.get("/execution/{execution_id}", operation_id="get_codegen_execution")
async def get_codegen_execution(execution_id: str) -> Dict[str, Any]:
    """Obtiene información de una ejecución de test."""
    
    execution = get_executor_service().get_execution(execution_id)
    if not execution:
        raise HTTPException(status_code=404, detail="Ejecución no encontrada")
    
    # Convertir datetime a string para JSON
    result = execution.copy()
    if "created_at" in result:
        result["created_at"] = result["created_at"].isoformat()
    if "updated_at" in result:
        result["updated_at"] = result["updated_at"].isoformat()
    if "completed_at" in result:
        result["completed_at"] = result["completed_at"].isoformat()
    
    return result

@router.get("/executions", operation_id="list_codegen_executions")
async def list_codegen_executions() -> Dict[str, Any]:
    """Lista todas las ejecuciones de tests."""
    
    executions = get_executor_service().list_executions()
    
    return {
        "total_executions": len(executions),
        "executions": executions
    }

@router.post("/execution/{execution_id}/stop", operation_id="stop_codegen_execution")
async def stop_codegen_execution(execution_id: str) -> Dict[str, str]:
    """Detiene una ejecución de test activa."""
    
    success = await get_executor_service().stop_execution(execution_id)
    if not success:
        raise HTTPException(status_code=404, detail="Ejecución no encontrada o ya detenida")
    
    return {"message": "Ejecución detenida exitosamente", "execution_id": execution_id}

@router.delete("/execution/{execution_id}", operation_id="cleanup_codegen_execution")
async def cleanup_codegen_execution(execution_id: str) -> Dict[str, str]:
    """Limpia los recursos de una ejecución."""

    execution = get_executor_service().get_execution(execution_id)
    if not execution:
        raise HTTPException(status_code=404, detail="Ejecución no encontrada")

    # Remover de ejecuciones activas
    if execution_id in get_executor_service().active_executions:
        del get_executor_service().active_executions[execution_id]

    return {"message": "Ejecución limpiada exitosamente", "execution_id": execution_id}

class TestExecutionFromJsonRequest(BaseModel):
    """Request para crear ejecución de prueba desde JSON."""
    session_id: Optional[str] = None

@router.post("/test-execution-from-json", operation_id="create_test_execution_from_json")
async def create_test_execution_from_json(request: Optional[TestExecutionFromJsonRequest] = None) -> Dict[str, str]:
    """Crea una ejecución de prueba desde el archivo JSON existente."""

    try:
        json_file_path = "codegen_sessions/codegen-execution-d603cf20-c24c-4ada-b13b-bf4b170646db.json"
        session_id = request.session_id if request else None
        execution_id = get_executor_service().create_test_execution_from_json(json_file_path, session_id)

        return {
            "message": "Ejecución de prueba creada exitosamente",
            "execution_id": execution_id
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creando ejecución de prueba: {str(e)}")

@router.post("/execution/{execution_id}/update-screenshots", operation_id="update_execution_screenshots")
async def update_execution_screenshots(execution_id: str) -> Dict[str, str]:
    """Actualiza una ejecución con screenshots del archivo JSON."""

    try:
        json_file_path = "codegen_sessions/codegen-execution-d603cf20-c24c-4ada-b13b-bf4b170646db.json"
        get_executor_service().update_execution_with_screenshots_from_json(execution_id, json_file_path)

        return {
            "message": "Screenshots actualizados exitosamente",
            "execution_id": execution_id
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error actualizando screenshots: {str(e)}")

@router.get("/execution/{execution_id}/screenshots-view", operation_id="view_execution_screenshots")
async def view_execution_screenshots(execution_id: str):
    """Muestra los screenshots de una ejecución en formato HTML."""

    execution = get_executor_service().get_execution(execution_id)
    if not execution:
        raise HTTPException(status_code=404, detail="Ejecución no encontrada")

    screenshots = execution.get("screenshots", [])

    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Screenshots - Execution {execution_id}</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            .screenshot {{ margin: 20px 0; border: 1px solid #ddd; padding: 10px; }}
            .screenshot img {{ max-width: 100%; height: auto; }}
            .screenshot h3 {{ margin-top: 0; }}
        </style>
    </head>
    <body>
        <h1>Screenshots for Execution {execution_id}</h1>
        <p>Total screenshots: {len(screenshots)}</p>
        <p>Session ID: {execution.get('session_id', 'N/A')}</p>
        <p>Status: {execution.get('status', 'N/A')}</p>

        {"".join([f'''
        <div class="screenshot">
            <h3>Screenshot {i+1}</h3>
            <img src="{screenshot}" alt="Screenshot {i+1}" />
        </div>
        ''' for i, screenshot in enumerate(screenshots)])}
    </body>
    </html>
    """

    from fastapi.responses import HTMLResponse
    return HTMLResponse(content=html_content)


