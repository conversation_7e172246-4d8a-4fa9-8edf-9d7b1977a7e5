"""Servicio para gestión de sesiones de Playwright Codegen."""

import os
import asyncio
import tempfile
import shutil
import subprocess
import json
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import logging
from pathlib import Path

from src.API.models import (
    PlaywrightCodegenRequest,
    CodegenSessionInfo,
    CodegenTestCaseRequest,
    CodegenStatsResponse
)
from src.Utilities.project_manager_service import ProjectManagerService

logger = logging.getLogger(__name__)

class PlaywrightCodegenService:
    """Servicio para gestionar sesiones de Playwright Codegen."""
    
    def __init__(self):
        """Inicializa el servicio de Playwright Codegen."""
        self.active_sessions: Dict[str, CodegenSessionInfo] = {}
        self.session_processes: Dict[str, asyncio.subprocess.Process] = {}
        
        # Directorio base para archivos temporales
        self.base_temp_dir = os.path.join(tempfile.gettempdir(), "qak_codegen")
        os.makedirs(self.base_temp_dir, exist_ok=True)
        
        # Directorio para persistencia de sesiones (usando ruta absoluta)
        root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        self.sessions_dir = os.path.join(root_dir, "codegen_sessions")
        os.makedirs(self.sessions_dir, exist_ok=True)
        
        # Archivos de persistencia
        self.sessions_file = os.path.join(self.sessions_dir, "sessions_history.json")
        self.completed_sessions: Dict[str, Dict[str, Any]] = {}
        
        # Cargar sesiones persistidas
        self._load_sessions_history()
        
        # Cleanup automático de sesiones antiguas
        asyncio.create_task(self._cleanup_old_sessions())
    
    async def start_session(self, request: PlaywrightCodegenRequest) -> CodegenSessionInfo:
        """Inicia una nueva sesión de Playwright Codegen."""

        try:
            # Verificar que Playwright esté disponible
            await self._verify_playwright_installation()

            # Generar ID único
            session_id = str(uuid.uuid4())

            # Crear directorio para la sesión
            session_dir = os.path.join(self.base_temp_dir, session_id)
            os.makedirs(session_dir, exist_ok=True)

            # Construir comando
            cmd = self._build_command(request, session_dir)

            # Crear información de sesión
            session_info = CodegenSessionInfo(
                session_id=session_id,
                status="starting",
                target_language=request.target_language,
                url=request.url,
                created_at=datetime.now(),
                updated_at=datetime.now(),
                artifacts_path=session_dir,
                command_used=" ".join(cmd),
                project_integration=self._build_project_integration(request)
            )

            # Almacenar sesión
            self.active_sessions[session_id] = session_info

            # Ejecutar comando en background
            asyncio.create_task(self._run_session(session_id, cmd, session_dir, request.headless))

            logger.info(f"Sesión de Playwright Codegen iniciada: {session_id}")
            return session_info

        except Exception as e:
            logger.error(f"Error iniciando sesión de codegen: {str(e)}")
            raise
    
    async def get_session(self, session_id: str) -> Optional[CodegenSessionInfo]:
        """Obtiene información de una sesión."""
        # Buscar en sesiones activas primero
        session = self.active_sessions.get(session_id)
        if session:
            return session
        
        # Si no está en activas, buscar en sesiones completadas
        session_data = self.completed_sessions.get(session_id)
        if session_data:
            return CodegenSessionInfo(**session_data)
        
        # Buscar en historial persistido
        session_data = self.get_session_from_history(session_id)
        if session_data:
            return CodegenSessionInfo(**session_data)
        
        return None
    
    async def stop_session(self, session_id: str) -> bool:
        """Detiene una sesión activa."""
        
        logger.debug(f"Intentando detener sesión: {session_id}")
        
        if session_id not in self.active_sessions:
            logger.warning(f"Sesión {session_id} no encontrada en active_sessions")
            return False
        
        try:
            # Actualizar estado primero para evitar race conditions
            logger.debug(f"Actualizando estado de sesión {session_id} a 'stopping'")
            session = self.active_sessions[session_id]
            session.status = "stopping"
            session.updated_at = datetime.now()
            
            # Terminar proceso si existe
            if session_id in self.session_processes:
                logger.debug(f"Terminando proceso para sesión {session_id}")
                process = self.session_processes[session_id]
                
                # Primero intentar terminar amablemente
                try:
                    logger.debug(f"Enviando SIGTERM al proceso de sesión {session_id}")
                    process.terminate()
                    # Esperar un poco para que termine amablemente
                    await asyncio.wait_for(process.wait(), timeout=3.0)
                    logger.debug(f"Proceso de sesión {session_id} terminado con SIGTERM")
                except asyncio.TimeoutError:
                    # Si no termina amablemente, forzar terminación
                    logger.debug(f"Timeout con SIGTERM, enviando SIGKILL a sesión {session_id}")
                    process.kill()
                    try:
                        await asyncio.wait_for(process.wait(), timeout=2.0)
                        logger.debug(f"Proceso de sesión {session_id} terminado con SIGKILL")
                    except asyncio.TimeoutError:
                        logger.warning(f"No se pudo terminar el proceso para sesión {session_id}")
                
                # Limpiar referencia al proceso
                logger.debug(f"Limpiando referencia al proceso para sesión {session_id}")
                if session_id in self.session_processes:
                    del self.session_processes[session_id]
                else:
                    logger.debug(f"Proceso para sesión {session_id} ya había sido limpiado")
            else:
                logger.debug(f"No hay proceso activo para sesión {session_id}")
            
            # Actualizar estado final
            logger.debug(f"Actualizando estado final de sesión {session_id} a 'stopped'")
            session.status = "stopped"
            session.completed_at = datetime.now()
            session.updated_at = datetime.now()
            
            # Intentar obtener código generado antes de persistir
            await self.get_generated_code(session_id)
            
            # Persistir sesión
            self._persist_session(session)
            
            logger.info(f"Sesión de Playwright Codegen detenida: {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deteniendo sesión {session_id}: {type(e).__name__}: {str(e)}")
            logger.error(f"Traceback completo:", exc_info=True)
            # Asegurarse de que la sesión está marcada como failed en caso de error
            if session_id in self.active_sessions:
                self.active_sessions[session_id].status = "failed"
                self.active_sessions[session_id].error_message = str(e)
                self.active_sessions[session_id].updated_at = datetime.now()
            return False
    
    async def get_generated_code(self, session_id: str) -> Optional[str]:
        """Obtiene el código generado de una sesión."""
        
        # Buscar en sesiones activas primero
        session = self.active_sessions.get(session_id)
        
        # Si no está en activas, buscar en sesiones completadas
        if not session:
            session_data = self.completed_sessions.get(session_id)
            if session_data:
                # Recrear objeto CodegenSessionInfo desde los datos guardados
                session = CodegenSessionInfo(**session_data)
            else:
                # Buscar en historial persistido
                session_data = self.get_session_from_history(session_id)
                if session_data:
                    session = CodegenSessionInfo(**session_data)
                else:
                    logger.warning(f"Sesión {session_id} no encontrada en activas, completadas o historial")
                    return None
        
        # Si ya tenemos el código, devolverlo
        if session.generated_code:
            logger.debug(f"Código ya cacheado para sesión {session_id}")
            return session.generated_code
        
        # Intentar leer desde archivos de output
        if session.artifacts_path:
            try:
                artifacts_dir = Path(session.artifacts_path)
                logger.debug(f"Buscando código generado en: {artifacts_dir}")
                
                # Verificar que el directorio existe
                if not artifacts_dir.exists():
                    logger.warning(f"Directorio de artefactos no existe: {artifacts_dir}")
                    return None
                
                # Listar todos los archivos en el directorio
                all_files = list(artifacts_dir.iterdir())
                logger.debug(f"Archivos encontrados en {artifacts_dir}: {[f.name for f in all_files]}")
                
                # Patrones de archivos según el lenguaje
                patterns = []
                if session.target_language == "python":
                    patterns = ["*.py", "test_*.py", "*_test.py"]
                elif session.target_language == "javascript":
                    patterns = ["*.spec.js", "*.test.js", "*.js"]
                elif session.target_language == "typescript":
                    patterns = ["*.spec.ts", "*.test.ts", "*.ts"]
                elif session.target_language == "java":
                    patterns = ["*.java"]
                elif session.target_language == "csharp":
                    patterns = ["*.cs"]
                else:
                    # Fallback: buscar cualquier archivo de código
                    patterns = ["*.py", "*.js", "*.ts", "*.java", "*.cs"]
                
                logger.debug(f"Patrones de búsqueda para {session.target_language}: {patterns}")
                
                # Buscar archivos usando los patrones
                output_files = []
                for pattern in patterns:
                    found_files = list(artifacts_dir.glob(pattern))
                    logger.debug(f"Patrón {pattern} encontró: {[f.name for f in found_files]}")
                    output_files.extend(found_files)
                
                # Intentar leer el primer archivo encontrado
                if output_files:
                    # Preferir archivos que contengan "test" o "spec" en el nombre
                    test_files = [f for f in output_files if any(keyword in f.name.lower() 
                                 for keyword in ['test', 'spec', 'codegen'])]
                    
                    target_file = test_files[0] if test_files else output_files[0]
                    logger.info(f"Leyendo código generado desde: {target_file}")
                    
                    with open(target_file, 'r', encoding='utf-8') as f:
                        generated_code = f.read()
                        session.generated_code = generated_code
                        session.updated_at = datetime.now()
                        logger.info(f"Código generado cargado exitosamente para sesión {session_id} ({len(generated_code)} caracteres)")
                        return generated_code
                        
                # Si no encontramos archivos, pero la sesión está "completed", 
                # buscar en el directorio actual cualquier archivo reciente
                if session.status in ["completed", "stopped"]:
                    logger.debug(f"Buscando archivos recientes desde {session.created_at}")
                    import time
                    cutoff_time = session.created_at.timestamp()
                    recent_files = [
                        f for f in artifacts_dir.iterdir() 
                        if f.is_file() and f.stat().st_mtime > cutoff_time
                    ]
                    
                    logger.debug(f"Archivos recientes encontrados: {[f.name for f in recent_files]}")
                    
                    if recent_files:
                        # Tomar el archivo más reciente
                        newest_file = max(recent_files, key=lambda f: f.stat().st_mtime)
                        logger.info(f"Intentando leer archivo más reciente: {newest_file}")
                        try:
                            with open(newest_file, 'r', encoding='utf-8') as f:
                                generated_code = f.read()
                                session.generated_code = generated_code
                                session.updated_at = datetime.now()
                                logger.info(f"Código generado cargado desde archivo reciente para sesión {session_id}")
                                return generated_code
                        except UnicodeDecodeError:
                            logger.warning(f"No se pudo decodificar archivo {newest_file} (posiblemente binario)")
                            pass
                
                logger.warning(f"No se encontró código generado para sesión {session_id}")
                return None
                
            except Exception as e:
                logger.warning(f"Error leyendo código generado para sesión {session_id}: {str(e)}")
        
        return None
    
    async def convert_to_testcase(self, request: CodegenTestCaseRequest) -> Dict[str, Any]:
        """Convierte código generado en un caso de prueba QAK."""
        
        session = self.active_sessions.get(request.session_id)
        if not session:
            raise ValueError(f"Sesión no encontrada: {request.session_id}")
        
        generated_code = await self.get_generated_code(request.session_id)
        if not generated_code:
            raise ValueError("No hay código generado disponible")
        
        try:
            # Procesar y adaptar el código para QAK
            adapted_code = self._adapt_code_for_qak(
                generated_code,
                session.target_language,
                request.framework,
                request.include_assertions,
                request.add_error_handling
            )
            
            # Crear estructura de caso de prueba QAK
            testcase_data = {
                "name": request.test_name,
                "description": request.test_description or f"Test generado con Playwright Codegen",
                "framework": request.framework,
                "language": session.target_language,
                "code": adapted_code,
                "metadata": {
                    "generated_from_codegen": True,
                    "codegen_session_id": request.session_id,
                    "original_url": session.url,
                    "generated_at": datetime.now().isoformat(),
                    "user_story": request.user_story
                }
            }
            
            # Si se especifica proyecto y suite, guardarlo
            if request.project_id and request.test_suite:
                success = await self._save_to_project(
                    request.project_id,
                    request.test_suite,
                    testcase_data
                )
                testcase_data["saved_to_project"] = success
            
            logger.info(f"Código convertido a caso de prueba QAK para sesión {request.session_id}")
            return testcase_data
            
        except Exception as e:
            logger.error(f"Error convirtiendo código a caso de prueba: {str(e)}")
            raise
    
    async def cleanup_session(self, session_id: str) -> bool:
        """Limpia recursos de una sesión."""
        
        if session_id not in self.active_sessions:
            return False
        
        try:
            # Detener proceso si está corriendo
            await self.stop_session(session_id)
            
            # Limpiar directorio
            session = self.active_sessions[session_id]
            if session.artifacts_path and os.path.exists(session.artifacts_path):
                shutil.rmtree(session.artifacts_path)
            
            # Remover de sesiones activas
            del self.active_sessions[session_id]
            
            logger.info(f"Recursos de sesión {session_id} limpiados")
            return True
            
        except Exception as e:
            logger.error(f"Error limpiando sesión {session_id}: {str(e)}")
            return False
    
    async def get_stats(self) -> CodegenStatsResponse:
        """Obtiene estadísticas de uso del servicio."""
        
        total_sessions = len(self.active_sessions)
        active_sessions = len([s for s in self.active_sessions.values() if s.status in ["starting", "running"]])
        completed_sessions = len([s for s in self.active_sessions.values() if s.status == "completed"])
        failed_sessions = len([s for s in self.active_sessions.values() if s.status == "failed"])
        
        # Estadísticas por lenguaje
        sessions_by_language = {}
        for session in self.active_sessions.values():
            lang = session.target_language
            sessions_by_language[lang] = sessions_by_language.get(lang, 0) + 1
        
        # Última sesión
        last_session_at = None
        if self.active_sessions:
            last_session_at = max(s.created_at for s in self.active_sessions.values())
        
        return CodegenStatsResponse(
            total_sessions=total_sessions,
            active_sessions=active_sessions,
            completed_sessions=completed_sessions,
            failed_sessions=failed_sessions,
            sessions_by_language=sessions_by_language,
            last_session_at=last_session_at
        )
    
    def _build_command(self, request: PlaywrightCodegenRequest, session_dir: str) -> List[str]:
        """Construye el comando de Playwright Codegen."""

        # Comando base según el lenguaje
        if request.target_language == "python":
            cmd = ["playwright", "codegen"]
        else:
            cmd = ["npx", "playwright", "codegen"]

        # Nota: Playwright Codegen no soporta modo headless nativo
        # Se ejecuta siempre con interfaz gráfica para interacción manual
        # El parámetro headless se mantiene para compatibilidad con la API

        # Configuraciones del navegador
        if request.device:
            # Validar y mapear dispositivos conocidos
            device_name = self._validate_device_name(request.device)
            if device_name:
                cmd.extend(["--device", device_name])

        if request.viewport_size:
            cmd.extend(["--viewport-size", request.viewport_size])

        # Configuraciones del entorno
        if request.timezone:
            cmd.extend(["--timezone", request.timezone])

        if request.geolocation:
            cmd.extend(["--geolocation", request.geolocation])

        if request.language:
            cmd.extend(["--lang", request.language])

        if request.color_scheme:
            cmd.extend(["--color-scheme", request.color_scheme])

        # Gestión de estado
        if request.load_storage:
            storage_path = os.path.join(session_dir, "auth_input.json")
            cmd.extend(["--load-storage", storage_path])

        if request.save_storage:
            storage_path = os.path.join(session_dir, "auth_output.json")
            cmd.extend(["--save-storage", storage_path])

        # Lenguaje objetivo y archivo de salida
        if request.target_language != "javascript":
            cmd.extend(["--target", request.target_language])

        # Especificar archivo de salida explícitamente
        output_file = self._get_output_filename(request.target_language, session_dir)
        cmd.extend(["-o", output_file])

        # URL inicial
        if request.url:
            cmd.append(request.url)

        return cmd

    def _validate_device_name(self, device: str) -> Optional[str]:
        """Valida y mapea nombres de dispositivos para Playwright."""

        # Mapeo de nombres comunes a dispositivos válidos de Playwright
        device_mapping = {
            "desktop chrome": None,  # No usar --device para desktop
            "desktop": None,
            "chrome": None,
            "desktop firefox": None,
            "firefox": None,
            "desktop safari": None,
            "safari": None,
            "iphone 13": "iPhone 13",
            "iphone 12": "iPhone 12",
            "iphone 11": "iPhone 11",
            "iphone se": "iPhone SE",
            "ipad": "iPad Pro",
            "ipad pro": "iPad Pro",
            "pixel 5": "Pixel 5",
            "pixel 4": "Pixel 4",
            "galaxy s21": "Galaxy S21",
            "galaxy s20": "Galaxy S20",
            "galaxy note 20": "Galaxy Note 20",
            "galaxy tab s7": "Galaxy Tab S7"
        }

        device_lower = device.lower().strip()

        # Si está en el mapeo, usar el valor mapeado
        if device_lower in device_mapping:
            mapped_device = device_mapping[device_lower]
            if mapped_device is None:
                logger.info(f"Dispositivo '{device}' mapeado a desktop (sin --device)")
            else:
                logger.info(f"Dispositivo '{device}' mapeado a '{mapped_device}'")
            return mapped_device

        # Si no está en el mapeo pero parece un dispositivo válido, usarlo tal como está
        if any(keyword in device_lower for keyword in ['iphone', 'ipad', 'pixel', 'galaxy', 'samsung']):
            logger.info(f"Usando dispositivo '{device}' tal como está")
            return device

        # Si no es reconocido, no usar --device (comportamiento desktop por defecto)
        logger.warning(f"Dispositivo '{device}' no reconocido, usando comportamiento desktop por defecto")
        return None

    def _get_output_filename(self, language: str, session_dir: str) -> str:
        """Genera el nombre del archivo de salida según el lenguaje."""
        
        if language == "python":
            return os.path.join(session_dir, "test_codegen.py")
        elif language == "java":
            return os.path.join(session_dir, "TestCodegen.java")
        elif language == "csharp":
            return os.path.join(session_dir, "TestCodegen.cs")
        elif language == "typescript":
            return os.path.join(session_dir, "test.spec.ts")
        else:  # javascript
            return os.path.join(session_dir, "test.spec.js")
    
    def _build_project_integration(self, request: PlaywrightCodegenRequest) -> Optional[Dict[str, Any]]:
        """Construye datos de integración con proyecto QAK."""

        if not request.project_id:
            return None

        return {
            "project_id": request.project_id,
            "test_suite": request.test_suite,
            "user_story": request.user_story,
            "integration_timestamp": datetime.now().isoformat()
        }

    async def _verify_playwright_installation(self):
        """Verifica que Playwright esté correctamente instalado y configurado."""

        try:
            # Preparar entorno para ejecutar playwright
            env = os.environ.copy()

            # Verificar que el comando playwright esté disponible
            process = await asyncio.create_subprocess_exec(
                "playwright", "--version",
                env=env,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode != 0:
                error_msg = stderr.decode('utf-8', errors='ignore') if stderr else "Comando playwright no encontrado"
                raise Exception(f"Playwright no está disponible: {error_msg}")

            version = stdout.decode('utf-8', errors='ignore').strip()
            logger.info(f"Playwright verificado: {version}")

            # Verificar que los navegadores estén instalados
            process = await asyncio.create_subprocess_exec(
                "playwright", "install", "--dry-run",
                env=env,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode != 0:
                error_msg = stderr.decode('utf-8', errors='ignore') if stderr else "Error verificando navegadores"
                logger.warning(f"Advertencia verificando navegadores: {error_msg}")
            else:
                logger.info("Navegadores de Playwright verificados")

            # Verificar que el comando codegen funciona
            test_cmd = ["playwright", "codegen", "--help"]
            process = await asyncio.create_subprocess_exec(
                *test_cmd,
                env=env,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode != 0:
                error_msg = stderr.decode('utf-8', errors='ignore') if stderr else "Error con comando codegen"
                raise Exception(f"Comando 'playwright codegen' no funciona: {error_msg}")

            logger.info("Comando 'playwright codegen' verificado correctamente")

        except FileNotFoundError:
            raise Exception("Playwright no está instalado. Ejecute 'pip install playwright' y 'playwright install'")
        except Exception as e:
            logger.error(f"Error verificando instalación de Playwright: {str(e)}")
            raise
    
    async def _run_session(self, session_id: str, cmd: List[str], session_dir: str, headless: bool = True):
        """Ejecuta una sesión de codegen en background."""

        session = self.active_sessions[session_id]

        try:
            session.status = "running"
            session.updated_at = datetime.now()

            # Preparar variables de entorno
            env = os.environ.copy()

            # Configurar entorno según modo headless
            if headless:
                # Modo headless: minimizar visibilidad del navegador
                env["PLAYWRIGHT_BROWSERS_PATH"] = env.get("PLAYWRIGHT_BROWSERS_PATH", "")
                # En sistemas Unix, intentar ejecutar sin display visible
                if hasattr(os, 'fork'):  # Unix-like systems
                    env["DISPLAY"] = ""
                logger.info(f"Sesión {session_id} configurada para modo headless")
            else:
                # Modo headed: asegurar que se puede mostrar la GUI
                if "DISPLAY" not in env:
                    env["DISPLAY"] = ":0"
                logger.info(f"Sesión {session_id} configurada para modo headed")

            # Log del comando que se va a ejecutar
            logger.info(f"Ejecutando comando para sesión {session_id}: {' '.join(cmd)}")
            logger.info(f"Directorio de trabajo: {session_dir}")

            # Ejecutar comando para Playwright Codegen
            # Capturar stderr para errores, pero permitir que stdout sea interactivo
            process = await asyncio.create_subprocess_exec(
                *cmd,
                cwd=session_dir,
                env=env,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            self.session_processes[session_id] = process

            # No esperamos a que termine porque es interactivo
            # El usuario lo detendrá manualmente desde la UI
            logger.info(f"Proceso Playwright Codegen iniciado para sesión {session_id}, PID: {process.pid}")

            # Monitorear el proceso en background
            asyncio.create_task(self._monitor_session(session_id, process))

        except Exception as e:
            session.status = "failed"
            session.error_message = str(e)
            logger.error(f"Error ejecutando sesión {session_id}: {str(e)}")
            logger.error(f"Comando que falló: {' '.join(cmd)}")
            session.updated_at = datetime.now()
            if session_id in self.session_processes:
                del self.session_processes[session_id]
    
    async def _monitor_session(self, session_id: str, process: asyncio.subprocess.Process):
        """Monitorea una sesión de codegen en background."""

        session = self.active_sessions.get(session_id)
        if not session:
            return

        try:
            # Leer stderr en background para capturar errores inmediatos
            async def read_stderr():
                try:
                    stderr_data = await process.stderr.read()
                    if stderr_data:
                        error_msg = stderr_data.decode('utf-8', errors='ignore')
                        if error_msg.strip():
                            logger.warning(f"Stderr de sesión {session_id}: {error_msg}")
                            # Si hay errores críticos, actualizar la sesión
                            if any(keyword in error_msg.lower() for keyword in ['error', 'failed', 'cannot', 'not found']):
                                session.error_message = error_msg
                except Exception as e:
                    logger.debug(f"Error leyendo stderr de sesión {session_id}: {str(e)}")

            # Iniciar lectura de stderr en background
            asyncio.create_task(read_stderr())

            # Esperar un poco para que el proceso se inicie completamente
            await asyncio.sleep(2)

            # Verificar si el proceso sigue corriendo después del inicio
            if process.returncode is not None:
                logger.warning(f"Proceso de sesión {session_id} terminó inmediatamente con código {process.returncode}")
                if process.returncode != 0:
                    session.status = "failed"
                    session.error_message = f"El proceso terminó inmediatamente con código {process.returncode}"
                    session.completed_at = datetime.now()
                    self._persist_session(session)
                    return

            # Esperar a que el proceso termine (por acción del usuario o stop_session)
            returncode = await process.wait()

            # Verificar si la sesión ya está siendo manejada por stop_session
            if session.status == "stopping":
                # La sesión está siendo detenida manualmente, no hacer nada más
                logger.debug(f"Sesión {session_id} ya está siendo detenida manualmente")
                return

            # Actualizar estado basado en cómo terminó
            if returncode == 0:
                session.status = "completed"
                session.completed_at = datetime.now()
                logger.info(f"Sesión {session_id} completada exitosamente")
            elif returncode == -15 or returncode == -9:  # SIGTERM or SIGKILL
                session.status = "stopped"
                session.completed_at = datetime.now()
                logger.info(f"Sesión {session_id} detenida por el usuario")
            else:
                session.status = "failed"
                session.completed_at = datetime.now()
                # Intentar leer error si está disponible
                try:
                    _, stderr = await asyncio.wait_for(process.communicate(), timeout=1.0)
                    if stderr:
                        error_msg = stderr.decode('utf-8', errors='ignore')
                        if not session.error_message:  # Solo si no hay error previo
                            session.error_message = error_msg
                except:
                    if not session.error_message:
                        session.error_message = f"Proceso terminó con código {returncode}"
                logger.warning(f"Sesión {session_id} falló con código {returncode}: {session.error_message}")

            # Persistir la sesión completada
            self._persist_session(session)

        except Exception as e:
            # Solo actualizar si la sesión todavía existe y no está siendo manejada manualmente
            if session_id in self.active_sessions and session.status != "stopping":
                session.status = "failed"
                session.error_message = str(e)
                logger.error(f"Error monitoreando sesión {session_id}: {str(e)}")

        finally:
            # Solo limpiar si la sesión todavía existe y no está siendo manejada manualmente
            if session_id in self.active_sessions and session.status != "stopping":
                session.updated_at = datetime.now()

            # Limpiar referencia al proceso solo si todavía existe
            if session_id in self.session_processes:
                del self.session_processes[session_id]
    
    def _adapt_code_for_qak(
        self,
        generated_code: str,
        language: str,
        framework: str,
        include_assertions: bool,
        add_error_handling: bool
    ) -> str:
        """Adapta el código generado para QAK."""
        
        # Plantillas de adaptación por lenguaje
        if language == "python":
            return self._adapt_python_code(generated_code, framework, include_assertions, add_error_handling)
        elif language == "javascript":
            return self._adapt_javascript_code(generated_code, framework, include_assertions, add_error_handling)
        else:
            # Para otros lenguajes, devolver tal como está por ahora
            return generated_code
    
    def _adapt_python_code(self, code: str, framework: str, include_assertions: bool, add_error_handling: bool) -> str:
        """Adapta código Python para QAK."""
        
        adapted = []
        adapted.append("# Código generado con Playwright Codegen y adaptado para QAK")
        adapted.append("import pytest")
        adapted.append("from playwright.sync_api import Page, expect")
        adapted.append("")
        
        if add_error_handling:
            adapted.append("@pytest.fixture(scope='function')")
            adapted.append("def setup_teardown():")
            adapted.append("    # Setup")
            adapted.append("    yield")
            adapted.append("    # Teardown")
            adapted.append("")
        
        # Agregar el código original con modificaciones
        lines = code.split('\n')
        for line in lines:
            if line.strip():
                adapted.append(line)
        
        if include_assertions:
            adapted.append("")
            adapted.append("    # Verificaciones adicionales agregadas por QAK")
            adapted.append("    expect(page).to_have_url(page.url)")
        
        return '\n'.join(adapted)
    
    def _adapt_javascript_code(self, code: str, framework: str, include_assertions: bool, add_error_handling: bool) -> str:
        """Adapta código JavaScript para QAK."""
        
        adapted = []
        adapted.append("// Código generado con Playwright Codegen y adaptado para QAK")
        adapted.append("import { test, expect } from '@playwright/test';")
        adapted.append("")
        
        # Agregar el código original
        adapted.append(code)
        
        if include_assertions:
            # Buscar la última línea del test e insertar verificaciones adicionales
            lines = adapted[-1].split('\n') if adapted else []
            if lines and '});' in lines[-1]:
                lines.insert(-1, "  // Verificaciones adicionales agregadas por QAK")
                lines.insert(-1, "  await expect(page).toHaveURL(page.url());")
                adapted[-1] = '\n'.join(lines)
        
        return '\n'.join(adapted)
    
    async def _save_to_project(self, project_id: str, test_suite: str, testcase_data: Dict[str, Any]) -> bool:
        """Guarda el caso de prueba en un proyecto QAK."""
        
        try:
            # Usar el ProjectManager existente para guardar
            success = self.project_manager.add_test_case(
                project_id,
                test_suite,
                testcase_data["name"],
                testcase_data
            )
            return success
            
        except Exception as e:
            logger.error(f"Error guardando caso de prueba en proyecto {project_id}: {str(e)}")
            return False
    
    def _load_sessions_history(self):
        """Carga el historial de sesiones desde el sistema de archivos."""
        try:
            logger.debug("Iniciando carga del historial de sesiones")
            
            # Cargar desde archivo central
            if os.path.exists(self.sessions_file):
                logger.debug(f"Cargando historial central desde {self.sessions_file}")
                with open(self.sessions_file, 'r', encoding='utf-8') as f:
                    self.completed_sessions = json.load(f)
                logger.info(f"Cargadas {len(self.completed_sessions)} sesiones del historial central")
            else:
                logger.debug("No existe archivo de historial central, iniciando vacío")
                self.completed_sessions = {}
            
            # Cargar sesiones individuales
            sessions_dir = Path(self.sessions_dir)
            session_files = list(sessions_dir.glob("*.json"))
            logger.debug(f"Encontrados {len(session_files)} archivos de sesiones individuales")
            
            for file_path in session_files:
                if file_path.name == "sessions_history.json":
                    continue  # Saltar el archivo central
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        session_data = json.load(f)
                        
                    # Validar que tiene los campos requeridos
                    if "session_id" in session_data:
                        session_id = session_data["session_id"]
                        
                        # Convertir strings de fecha a datetime
                        for date_field in ["created_at", "updated_at", "completed_at"]:
                            if date_field in session_data and isinstance(session_data[date_field], str):
                                try:
                                    session_data[date_field] = datetime.fromisoformat(session_data[date_field])
                                except ValueError:
                                    logger.warning(f"No se pudo convertir fecha {date_field} para sesión {session_id}")
                        
                        # Agregar al diccionario de sesiones si está completada
                        if session_data.get("status") in ["completed", "failed", "stopped"]:
                            self.completed_sessions[session_id] = session_data
                            logger.debug(f"Cargada sesión {session_id} desde archivo individual")
                        
                except Exception as e:
                    logger.warning(f"Error cargando sesión desde {file_path}: {str(e)}")
            
            logger.info(f"Carga de historial completada. Total sesiones: {len(self.completed_sessions)}")
            
            # Hacer limpieza de sesiones antiguas
            self._cleanup_sessions_history()
            
        except Exception as e:
            logger.error(f"Error cargando historial de sesiones: {str(e)}")
            self.completed_sessions = {}
    
    def _cleanup_sessions_history(self):
        """Limpia sesiones antiguas del historial."""
        try:
            now = datetime.now()
            cutoff_time = now - timedelta(days=30)  # Mantener sesiones de últimos 30 días
            
            # Filtrar sesiones antiguas
            sessions_to_keep = {}
            for session_id, session_data in self.completed_sessions.items():
                created_at = session_data.get("created_at")
                if isinstance(created_at, str):
                    try:
                        created_at = datetime.fromisoformat(created_at)
                    except ValueError:
                        continue
                
                if isinstance(created_at, datetime) and created_at > cutoff_time:
                    sessions_to_keep[session_id] = session_data
            
            removed_count = len(self.completed_sessions) - len(sessions_to_keep)
            self.completed_sessions = sessions_to_keep
            
            if removed_count > 0:
                logger.info(f"Limpiadas {removed_count} sesiones antiguas del historial")
                
                # Actualizar archivo central
                self._save_sessions_history()
                
                # Limpiar archivos individuales antiguos
                sessions_dir = Path(self.sessions_dir)
                for file_path in sessions_dir.glob("*.json"):
                    if file_path.name == "sessions_history.json":
                        continue
                        
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            session_data = json.load(f)
                            
                        if "session_id" in session_data and session_data["session_id"] not in sessions_to_keep:
                            file_path.unlink()
                            logger.debug(f"Eliminado archivo de sesión antigua: {file_path}")
                    except Exception as e:
                        logger.warning(f"Error limpiando archivo {file_path}: {str(e)}")
                        
        except Exception as e:
            logger.error(f"Error limpiando historial de sesiones: {str(e)}")
    
    def _save_sessions_history(self):
        """Guarda el historial de sesiones en el archivo central."""
        try:
            with open(self.sessions_file, 'w', encoding='utf-8') as f:
                json.dump(self.completed_sessions, f, ensure_ascii=False, indent=2, default=str)
            logger.debug(f"Historial guardado en {self.sessions_file}")
        except Exception as e:
            logger.error(f"Error guardando historial de sesiones: {str(e)}")

    def _persist_session(self, session: CodegenSessionInfo):
        """Persiste una sesión completada en el historial y archivo individual."""
        try:
            session_data = {
                "session_id": session.session_id,
                "status": session.status,
                "target_language": session.target_language,
                "url": session.url,
                "created_at": session.created_at.isoformat(),
                "updated_at": session.updated_at.isoformat(),
                "completed_at": session.completed_at.isoformat() if session.completed_at else None,
                "command_used": session.command_used,
                "error_message": session.error_message,
                "generated_code": session.generated_code,
                "project_integration": session.project_integration,
                "artifacts_path": session.artifacts_path
            }
            
            # Guardar en historial centralizado
            self.completed_sessions[session.session_id] = session_data
            self._save_sessions_history()
            
            # Guardar sesión individual en archivo
            session_file = os.path.join(self.sessions_dir, f"{session.session_id}.json")
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, ensure_ascii=False, indent=2, default=str)
            
            logger.debug(f"Sesión {session.session_id} persistida en historial y archivo individual")
            
        except Exception as e:
            logger.error(f"Error persistiendo sesión {session.session_id}: {str(e)}")

    def get_sessions_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Obtiene el historial de sesiones completadas."""
        try:
            # Combinar sesiones activas completadas y sesiones históricas
            all_sessions = []
            
            # Agregar sesiones activas completadas
            for session in self.active_sessions.values():
                if session.status in ["completed", "stopped", "failed"]:
                    all_sessions.append({
                        "session_id": session.session_id,
                        "status": session.status,
                        "target_language": session.target_language,
                        "url": session.url,
                        "created_at": session.created_at.isoformat(),
                        "updated_at": session.updated_at.isoformat(),
                        "completed_at": session.completed_at.isoformat() if session.completed_at else None,
                        "has_generated_code": bool(session.generated_code),
                        "project_integration": session.project_integration
                    })
            
            # Agregar sesiones del historial
            for session_data in self.completed_sessions.values():
                # Evitar duplicados
                if not any(s["session_id"] == session_data["session_id"] for s in all_sessions):
                    session_copy = session_data.copy()
                    session_copy["has_generated_code"] = bool(session_copy.get("generated_code"))
                    # No incluir el código generado en la lista (solo el flag)
                    session_copy.pop("generated_code", None)
                    all_sessions.append(session_copy)
            
            # Ordenar por fecha de creación (más recientes primero)
            all_sessions.sort(key=lambda x: x["created_at"], reverse=True)
            
            return all_sessions[:limit]
            
        except Exception as e:
            logger.error(f"Error obteniendo historial de sesiones: {str(e)}")
            return []

    def get_session_from_history(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Obtiene una sesión específica del historial o sesiones activas."""
        # Buscar en sesiones activas primero
        if session_id in self.active_sessions:
            session = self.active_sessions[session_id]
            return {
                "session_id": session.session_id,
                "status": session.status,
                "target_language": session.target_language,
                "url": session.url,
                "created_at": session.created_at.isoformat(),
                "updated_at": session.updated_at.isoformat(),
                "completed_at": session.completed_at.isoformat() if session.completed_at else None,
                "command_used": session.command_used,
                "error_message": session.error_message,
                "generated_code": session.generated_code,
                "project_integration": session.project_integration
            }
        
        # Buscar en historial
        return self.completed_sessions.get(session_id)
    
    async def _cleanup_old_sessions(self):
        """Tarea de limpieza automática de sesiones antiguas."""
        
        while True:
            try:
                await asyncio.sleep(3600)  # Ejecutar cada hora
                
                # Limpiar sesiones activas antiguas
                cutoff_time = datetime.now() - timedelta(hours=24)
                sessions_to_cleanup = [
                    session_id for session_id, session in self.active_sessions.items()
                    if session.created_at < cutoff_time and session.status in ["completed", "failed", "stopped"]
                ]
                
                for session_id in sessions_to_cleanup:
                    await self.cleanup_session(session_id)
                
                # Limpiar historial muy antiguo (más de 30 días)
                old_cutoff_time = datetime.now() - timedelta(days=30)
                old_sessions = []
                
                for session_id, session_data in list(self.completed_sessions.items()):
                    try:
                        created_at = datetime.fromisoformat(session_data["created_at"].replace('Z', '+00:00'))
                        if created_at < old_cutoff_time:
                            old_sessions.append(session_id)
                    except (ValueError, KeyError):
                        # Si no se puede parsear la fecha, mantener la sesión
                        continue
                
                # Eliminar sesiones muy antiguas
                for session_id in old_sessions:
                    try:
                        # Eliminar del historial
                        del self.completed_sessions[session_id]
                        
                        # Eliminar archivo individual
                        session_file = os.path.join(self.sessions_dir, f"{session_id}.json")
                        if os.path.exists(session_file):
                            os.remove(session_file)
                        
                        logger.debug(f"Sesión antigua eliminada: {session_id}")
                    except Exception as e:
                        logger.warning(f"Error eliminando sesión antigua {session_id}: {str(e)}")
                
                if old_sessions:
                    # Actualizar historial centralizado
                    self._save_sessions_history()
                    logger.info(f"Eliminadas {len(old_sessions)} sesiones antiguas del historial")
                
                if sessions_to_cleanup:
                    logger.info(f"Limpiadas {len(sessions_to_cleanup)} sesiones activas antiguas")
                    
            except Exception as e:
                logger.error(f"Error en limpieza automática de sesiones: {str(e)}")
