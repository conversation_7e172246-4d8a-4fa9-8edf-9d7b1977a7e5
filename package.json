{"name": "qak", "version": "1.0.0", "description": "Herramienta de automatización de pruebas impulsada por IA que transforma historias de usuario en código de automatización ejecutable, permitiendo la generación y ejecución de pruebas automatizadas a partir de instrucciones en lenguaje natural.", "main": "index.js", "directories": {"example": "examples", "test": "tests"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"playwright": "^1.53.1"}}