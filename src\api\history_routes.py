"""Rutas de API para gestión de historial de pruebas."""

import os
from fastapi import APIRouter, HTTPException, Depends

from src.API.models import SaveHistoryRequest
from src.Core.test_service import TestService
from src.Utilities.response_transformers import (
    clean_data_for_json_serialization,
    convert_screenshot_paths_to_urls
)

# Router para historial de pruebas
router = APIRouter(prefix="/api", tags=["history"])


def get_test_service():
    """Crea y devuelve una instancia del servicio de pruebas."""
    return TestService(api_key=os.environ.get("GOOGLE_API_KEY"))


@router.post("/projects/save-history", summary="Guardar historial en proyecto")
async def save_history_to_project(
    request: SaveHistoryRequest,
    test_service: TestService = Depends(get_test_service)
):
    """Guarda un historial de prueba en un proyecto."""
    try:
        test_case = test_service.save_history_to_project(
            project_id=request.project_id,
            suite_id=request.suite_id,
            test_history=request.test_history,
            name=request.name,
            description=request.description,
            gherkin=request.gherkin
        )
        return {"test_case": test_case}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/history/{history_path:path}", summary="Obtener historial de prueba procesado")
async def get_processed_test_history(history_path: str):
    """
    Obtiene un historial de prueba procesado con capturas de pantalla extraídas.
    
    Args:
        history_path: Ruta al archivo de historial (ej: tests/smoke_test_20250611231818/history.json)
    
    Returns:
        TestExecutionHistoryData: Historial procesado con capturas de pantalla convertidas a URLs
    """
    try:
        from src.Utilities.project_manager_service import load_test_history
        
        # Construir la ruta completa al archivo
        if not history_path.startswith(os.sep) and not os.path.isabs(history_path):
            # Es una ruta relativa, construir desde el directorio de trabajo
            full_path = os.path.join(os.getcwd(), history_path)
        else:
            full_path = history_path
        
        # Verificar que el archivo existe
        if not os.path.exists(full_path):
            raise HTTPException(status_code=404, detail=f"History file not found: {history_path}")
        
        # Cargar y procesar el historial
        processed_history = load_test_history(full_path)
        
        # Convertir las rutas de capturas de pantalla a URLs servibles
        if processed_history.get("screenshots"):
            converted_screenshots = convert_screenshot_paths_to_urls(processed_history["screenshots"])
            processed_history["screenshots"] = converted_screenshots
        
        # Limpiar los datos para serialización JSON
        cleaned_history = clean_data_for_json_serialization(processed_history)
        
        return cleaned_history
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing history file: {str(e)}")
