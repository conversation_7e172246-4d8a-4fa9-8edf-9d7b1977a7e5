{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/lib/config.ts"], "sourcesContent": ["export const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api';\r\n\r\n"], "names": [], "mappings": ";;;AAA4B;AAArB,MAAM,eAAe,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,wBAAwB,IAAI", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/lib/api.ts"], "sourcesContent": ["import { API_BASE_URL } from '@/lib/config';\r\nimport type {\r\n  ApiResponse,\r\n  Project, ProjectCreateInput, ProjectUpdateInput,\r\n  TestSuite, TestSuiteCreateInput, TestSuiteUpdateInput,\r\n  TestCase, TestCaseCreateInput, TestCaseUpdateInput, TestCaseStatusUpdateInput,\r\n  ApiHealth,\r\n  TestCaseExecutionResponse,\r\n  SuiteExecutionResponse,\r\n  TestExecutionHistoryData,\r\n  GenerateGherkinInput, GenerateGherkinOutput,\r\n  GenerateCodeInput, GenerateCodeOutput,\r\n  EnhanceUserStoryInput, EnhanceUserStoryOutput,\r\n  ExecuteSmokeTestInput, ExecuteSmokeTestOutput,\r\n  GenerateManualTestCasesInput, GenerateManualTestCasesOutput,\r\n  SummarizeTestResultsInput, SummarizeTestResultsOutput,\r\n  PlaywrightCodegenRequest,\r\n  CodegenSessionInfo,\r\n  CodegenTestCaseRequest,\r\n  CodegenStatsResponse,\r\n  CodegenSessionListResponse,\r\n  CodegenHealthResponse,\r\n  CodegenHistoryResponse,\r\n  CodegenHistorySessionDetailResponse,\r\n  CodegenExecutionRequest,\r\n  CodegenExecutionResponse,\r\n  CodegenExecutionInfo,\r\n  CodegenExecutionListResponse\r\n} from '@/lib/types';\r\n\r\n\r\nasync function fetchApi<T>(url: string, options?: RequestInit): Promise<T> {\r\n  const response = await fetch(`${API_BASE_URL}${url}`, {\r\n    ...options,\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      'ngrok-skip-browser-warning': 'true', // 🔧 Agregar header para evitar bloqueo de ngrok\r\n      ...(options?.headers),\r\n    },\r\n  });\r\n\r\n  if (!response.ok) {\r\n    let errorData;\r\n    let errorMessage = `HTTP error! status: ${response.status} for URL: ${url}`;\r\n    try {\r\n      const contentType = response.headers.get(\"content-type\");\r\n      if (contentType && contentType.indexOf(\"application/json\") !== -1) {\r\n        errorData = await response.json();\r\n        errorMessage = errorData?.error || errorData?.details || errorMessage;\r\n      } else {\r\n        const errorText = await response.text();\r\n        // If HTML error page, include a snippet.\r\n        if (errorText.toLowerCase().includes(\"<!doctype html\")) {\r\n            errorMessage = `${errorMessage}. Server returned an HTML error page. Snippet: ${errorText.substring(0, 200)}...`;\r\n        } else {\r\n            errorMessage = `${errorMessage}. Response: ${errorText.substring(0, 500)}${errorText.length > 500 ? '...' : ''}`;\r\n        }\r\n      }\r\n    } catch (e) {\r\n      // If parsing as JSON or text fails, stick with the basic error from statusText or status code.\r\n      errorMessage = `${response.statusText || `HTTP error! status: ${response.status} for URL: ${url}`}. Could not parse error response.`;\r\n    }\r\n    throw new Error(errorMessage);\r\n  }\r\n  // Handle cases where response is OK but content might be empty for 204 No Content\r\n  if (response.status === 204) {\r\n    return {} as T; // Or handle as appropriate for your application\r\n  }\r\n  return response.json();\r\n}\r\n\r\n// Project Endpoints\r\nexport const getProjects = (): Promise<ApiResponse<Project>> => fetchApi<ApiResponse<Project>>('/projects/');\r\nexport const createProject = (data: ProjectCreateInput): Promise<Project> => fetchApi<Project>('/projects/', { method: 'POST', body: JSON.stringify(data) });\r\nexport const getProjectById = (projectId: string): Promise<Project> => fetchApi<Project>(`/projects/${projectId}`);\r\nexport const updateProject = (projectId: string, data: ProjectUpdateInput): Promise<Project> => fetchApi<Project>(`/projects/${projectId}`, { method: 'PUT', body: JSON.stringify(data) });\r\nexport const deleteProject = (projectId: string): Promise<void> => fetchApi<void>(`/projects/${projectId}`, { method: 'DELETE' });\r\n\r\n// Test Suite Endpoints\r\nexport const getSuitesByProjectId = (projectId: string): Promise<ApiResponse<TestSuite>> => fetchApi<ApiResponse<TestSuite>>(`/projects/${projectId}/suites`);\r\nexport const createSuite = (projectId: string, data: TestSuiteCreateInput): Promise<TestSuite> => fetchApi<TestSuite>(`/projects/${projectId}/suites/`, { method: 'POST', body: JSON.stringify(data) });\r\nexport const getSuiteById = (projectId: string, suiteId: string): Promise<TestSuite> => fetchApi<TestSuite>(`/projects/${projectId}/suites/${suiteId}`);\r\nexport const updateSuite = (projectId: string, suiteId: string, data: TestSuiteUpdateInput): Promise<TestSuite> => fetchApi<TestSuite>(`/projects/${projectId}/suites/${suiteId}`, { method: 'PUT', body: JSON.stringify(data) });\r\nexport const deleteSuite = (projectId: string, suiteId: string): Promise<void> => fetchApi<void>(`/projects/${projectId}/suites/${suiteId}`, { method: 'DELETE' });\r\nexport const executeSuite = (projectId: string, suiteId: string): Promise<SuiteExecutionResponse> => fetchApi<SuiteExecutionResponse>(`/projects/${projectId}/suites/${suiteId}/execute`, { method: 'POST' });\r\n\r\n// Test Case Endpoints\r\nexport const getTestCasesBySuiteId = (projectId: string, suiteId: string): Promise<ApiResponse<TestCase>> => fetchApi<ApiResponse<TestCase>>(`/projects/${projectId}/suites/${suiteId}/tests`);\r\nexport const createTestCase = (projectId: string, suiteId: string, data: TestCaseCreateInput): Promise<TestCase> => fetchApi<TestCase>(`/projects/${projectId}/suites/${suiteId}/tests/`, { method: 'POST', body: JSON.stringify(data) });\r\nexport const getTestCaseById = (projectId: string, suiteId: string, testId: string): Promise<TestCase> => fetchApi<TestCase>(`/projects/${projectId}/suites/${suiteId}/tests/${testId}`);\r\nexport const updateTestCase = (projectId: string, suiteId: string, testId: string, data: TestCaseUpdateInput): Promise<TestCase> => fetchApi<TestCase>(`/projects/${projectId}/suites/${suiteId}/tests/${testId}`, { method: 'PUT', body: JSON.stringify(data) });\r\nexport const updateTestCaseStatus = (projectId: string, suiteId: string, testId: string, data: TestCaseStatusUpdateInput): Promise<TestCase> => fetchApi<TestCase>(`/projects/${projectId}/suites/${suiteId}/tests/${testId}/status`, { method: 'PATCH', body: JSON.stringify(data) });\r\nexport const deleteTestCase = (projectId: string, suiteId: string, testId: string): Promise<void> => fetchApi<void>(`/projects/${projectId}/suites/${suiteId}/tests/${testId}`, { method: 'DELETE' });\r\nexport const executeTestCase = (projectId: string, suiteId: string, testId: string): Promise<TestCaseExecutionResponse> => fetchApi<TestCaseExecutionResponse>(`/projects/${projectId}/suites/${suiteId}/tests/${testId}/execute`, { method: 'POST' });\r\n\r\n// New function to execute test case using smoke test logic\r\nexport const executeTestCaseAsSmokeTest = async (projectId: string, suiteId: string, testId: string): Promise<ExecuteSmokeTestOutput> => {\r\n  // First, get the test case details\r\n  const testCase = await getTestCaseById(projectId, suiteId, testId);\r\n\r\n  // Create smoke test input from test case data\r\n  const smokeTestInput: ExecuteSmokeTestInput = {\r\n    instructions: testCase.instrucciones || '',\r\n    baseUrl: testCase.url || '',\r\n    userStory: testCase.historia_de_usuario || ''\r\n  };\r\n\r\n  // Execute using the same smoke test logic\r\n  return callExecuteSmokeTest(smokeTestInput);\r\n};\r\n\r\n// API Health\r\nexport const getApiHealth = (): Promise<ApiHealth> => fetchApi<ApiHealth>('/health');\r\n\r\nexport const getTestExecutionHistoryDetails = async (historyPath: string): Promise<TestExecutionHistoryData> => {\r\n  // Use the new backend endpoint that processes the history and extracts screenshots\r\n  const response = await fetchApi<TestExecutionHistoryData>(`/history/${encodeURIComponent(historyPath)}`);\r\n  return response;\r\n};\r\n\r\n\r\n// AI Tool Endpoints\r\n\r\n// Uses the API endpoint from OpenAPI spec\r\nexport async function callEnhanceUserStory(input: EnhanceUserStoryInput): Promise<EnhanceUserStoryOutput> {\r\n  // Converting our input to match the API schema\r\n  const apiInput = {\r\n    user_story: input.userStory,\r\n    language: input.language\r\n  };\r\n\r\n  try {\r\n    // Using the documented endpoint\r\n    const result = await fetchApi<any>('/stories/enhance', {\r\n      method: 'POST',\r\n      body: JSON.stringify(apiInput),\r\n    });\r\n    return {\r\n      enhancedUserStory: result.enhanced_story || result.enhancedUserStory || '',\r\n    };\r\n  } catch (error) {\r\n    throw new Error(`Failed to enhance user story: ${(error as Error).message}`);\r\n  }\r\n}\r\n\r\nexport async function callGenerateManualTestCases(input: GenerateManualTestCasesInput): Promise<GenerateManualTestCasesOutput> {\r\n  // Converting our input to match the API schema\r\n  const apiInput = {\r\n    enhanced_story: input.userStory,\r\n    language: input.language\r\n  };\r\n\r\n  try {\r\n    // Using the documented endpoint\r\n    const result = await fetchApi<any>('/stories/generate-manual-tests', {\r\n      method: 'POST',\r\n      body: JSON.stringify(apiInput),\r\n    });    // Convert API response to our expected output format\r\n    let manualTestCases: (string | any)[] = [];\r\n    \r\n    // Handle different response formats from the API\r\n    if (result && result.manual_tests) {\r\n      if (Array.isArray(result.manual_tests)) {\r\n        // Check if it's an array of objects (new format) or strings (old format)\r\n        if (result.manual_tests.length > 0 && typeof result.manual_tests[0] === 'object') {\r\n          // New format: array of test case objects - keep as objects\r\n          manualTestCases = result.manual_tests;\r\n        } else {\r\n          // Old format: direct array of strings\r\n          manualTestCases = result.manual_tests;\r\n        }\r\n      } else if (typeof result.manual_tests === 'string') {\r\n        // String response - might be JSON or plain text\r\n        try {\r\n          // Try parsing as JSON\r\n          const parsed = JSON.parse(result.manual_tests);\r\n          if (Array.isArray(parsed)) {\r\n            if (parsed.length > 0 && typeof parsed[0] === 'object') {\r\n              // Array of objects - keep as objects\r\n              manualTestCases = parsed;\r\n            } else {\r\n              // Array of strings\r\n              manualTestCases = parsed;\r\n            }\r\n          } else {\r\n            // Create a single item array if it's an object\r\n            manualTestCases = [parsed];\r\n          }\r\n        } catch (e) {\r\n          // If parsing fails, split lines\r\n          manualTestCases = result.manual_tests.split('\\n').filter((line: string) => line.trim().length > 0);\r\n        }\r\n      }\r\n    } else if (result && Array.isArray(result.manualTestCases)) {\r\n      // Alternative field name\r\n      manualTestCases = result.manualTestCases;\r\n    } else if (result && typeof result === 'object') {\r\n      // Try to use the result directly\r\n      manualTestCases = [result];\r\n    }\r\n\r\n    return { manualTestCases };\r\n  } catch (error) {\r\n    console.error(\"Manual test generation error:\", error);\r\n    throw new Error(`Failed to generate manual test cases: ${(error as Error).message}`);\r\n  }\r\n}\r\n\r\n// Uses the API endpoint from OpenAPI spec\r\nexport async function callGenerateGherkin(input: GenerateGherkinInput): Promise<GenerateGherkinOutput> {\r\n  // For generating Gherkin from manual test cases\r\n  if (input.instructions) {\r\n    try {\r\n      // Using the documented endpoint for generating Gherkin from manual tests\r\n      const apiInput = {\r\n        manual_tests: Array.isArray(input.instructions)\r\n          ? input.instructions.join('\\n')\r\n          : input.instructions,\r\n        language: input.language\r\n      };\r\n\r\n      const result = await fetchApi<any>('/stories/generate-gherkin', {\r\n        method: 'POST',\r\n        body: JSON.stringify(apiInput),\r\n      });\r\n\r\n      // Convert API response to our expected output format\r\n      return {\r\n        gherkin: result.gherkin || result.gherkin_scenario || '',\r\n      };\r\n    } catch (error) {\r\n      throw new Error(`Failed to generate Gherkin: ${(error as Error).message}`);\r\n    }\r\n  }\r\n  // For generating Gherkin directly from instructions and URL\r\n  else {\r\n    try {\r\n      // Using the documented endpoint for generating Gherkin directly\r\n      const apiInput = {\r\n        instructions: input.userStory || '',\r\n        url: input.url || '',\r\n        user_story: input.userStory || '',\r\n        language: input.language\r\n      };\r\n\r\n      const result = await fetchApi<any>('/generate/gherkin', {\r\n        method: 'POST',\r\n        body: JSON.stringify(apiInput),\r\n      });\r\n\r\n      // Convert API response to our expected output format\r\n      return {\r\n        gherkin: result.gherkin || result.gherkin_scenario || '',\r\n      };\r\n    } catch (error) {\r\n      throw new Error(`Failed to generate Gherkin: ${(error as Error).message}`);\r\n    }\r\n  }\r\n}\r\n\r\n\r\n// Uses the API endpoint from OpenAPI spec for code generation\r\nexport async function callGenerateCode(input: GenerateCodeInput): Promise<GenerateCodeOutput> {\r\n  try {\r\n    const apiInput = {\r\n      framework: input.framework,\r\n      gherkin_scenario: input.gherkin_scenario,\r\n      test_history: input.test_history || {}\r\n    };\r\n\r\n    const result = await fetchApi<any>('/generate/code', {\r\n      method: 'POST',\r\n      body: JSON.stringify(apiInput),\r\n    });\r\n\r\n    return {\r\n      code: result.code || '',\r\n    };\r\n  } catch (error) {\r\n    let errorMessage = `Failed to generate code: ${(error as Error).message}`;\r\n    throw new Error(errorMessage);\r\n  }\r\n}\r\n\r\nexport async function callExecuteSmokeTest(input: ExecuteSmokeTestInput): Promise<ExecuteSmokeTestOutput> {\r\n  const apiInput = {\r\n    instructions: input.instructions,\r\n    url: input.baseUrl,\r\n    user_story: input.userStory,\r\n    config_id: input.configId,\r\n    configuration: input.configuration\r\n  };\r\n\r\n  try {\r\n    // Using the documented smoke test endpoint\r\n    const result = await fetchApi<any>('/tests/smoke', {\r\n      method: 'POST',\r\n      body: JSON.stringify(apiInput),\r\n    });\r\n\r\n    // Convert API response to our expected output format based on TestExecutionHistoryData schema\r\n    const executionData: ExecuteSmokeTestOutput = {\r\n      actions: result.actions || [],\r\n      results: result.results || [],\r\n      elements: result.elements || [],\r\n      urls: result.urls || [],\r\n      errors: result.errors || [],\r\n      screenshots: result.screenshots || [],\r\n      metadata: {\r\n        start_time: result.start_time || new Date().toISOString(),\r\n        end_time: result.end_time || new Date().toISOString(),\r\n        total_steps: result.total_steps || 0,\r\n        success: result.success !== undefined ? result.success : true\r\n      },\r\n      generatedGherkin: result.generated_gherkin || result.generatedGherkin || '',\r\n      userStory: input.userStory\r\n    };\r\n\r\n    return executionData;\r\n  } catch (error) {\r\n    throw new Error(`Failed to execute smoke test: ${(error as Error).message}`);\r\n  }\r\n}\r\n\r\n// Uses the API endpoint from OpenAPI spec for full test execution\r\nexport async function callExecuteFullTest(input: { gherkin: string, url?: string }): Promise<ExecuteSmokeTestOutput> {\r\n  // Converting our input to match the API schema for full tests\r\n  const apiInput = {\r\n    gherkin_scenario: input.gherkin,\r\n    url: input.url\r\n  };\r\n\r\n  try {\r\n    // Using the documented full test endpoint\r\n    const result = await fetchApi<any>('/tests/full', {\r\n      method: 'POST',\r\n      body: JSON.stringify(apiInput),\r\n    });\r\n\r\n    // Convert API response to our expected output format based on TestExecutionHistoryData schema\r\n    const executionData: ExecuteSmokeTestOutput = {\r\n      actions: result.actions || [],\r\n      results: result.results || [],\r\n      elements: result.elements || [],\r\n      urls: result.urls || [],\r\n      errors: result.errors || [],\r\n      screenshots: result.screenshots || [],\r\n      metadata: {\r\n        start_time: result.start_time || new Date().toISOString(),\r\n        end_time: result.end_time || new Date().toISOString(),\r\n        total_steps: result.total_steps || 0,\r\n        success: result.success !== undefined ? result.success : true\r\n      },\r\n      generatedGherkin: input.gherkin // Use the input Gherkin since this was a full test\r\n    };\r\n\r\n    return executionData;\r\n  } catch (error) {\r\n    throw new Error(`Failed to execute full test: ${(error as Error).message}`);\r\n  }\r\n}\r\n\r\n// Function to save test history to a project\r\nexport async function saveTestHistory(input: {\r\n  projectId: string,\r\n  suiteId: string,\r\n  name: string,\r\n  description: string,\r\n  gherkin: string,\r\n  testHistory: any\r\n}): Promise<any> {\r\n  // Convert our input to match the API schema\r\n  const apiInput = {\r\n    project_id: input.projectId,\r\n    suite_id: input.suiteId,\r\n    name: input.name,\r\n    description: input.description,\r\n    gherkin: input.gherkin,\r\n    test_history: input.testHistory\r\n  };\r\n\r\n  try {\r\n    return await fetchApi<any>('/projects/save-history', {\r\n      method: 'POST',\r\n      body: JSON.stringify(apiInput),\r\n    });\r\n  } catch (error) {\r\n    throw new Error(`Failed to save test history: ${(error as Error).message}`);\r\n  }\r\n}\r\n\r\n// Uses the API endpoint for summarizing test results\r\nexport async function callSummarizeTestResults(input: SummarizeTestResultsInput): Promise<SummarizeTestResultsOutput> {\r\n  // Converting our input to match the API schema\r\n  const apiInput = {\r\n    test_results: input.testResults\r\n  };\r\n\r\n  try {\r\n    const result = await fetchApi<any>('/tests/summarize', {\r\n      method: 'POST',\r\n      body: JSON.stringify(apiInput),\r\n    });\r\n\r\n    return {\r\n      summary: result.summary || '',\r\n    };\r\n  } catch (error) {\r\n    throw new Error(`Failed to summarize test results: ${(error as Error).message}`);\r\n  }\r\n}\r\n\r\n// Prompt Management API Functions\r\nexport async function fetchPrompts(): Promise<any> {\r\n  try {\r\n    return await fetchApi<any>('/prompts/');\r\n  } catch (error) {\r\n    throw new Error(`Failed to fetch prompts: ${(error as Error).message}`);\r\n  }\r\n}\r\n\r\nexport async function fetchPromptDetail(category: string, promptId: string): Promise<any> {\r\n  try {\r\n    return await fetchApi<any>(`/prompts/${category}/${promptId}`);\r\n  } catch (error) {\r\n    throw new Error(`Failed to fetch prompt details: ${(error as Error).message}`);\r\n  }\r\n}\r\n\r\nexport async function updatePrompt(\r\n  category: string, \r\n  promptId: string, \r\n  data: {\r\n    content: Record<string, string>;\r\n    metadata?: Record<string, any>;\r\n    commit_message?: string;\r\n  }\r\n): Promise<any> {\r\n  try {\r\n    return await fetchApi<any>(`/prompts/${category}/${promptId}`, {\r\n      method: 'PUT',\r\n      body: JSON.stringify(data),\r\n    });\r\n  } catch (error) {\r\n    throw new Error(`Failed to update prompt: ${(error as Error).message}`);\r\n  }\r\n}\r\n\r\nexport async function createPrompt(data: {\r\n  category: string;\r\n  prompt_id: string;\r\n  name: string;\r\n  description: string;\r\n  languages?: string[];\r\n  content: Record<string, string>;\r\n  metadata?: Record<string, any>;\r\n}): Promise<any> {\r\n  try {\r\n    return await fetchApi<any>('/prompts/', {\r\n      method: 'POST',\r\n      body: JSON.stringify(data),\r\n    });\r\n  } catch (error) {\r\n    throw new Error(`Failed to create prompt: ${(error as Error).message}`);\r\n  }\r\n}\r\n\r\nexport async function deletePrompt(category: string, promptId: string): Promise<any> {\r\n  try {\r\n    return await fetchApi<any>(`/prompts/${category}/${promptId}`, {\r\n      method: 'DELETE',\r\n    });\r\n  } catch (error) {\r\n    throw new Error(`Failed to delete prompt: ${(error as Error).message}`);\r\n  }\r\n}\r\n\r\nexport async function validatePrompt(category: string, promptId: string): Promise<any> {\r\n  try {\r\n    return await fetchApi<any>(`/prompts/${category}/${promptId}/validate`, {\r\n      method: 'POST',\r\n    });\r\n  } catch (error) {\r\n    throw new Error(`Failed to validate prompt: ${(error as Error).message}`);\r\n  }\r\n}\r\n\r\nexport async function validateAllPrompts(): Promise<any> {\r\n  try {\r\n    return await fetchApi<any>('/prompts/validate-all', {\r\n      method: 'POST',\r\n    });\r\n  } catch (error) {\r\n    throw new Error(`Failed to validate all prompts: ${(error as Error).message}`);\r\n  }\r\n}\r\n\r\n// === CONFIGURATION API ENDPOINTS ===\r\n\r\n// Get all configurations (predefined + custom)\r\nexport const getAllConfigurations = (): Promise<{predefined: any[], custom: any[]}> => fetchApi<{predefined: any[], custom: any[]}>('/config/');\r\n\r\n// Get all predefined configurations\r\nexport const getPredefinedConfigurations = (): Promise<any[]> => fetchApi<any[]>('/config/predefined');\r\n\r\n// Get specific predefined configuration\r\nexport const getPredefinedConfiguration = (configType: string): Promise<any> => fetchApi<any>(`/config/predefined/${configType}`);\r\n\r\n// Get all custom configurations\r\nexport const getCustomConfigurations = (): Promise<any[]> => fetchApi<any[]>('/config/custom');\r\n\r\n// Get specific custom configuration\r\nexport const getCustomConfiguration = (configId: string): Promise<any> => fetchApi<any>(`/config/custom/${configId}`);\r\n\r\n// Create custom configuration\r\nexport const createCustomConfiguration = (data: any): Promise<any> => fetchApi<any>('/config/custom', { \r\n  method: 'POST', \r\n  body: JSON.stringify(data) \r\n});\r\n\r\n// Update custom configuration\r\nexport const updateCustomConfiguration = (configId: string, data: any): Promise<any> => fetchApi<any>(`/config/custom/${configId}`, { \r\n  method: 'PUT', \r\n  body: JSON.stringify(data) \r\n});\r\n\r\n// Delete custom configuration\r\nexport const deleteCustomConfiguration = (configId: string): Promise<any> => fetchApi<any>(`/config/custom/${configId}`, { \r\n  method: 'DELETE' \r\n});\r\n\r\n// Validate configuration\r\nexport const validateConfiguration = (data: any): Promise<any> => fetchApi<any>('/config/validate', { \r\n  method: 'POST', \r\n  body: JSON.stringify(data) \r\n});\r\n\r\n// Test configuration\r\nexport const testConfiguration = (data: any): Promise<any> => fetchApi<any>('/config/test', { \r\n  method: 'POST', \r\n  body: JSON.stringify(data) \r\n});\r\n\r\n// Get environment defaults\r\nexport const getEnvironmentDefaults = (): Promise<any> => fetchApi<any>('/config/defaults');\r\n\r\n// ==========================================\r\n// CodeGen API Functions\r\n// ==========================================\r\n\r\n// Start a new codegen session\r\nexport const startCodegenSession = (data: PlaywrightCodegenRequest): Promise<CodegenSessionInfo> => \r\n  fetchApi<CodegenSessionInfo>('/codegen/start', {\r\n    method: 'POST',\r\n    body: JSON.stringify(data)\r\n  });\r\n\r\n// Get session status\r\nexport const getCodegenSession = (sessionId: string): Promise<CodegenSessionInfo> => \r\n  fetchApi<CodegenSessionInfo>(`/codegen/session/${sessionId}`);\r\n\r\n// Stop session\r\nexport const stopCodegenSession = (sessionId: string): Promise<{ message: string; session_id: string }> => \r\n  fetchApi<{ message: string; session_id: string }>(`/codegen/session/${sessionId}/stop`, {\r\n    method: 'POST'\r\n  });\r\n\r\n// Get generated code\r\nexport const getCodegenGeneratedCode = (sessionId: string): Promise<{\r\n  session_id: string;\r\n  status: string;\r\n  generated_code?: string;\r\n  target_language: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}> => fetchApi(`/codegen/session/${sessionId}/code`);\r\n\r\n// Convert to test case\r\nexport const convertCodegenToTestcase = (data: CodegenTestCaseRequest): Promise<Record<string, any>> => \r\n  fetchApi<Record<string, any>>(`/codegen/session/${data.session_id}/convert`, {\r\n    method: 'POST',\r\n    body: JSON.stringify(data)\r\n  });\r\n\r\n// Cleanup session\r\nexport const cleanupCodegenSession = (sessionId: string): Promise<{ message: string; session_id: string }> => \r\n  fetchApi<{ message: string; session_id: string }>(`/codegen/session/${sessionId}`, {\r\n    method: 'DELETE'\r\n  });\r\n\r\n// List active sessions\r\nexport const listCodegenSessions = (): Promise<CodegenSessionListResponse> => \r\n  fetchApi<CodegenSessionListResponse>('/codegen/sessions');\r\n\r\n// Get statistics\r\nexport const getCodegenStats = (): Promise<CodegenStatsResponse> => \r\n  fetchApi<CodegenStatsResponse>('/codegen/stats');\r\n\r\n// Bulk cleanup\r\nexport const bulkCleanupCodegenSessions = (): Promise<{\r\n  message: string;\r\n  sessions_cleaned: number;\r\n  sessions_remaining: number;\r\n}> => fetchApi('/codegen/bulk-cleanup', { method: 'POST' });\r\n\r\n// Health check\r\nexport const getCodegenHealth = (): Promise<CodegenHealthResponse> => \r\n  fetchApi<CodegenHealthResponse>('/codegen/health');\r\n\r\n// History endpoints\r\nexport const getCodegenHistory = (limit?: number): Promise<CodegenHistoryResponse> => \r\n  fetchApi<CodegenHistoryResponse>(`/codegen/history${limit ? `?limit=${limit}` : ''}`);\r\n\r\nexport const getCodegenHistorySession = (sessionId: string): Promise<CodegenHistorySessionDetailResponse> => \r\n  fetchApi<CodegenHistorySessionDetailResponse>(`/codegen/history/${sessionId}`);\r\n\r\n// Execution endpoints\r\nexport const executeCodegenTest = (data: CodegenExecutionRequest): Promise<CodegenExecutionResponse> => \r\n  fetchApi<CodegenExecutionResponse>('/codegen/execute', {\r\n    method: 'POST',\r\n    body: JSON.stringify(data)\r\n  });\r\n\r\nexport const getCodegenExecution = (executionId: string): Promise<CodegenExecutionInfo> => \r\n  fetchApi<CodegenExecutionInfo>(`/codegen/execution/${executionId}`);\r\n\r\nexport const listCodegenExecutions = (): Promise<CodegenExecutionListResponse> => \r\n  fetchApi<CodegenExecutionListResponse>('/codegen/executions');\r\n\r\nexport const stopCodegenExecution = (executionId: string): Promise<{ message: string; execution_id: string }> => \r\n  fetchApi<{ message: string; execution_id: string }>(`/codegen/execution/${executionId}/stop`, {\r\n    method: 'POST'\r\n  });\r\n\r\nexport const cleanupCodegenExecution = (executionId: string): Promise<{ message: string; execution_id: string }> => \r\n  fetchApi<{ message: string; execution_id: string }>(`/codegen/execution/${executionId}`, {\r\n    method: 'DELETE'\r\n  });\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AA+BA,eAAe,SAAY,GAAW,EAAE,OAAqB;IAC3D,MAAM,WAAW,MAAM,MAAM,GAAG,uHAAA,CAAA,eAAY,GAAG,KAAK,EAAE;QACpD,GAAG,OAAO;QACV,SAAS;YACP,gBAAgB;YAChB,8BAA8B;YAC9B,GAAI,SAAS,OAAO;QACtB;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,IAAI;QACJ,IAAI,eAAe,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,UAAU,EAAE,KAAK;QAC3E,IAAI;YACF,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC;YACzC,IAAI,eAAe,YAAY,OAAO,CAAC,wBAAwB,CAAC,GAAG;gBACjE,YAAY,MAAM,SAAS,IAAI;gBAC/B,eAAe,WAAW,SAAS,WAAW,WAAW;YAC3D,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,yCAAyC;gBACzC,IAAI,UAAU,WAAW,GAAG,QAAQ,CAAC,mBAAmB;oBACpD,eAAe,GAAG,aAAa,+CAA+C,EAAE,UAAU,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC;gBACpH,OAAO;oBACH,eAAe,GAAG,aAAa,YAAY,EAAE,UAAU,SAAS,CAAC,GAAG,OAAO,UAAU,MAAM,GAAG,MAAM,QAAQ,IAAI;gBACpH;YACF;QACF,EAAE,OAAO,GAAG;YACV,+FAA+F;YAC/F,eAAe,GAAG,SAAS,UAAU,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,iCAAiC,CAAC;QACtI;QACA,MAAM,IAAI,MAAM;IAClB;IACA,kFAAkF;IAClF,IAAI,SAAS,MAAM,KAAK,KAAK;QAC3B,OAAO,CAAC,GAAQ,gDAAgD;IAClE;IACA,OAAO,SAAS,IAAI;AACtB;AAGO,MAAM,cAAc,IAAqC,SAA+B;AACxF,MAAM,gBAAgB,CAAC,OAA+C,SAAkB,cAAc;QAAE,QAAQ;QAAQ,MAAM,KAAK,SAAS,CAAC;IAAM;AACnJ,MAAM,iBAAiB,CAAC,YAAwC,SAAkB,CAAC,UAAU,EAAE,WAAW;AAC1G,MAAM,gBAAgB,CAAC,WAAmB,OAA+C,SAAkB,CAAC,UAAU,EAAE,WAAW,EAAE;QAAE,QAAQ;QAAO,MAAM,KAAK,SAAS,CAAC;IAAM;AACjL,MAAM,gBAAgB,CAAC,YAAqC,SAAe,CAAC,UAAU,EAAE,WAAW,EAAE;QAAE,QAAQ;IAAS;AAGxH,MAAM,uBAAuB,CAAC,YAAuD,SAAiC,CAAC,UAAU,EAAE,UAAU,OAAO,CAAC;AACrJ,MAAM,cAAc,CAAC,WAAmB,OAAmD,SAAoB,CAAC,UAAU,EAAE,UAAU,QAAQ,CAAC,EAAE;QAAE,QAAQ;QAAQ,MAAM,KAAK,SAAS,CAAC;IAAM;AAC9L,MAAM,eAAe,CAAC,WAAmB,UAAwC,SAAoB,CAAC,UAAU,EAAE,UAAU,QAAQ,EAAE,SAAS;AAC/I,MAAM,cAAc,CAAC,WAAmB,SAAiB,OAAmD,SAAoB,CAAC,UAAU,EAAE,UAAU,QAAQ,EAAE,SAAS,EAAE;QAAE,QAAQ;QAAO,MAAM,KAAK,SAAS,CAAC;IAAM;AACxN,MAAM,cAAc,CAAC,WAAmB,UAAmC,SAAe,CAAC,UAAU,EAAE,UAAU,QAAQ,EAAE,SAAS,EAAE;QAAE,QAAQ;IAAS;AACzJ,MAAM,eAAe,CAAC,WAAmB,UAAqD,SAAiC,CAAC,UAAU,EAAE,UAAU,QAAQ,EAAE,QAAQ,QAAQ,CAAC,EAAE;QAAE,QAAQ;IAAO;AAGpM,MAAM,wBAAwB,CAAC,WAAmB,UAAoD,SAAgC,CAAC,UAAU,EAAE,UAAU,QAAQ,EAAE,QAAQ,MAAM,CAAC;AACtL,MAAM,iBAAiB,CAAC,WAAmB,SAAiB,OAAiD,SAAmB,CAAC,UAAU,EAAE,UAAU,QAAQ,EAAE,QAAQ,OAAO,CAAC,EAAE;QAAE,QAAQ;QAAQ,MAAM,KAAK,SAAS,CAAC;IAAM;AAChO,MAAM,kBAAkB,CAAC,WAAmB,SAAiB,SAAsC,SAAmB,CAAC,UAAU,EAAE,UAAU,QAAQ,EAAE,QAAQ,OAAO,EAAE,QAAQ;AAChL,MAAM,iBAAiB,CAAC,WAAmB,SAAiB,QAAgB,OAAiD,SAAmB,CAAC,UAAU,EAAE,UAAU,QAAQ,EAAE,QAAQ,OAAO,EAAE,QAAQ,EAAE;QAAE,QAAQ;QAAO,MAAM,KAAK,SAAS,CAAC;IAAM;AACxP,MAAM,uBAAuB,CAAC,WAAmB,SAAiB,QAAgB,OAAuD,SAAmB,CAAC,UAAU,EAAE,UAAU,QAAQ,EAAE,QAAQ,OAAO,EAAE,OAAO,OAAO,CAAC,EAAE;QAAE,QAAQ;QAAS,MAAM,KAAK,SAAS,CAAC;IAAM;AAC7Q,MAAM,iBAAiB,CAAC,WAAmB,SAAiB,SAAkC,SAAe,CAAC,UAAU,EAAE,UAAU,QAAQ,EAAE,QAAQ,OAAO,EAAE,QAAQ,EAAE;QAAE,QAAQ;IAAS;AAC5L,MAAM,kBAAkB,CAAC,WAAmB,SAAiB,SAAuD,SAAoC,CAAC,UAAU,EAAE,UAAU,QAAQ,EAAE,QAAQ,OAAO,EAAE,OAAO,QAAQ,CAAC,EAAE;QAAE,QAAQ;IAAO;AAG7O,MAAM,6BAA6B,OAAO,WAAmB,SAAiB;IACnF,mCAAmC;IACnC,MAAM,WAAW,MAAM,gBAAgB,WAAW,SAAS;IAE3D,8CAA8C;IAC9C,MAAM,iBAAwC;QAC5C,cAAc,SAAS,aAAa,IAAI;QACxC,SAAS,SAAS,GAAG,IAAI;QACzB,WAAW,SAAS,mBAAmB,IAAI;IAC7C;IAEA,0CAA0C;IAC1C,OAAO,qBAAqB;AAC9B;AAGO,MAAM,eAAe,IAA0B,SAAoB;AAEnE,MAAM,iCAAiC,OAAO;IACnD,mFAAmF;IACnF,MAAM,WAAW,MAAM,SAAmC,CAAC,SAAS,EAAE,mBAAmB,cAAc;IACvG,OAAO;AACT;AAMO,eAAe,qBAAqB,KAA4B;IACrE,+CAA+C;IAC/C,MAAM,WAAW;QACf,YAAY,MAAM,SAAS;QAC3B,UAAU,MAAM,QAAQ;IAC1B;IAEA,IAAI;QACF,gCAAgC;QAChC,MAAM,SAAS,MAAM,SAAc,oBAAoB;YACrD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,OAAO;YACL,mBAAmB,OAAO,cAAc,IAAI,OAAO,iBAAiB,IAAI;QAC1E;IACF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,AAAC,MAAgB,OAAO,EAAE;IAC7E;AACF;AAEO,eAAe,4BAA4B,KAAmC;IACnF,+CAA+C;IAC/C,MAAM,WAAW;QACf,gBAAgB,MAAM,SAAS;QAC/B,UAAU,MAAM,QAAQ;IAC1B;IAEA,IAAI;QACF,gCAAgC;QAChC,MAAM,SAAS,MAAM,SAAc,kCAAkC;YACnE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB,IAAO,qDAAqD;QAC5D,IAAI,kBAAoC,EAAE;QAE1C,iDAAiD;QACjD,IAAI,UAAU,OAAO,YAAY,EAAE;YACjC,IAAI,MAAM,OAAO,CAAC,OAAO,YAAY,GAAG;gBACtC,yEAAyE;gBACzE,IAAI,OAAO,YAAY,CAAC,MAAM,GAAG,KAAK,OAAO,OAAO,YAAY,CAAC,EAAE,KAAK,UAAU;oBAChF,2DAA2D;oBAC3D,kBAAkB,OAAO,YAAY;gBACvC,OAAO;oBACL,sCAAsC;oBACtC,kBAAkB,OAAO,YAAY;gBACvC;YACF,OAAO,IAAI,OAAO,OAAO,YAAY,KAAK,UAAU;gBAClD,gDAAgD;gBAChD,IAAI;oBACF,sBAAsB;oBACtB,MAAM,SAAS,KAAK,KAAK,CAAC,OAAO,YAAY;oBAC7C,IAAI,MAAM,OAAO,CAAC,SAAS;wBACzB,IAAI,OAAO,MAAM,GAAG,KAAK,OAAO,MAAM,CAAC,EAAE,KAAK,UAAU;4BACtD,qCAAqC;4BACrC,kBAAkB;wBACpB,OAAO;4BACL,mBAAmB;4BACnB,kBAAkB;wBACpB;oBACF,OAAO;wBACL,+CAA+C;wBAC/C,kBAAkB;4BAAC;yBAAO;oBAC5B;gBACF,EAAE,OAAO,GAAG;oBACV,gCAAgC;oBAChC,kBAAkB,OAAO,YAAY,CAAC,KAAK,CAAC,MAAM,MAAM,CAAC,CAAC,OAAiB,KAAK,IAAI,GAAG,MAAM,GAAG;gBAClG;YACF;QACF,OAAO,IAAI,UAAU,MAAM,OAAO,CAAC,OAAO,eAAe,GAAG;YAC1D,yBAAyB;YACzB,kBAAkB,OAAO,eAAe;QAC1C,OAAO,IAAI,UAAU,OAAO,WAAW,UAAU;YAC/C,iCAAiC;YACjC,kBAAkB;gBAAC;aAAO;QAC5B;QAEA,OAAO;YAAE;QAAgB;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM,IAAI,MAAM,CAAC,sCAAsC,EAAE,AAAC,MAAgB,OAAO,EAAE;IACrF;AACF;AAGO,eAAe,oBAAoB,KAA2B;IACnE,gDAAgD;IAChD,IAAI,MAAM,YAAY,EAAE;QACtB,IAAI;YACF,yEAAyE;YACzE,MAAM,WAAW;gBACf,cAAc,MAAM,OAAO,CAAC,MAAM,YAAY,IAC1C,MAAM,YAAY,CAAC,IAAI,CAAC,QACxB,MAAM,YAAY;gBACtB,UAAU,MAAM,QAAQ;YAC1B;YAEA,MAAM,SAAS,MAAM,SAAc,6BAA6B;gBAC9D,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,qDAAqD;YACrD,OAAO;gBACL,SAAS,OAAO,OAAO,IAAI,OAAO,gBAAgB,IAAI;YACxD;QACF,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,AAAC,MAAgB,OAAO,EAAE;QAC3E;IACF,OAEK;QACH,IAAI;YACF,gEAAgE;YAChE,MAAM,WAAW;gBACf,cAAc,MAAM,SAAS,IAAI;gBACjC,KAAK,MAAM,GAAG,IAAI;gBAClB,YAAY,MAAM,SAAS,IAAI;gBAC/B,UAAU,MAAM,QAAQ;YAC1B;YAEA,MAAM,SAAS,MAAM,SAAc,qBAAqB;gBACtD,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,qDAAqD;YACrD,OAAO;gBACL,SAAS,OAAO,OAAO,IAAI,OAAO,gBAAgB,IAAI;YACxD;QACF,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,AAAC,MAAgB,OAAO,EAAE;QAC3E;IACF;AACF;AAIO,eAAe,iBAAiB,KAAwB;IAC7D,IAAI;QACF,MAAM,WAAW;YACf,WAAW,MAAM,SAAS;YAC1B,kBAAkB,MAAM,gBAAgB;YACxC,cAAc,MAAM,YAAY,IAAI,CAAC;QACvC;QAEA,MAAM,SAAS,MAAM,SAAc,kBAAkB;YACnD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,OAAO;YACL,MAAM,OAAO,IAAI,IAAI;QACvB;IACF,EAAE,OAAO,OAAO;QACd,IAAI,eAAe,CAAC,yBAAyB,EAAE,AAAC,MAAgB,OAAO,EAAE;QACzE,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,eAAe,qBAAqB,KAA4B;IACrE,MAAM,WAAW;QACf,cAAc,MAAM,YAAY;QAChC,KAAK,MAAM,OAAO;QAClB,YAAY,MAAM,SAAS;QAC3B,WAAW,MAAM,QAAQ;QACzB,eAAe,MAAM,aAAa;IACpC;IAEA,IAAI;QACF,2CAA2C;QAC3C,MAAM,SAAS,MAAM,SAAc,gBAAgB;YACjD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,8FAA8F;QAC9F,MAAM,gBAAwC;YAC5C,SAAS,OAAO,OAAO,IAAI,EAAE;YAC7B,SAAS,OAAO,OAAO,IAAI,EAAE;YAC7B,UAAU,OAAO,QAAQ,IAAI,EAAE;YAC/B,MAAM,OAAO,IAAI,IAAI,EAAE;YACvB,QAAQ,OAAO,MAAM,IAAI,EAAE;YAC3B,aAAa,OAAO,WAAW,IAAI,EAAE;YACrC,UAAU;gBACR,YAAY,OAAO,UAAU,IAAI,IAAI,OAAO,WAAW;gBACvD,UAAU,OAAO,QAAQ,IAAI,IAAI,OAAO,WAAW;gBACnD,aAAa,OAAO,WAAW,IAAI;gBACnC,SAAS,OAAO,OAAO,KAAK,YAAY,OAAO,OAAO,GAAG;YAC3D;YACA,kBAAkB,OAAO,iBAAiB,IAAI,OAAO,gBAAgB,IAAI;YACzE,WAAW,MAAM,SAAS;QAC5B;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,AAAC,MAAgB,OAAO,EAAE;IAC7E;AACF;AAGO,eAAe,oBAAoB,KAAwC;IAChF,8DAA8D;IAC9D,MAAM,WAAW;QACf,kBAAkB,MAAM,OAAO;QAC/B,KAAK,MAAM,GAAG;IAChB;IAEA,IAAI;QACF,0CAA0C;QAC1C,MAAM,SAAS,MAAM,SAAc,eAAe;YAChD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,8FAA8F;QAC9F,MAAM,gBAAwC;YAC5C,SAAS,OAAO,OAAO,IAAI,EAAE;YAC7B,SAAS,OAAO,OAAO,IAAI,EAAE;YAC7B,UAAU,OAAO,QAAQ,IAAI,EAAE;YAC/B,MAAM,OAAO,IAAI,IAAI,EAAE;YACvB,QAAQ,OAAO,MAAM,IAAI,EAAE;YAC3B,aAAa,OAAO,WAAW,IAAI,EAAE;YACrC,UAAU;gBACR,YAAY,OAAO,UAAU,IAAI,IAAI,OAAO,WAAW;gBACvD,UAAU,OAAO,QAAQ,IAAI,IAAI,OAAO,WAAW;gBACnD,aAAa,OAAO,WAAW,IAAI;gBACnC,SAAS,OAAO,OAAO,KAAK,YAAY,OAAO,OAAO,GAAG;YAC3D;YACA,kBAAkB,MAAM,OAAO,CAAC,mDAAmD;QACrF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,AAAC,MAAgB,OAAO,EAAE;IAC5E;AACF;AAGO,eAAe,gBAAgB,KAOrC;IACC,4CAA4C;IAC5C,MAAM,WAAW;QACf,YAAY,MAAM,SAAS;QAC3B,UAAU,MAAM,OAAO;QACvB,MAAM,MAAM,IAAI;QAChB,aAAa,MAAM,WAAW;QAC9B,SAAS,MAAM,OAAO;QACtB,cAAc,MAAM,WAAW;IACjC;IAEA,IAAI;QACF,OAAO,MAAM,SAAc,0BAA0B;YACnD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,AAAC,MAAgB,OAAO,EAAE;IAC5E;AACF;AAGO,eAAe,yBAAyB,KAAgC;IAC7E,+CAA+C;IAC/C,MAAM,WAAW;QACf,cAAc,MAAM,WAAW;IACjC;IAEA,IAAI;QACF,MAAM,SAAS,MAAM,SAAc,oBAAoB;YACrD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,OAAO;YACL,SAAS,OAAO,OAAO,IAAI;QAC7B;IACF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,AAAC,MAAgB,OAAO,EAAE;IACjF;AACF;AAGO,eAAe;IACpB,IAAI;QACF,OAAO,MAAM,SAAc;IAC7B,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,AAAC,MAAgB,OAAO,EAAE;IACxE;AACF;AAEO,eAAe,kBAAkB,QAAgB,EAAE,QAAgB;IACxE,IAAI;QACF,OAAO,MAAM,SAAc,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,UAAU;IAC/D,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,gCAAgC,EAAE,AAAC,MAAgB,OAAO,EAAE;IAC/E;AACF;AAEO,eAAe,aACpB,QAAgB,EAChB,QAAgB,EAChB,IAIC;IAED,IAAI;QACF,OAAO,MAAM,SAAc,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,UAAU,EAAE;YAC7D,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,AAAC,MAAgB,OAAO,EAAE;IACxE;AACF;AAEO,eAAe,aAAa,IAQlC;IACC,IAAI;QACF,OAAO,MAAM,SAAc,aAAa;YACtC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,AAAC,MAAgB,OAAO,EAAE;IACxE;AACF;AAEO,eAAe,aAAa,QAAgB,EAAE,QAAgB;IACnE,IAAI;QACF,OAAO,MAAM,SAAc,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,UAAU,EAAE;YAC7D,QAAQ;QACV;IACF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,AAAC,MAAgB,OAAO,EAAE;IACxE;AACF;AAEO,eAAe,eAAe,QAAgB,EAAE,QAAgB;IACrE,IAAI;QACF,OAAO,MAAM,SAAc,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,SAAS,SAAS,CAAC,EAAE;YACtE,QAAQ;QACV;IACF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,AAAC,MAAgB,OAAO,EAAE;IAC1E;AACF;AAEO,eAAe;IACpB,IAAI;QACF,OAAO,MAAM,SAAc,yBAAyB;YAClD,QAAQ;QACV;IACF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,gCAAgC,EAAE,AAAC,MAAgB,OAAO,EAAE;IAC/E;AACF;AAKO,MAAM,uBAAuB,IAAmD,SAA6C;AAG7H,MAAM,8BAA8B,IAAsB,SAAgB;AAG1E,MAAM,6BAA6B,CAAC,aAAqC,SAAc,CAAC,mBAAmB,EAAE,YAAY;AAGzH,MAAM,0BAA0B,IAAsB,SAAgB;AAGtE,MAAM,yBAAyB,CAAC,WAAmC,SAAc,CAAC,eAAe,EAAE,UAAU;AAG7G,MAAM,4BAA4B,CAAC,OAA4B,SAAc,kBAAkB;QACpG,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AAGO,MAAM,4BAA4B,CAAC,UAAkB,OAA4B,SAAc,CAAC,eAAe,EAAE,UAAU,EAAE;QAClI,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AAGO,MAAM,4BAA4B,CAAC,WAAmC,SAAc,CAAC,eAAe,EAAE,UAAU,EAAE;QACvH,QAAQ;IACV;AAGO,MAAM,wBAAwB,CAAC,OAA4B,SAAc,oBAAoB;QAClG,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AAGO,MAAM,oBAAoB,CAAC,OAA4B,SAAc,gBAAgB;QAC1F,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AAGO,MAAM,yBAAyB,IAAoB,SAAc;AAOjE,MAAM,sBAAsB,CAAC,OAClC,SAA6B,kBAAkB;QAC7C,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AAGK,MAAM,oBAAoB,CAAC,YAChC,SAA6B,CAAC,iBAAiB,EAAE,WAAW;AAGvD,MAAM,qBAAqB,CAAC,YACjC,SAAkD,CAAC,iBAAiB,EAAE,UAAU,KAAK,CAAC,EAAE;QACtF,QAAQ;IACV;AAGK,MAAM,0BAA0B,CAAC,YAOlC,SAAS,CAAC,iBAAiB,EAAE,UAAU,KAAK,CAAC;AAG5C,MAAM,2BAA2B,CAAC,OACvC,SAA8B,CAAC,iBAAiB,EAAE,KAAK,UAAU,CAAC,QAAQ,CAAC,EAAE;QAC3E,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AAGK,MAAM,wBAAwB,CAAC,YACpC,SAAkD,CAAC,iBAAiB,EAAE,WAAW,EAAE;QACjF,QAAQ;IACV;AAGK,MAAM,sBAAsB,IACjC,SAAqC;AAGhC,MAAM,kBAAkB,IAC7B,SAA+B;AAG1B,MAAM,6BAA6B,IAIpC,SAAS,yBAAyB;QAAE,QAAQ;IAAO;AAGlD,MAAM,mBAAmB,IAC9B,SAAgC;AAG3B,MAAM,oBAAoB,CAAC,QAChC,SAAiC,CAAC,gBAAgB,EAAE,QAAQ,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI;AAE/E,MAAM,2BAA2B,CAAC,YACvC,SAA8C,CAAC,iBAAiB,EAAE,WAAW;AAGxE,MAAM,qBAAqB,CAAC,OACjC,SAAmC,oBAAoB;QACrD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AAEK,MAAM,sBAAsB,CAAC,cAClC,SAA+B,CAAC,mBAAmB,EAAE,aAAa;AAE7D,MAAM,wBAAwB,IACnC,SAAuC;AAElC,MAAM,uBAAuB,CAAC,cACnC,SAAoD,CAAC,mBAAmB,EAAE,YAAY,KAAK,CAAC,EAAE;QAC5F,QAAQ;IACV;AAEK,MAAM,0BAA0B,CAAC,cACtC,SAAoD,CAAC,mBAAmB,EAAE,aAAa,EAAE;QACvF,QAAQ;IACV", "debugId": null}}, {"offset": {"line": 575, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border bg-card text-card-foreground shadow-sm relative\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nconst DevCard = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, children, ...props }, ref) => (\r\n  <Card ref={ref} className={cn(\"flex flex-col\", className)} {...props}>\r\n    {children}\r\n\r\n  </Card>\r\n))\r\nDevCard.displayName = \"DevCard\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent, DevCard }\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qEACA;QAED,GAAG,KAAK;;;;;;KAVP;AAaN,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,wBAAU,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAG7B,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;QAAK,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAAa,GAAG,KAAK;kBACjE;;;;;;;AAIL,QAAQ,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 692, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\r\n        outline: \"text-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 740, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-background text-foreground\",\r\n        destructive:\r\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Alert = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\r\n>(({ className, variant, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    role=\"alert\"\r\n    className={cn(alertVariants({ variant }), className)}\r\n    {...props}\r\n  />\r\n))\r\nAlert.displayName = \"Alert\"\r\n\r\nconst AlertTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h5\r\n    ref={ref}\r\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertTitle.displayName = \"AlertTitle\"\r\n\r\nconst AlertDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDescription.displayName = \"AlertDescription\"\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,6JACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,6LAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 817, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/app/projects/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from 'next/link';\r\nimport { useQuery } from '@tanstack/react-query';\r\nimport { getProjects } from '@/lib/api';\r\nimport type { Project } from '@/lib/types';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Card, DevCard, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\nimport { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';\r\nimport { FolderKanban, PlusCircle, AlertCircle } from 'lucide-react';\r\nimport { format } from 'date-fns';\r\n\r\nfunction ProjectCard({ project }: { project: Project }) {\r\n  return (\r\n    <DevCard className=\"flex flex-col\">\r\n      <CardHeader>\r\n        <CardTitle className=\"flex items-center gap-2\">\r\n          <FolderKanban className=\"h-6 w-6 text-primary\" />\r\n          {project.name}\r\n        </CardTitle>\r\n        <CardDescription className=\"line-clamp-2\">{project.description}</CardDescription>\r\n      </CardHeader>\r\n      <CardContent className=\"flex-grow\">\r\n        <div className=\"mb-2\">\r\n          <span className=\"text-sm font-semibold\">Tags:</span>\r\n          <div className=\"flex flex-wrap gap-1 mt-1\">\r\n            {project.tags.length > 0 ? (\r\n              project.tags.map((tag) => <Badge key={tag} variant=\"secondary\">{tag}</Badge>)\r\n            ) : (\r\n              <span className=\"text-xs text-muted-foreground\">No tags</span>\r\n            )}\r\n          </div>\r\n        </div>\r\n        <p className=\"text-xs text-muted-foreground\">\r\n          Last updated: {format(new Date(project.updated_at), 'PPP')}\r\n        </p>\r\n      </CardContent>\r\n      <CardFooter>\r\n        <Button asChild size=\"sm\" className=\"w-full\">\r\n          <Link href={`/projects/${project.project_id}`}>View Project</Link>\r\n        </Button>\r\n      </CardFooter>\r\n    </DevCard>\r\n  );\r\n}\r\n\r\nfunction ProjectListSkeleton() {\r\n  return (\r\n    <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\">\r\n      {[...Array(3)].map((_, i) => (\r\n        <Card key={i}>\r\n          <CardHeader>\r\n            <Skeleton className=\"h-6 w-3/4 mb-2\" />\r\n            <Skeleton className=\"h-4 w-full\" />\r\n            <Skeleton className=\"h-4 w-2/3 mt-1\" />\r\n          </CardHeader>\r\n          <CardContent>\r\n            <Skeleton className=\"h-4 w-1/4 mb-2\" />\r\n            <div className=\"flex gap-1 mt-1\">\r\n              <Skeleton className=\"h-5 w-12 rounded-full\" />\r\n              <Skeleton className=\"h-5 w-16 rounded-full\" />\r\n            </div>\r\n            <Skeleton className=\"h-3 w-1/2 mt-3\" />\r\n          </CardContent>\r\n          <CardFooter>\r\n            <Skeleton className=\"h-9 w-full\" />\r\n          </CardFooter>\r\n        </Card>\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default function ProjectsPage() {\r\n  const { data: projectsResponse, isLoading, error, isError } = useQuery({\r\n    queryKey: ['projects'],\r\n    queryFn: getProjects,\r\n  });\r\n\r\n  const projects = projectsResponse?.items || [];\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"flex justify-between items-center mb-6\">\r\n        <h1 className=\"page-header mb-0\">Projects</h1>\r\n        <Button asChild>\r\n          <Link href=\"/projects/create\">\r\n            <PlusCircle className=\"mr-2 h-4 w-4\" /> Create Project\r\n          </Link>\r\n        </Button>\r\n      </div>\r\n\r\n      {isLoading && <ProjectListSkeleton />}\r\n      {isError && (\r\n        <Alert variant=\"destructive\">\r\n          <AlertCircle className=\"h-4 w-4\" />\r\n          <AlertTitle>Error fetching projects</AlertTitle>\r\n          <AlertDescription>{error?.message || 'An unknown error occurred.'}</AlertDescription>\r\n        </Alert>\r\n      )}\r\n      {!isLoading && !isError && projects.length === 0 && (\r\n        <DevCard className=\"text-center py-12\">\r\n          <CardHeader>\r\n            <FolderKanban className=\"mx-auto h-12 w-12 text-muted-foreground\" />\r\n            <CardTitle className=\"mt-4\">No Projects Yet</CardTitle>\r\n          </CardHeader>\r\n          <CardContent>\r\n            <CardDescription>Get started by creating your first project.</CardDescription>\r\n          </CardContent>\r\n          <CardFooter className=\"justify-center\">\r\n             <Button asChild>\r\n                <Link href=\"/projects/create\">\r\n                  <PlusCircle className=\"mr-2 h-4 w-4\" /> Create Project\r\n                </Link>\r\n              </Button>\r\n          </CardFooter>\r\n        </DevCard>\r\n      )}\r\n      {!isLoading && !isError && projects.length > 0 && (\r\n        <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\">\r\n          {projects.map((project) => (\r\n            <ProjectCard key={project.project_id} project={project} />\r\n          ))}\r\n        </div>\r\n      )}\r\n\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;;;AAZA;;;;;;;;;;;AAcA,SAAS,YAAY,EAAE,OAAO,EAAwB;IACpD,qBACE,6LAAC,mIAAA,CAAA,UAAO;QAAC,WAAU;;0BACjB,6LAAC,mIAAA,CAAA,aAAU;;kCACT,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;4BACvB,QAAQ,IAAI;;;;;;;kCAEf,6LAAC,mIAAA,CAAA,kBAAe;wBAAC,WAAU;kCAAgB,QAAQ,WAAW;;;;;;;;;;;;0BAEhE,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAwB;;;;;;0CACxC,6LAAC;gCAAI,WAAU;0CACZ,QAAQ,IAAI,CAAC,MAAM,GAAG,IACrB,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,oBAAQ,6LAAC,oIAAA,CAAA,QAAK;wCAAW,SAAQ;kDAAa;uCAA1B;;;;8DAEtC,6LAAC;oCAAK,WAAU;8CAAgC;;;;;;;;;;;;;;;;;kCAItD,6LAAC;wBAAE,WAAU;;4BAAgC;4BAC5B,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,QAAQ,UAAU,GAAG;;;;;;;;;;;;;0BAGxD,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,OAAO;oBAAC,MAAK;oBAAK,WAAU;8BAClC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,UAAU,EAAE;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAKzD;KAhCS;AAkCT,SAAS;IACP,qBACE,6LAAC;QAAI,WAAU;kBACZ;eAAI,MAAM;SAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,uIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC,uIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC,uIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;kCAEtB,6LAAC,mIAAA,CAAA,cAAW;;0CACV,6LAAC,uIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC,uIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAEtB,6LAAC,uIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;kCAEtB,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,uIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;eAfb;;;;;;;;;;AAqBnB;MAzBS;AA2BM,SAAS;;IACtB,MAAM,EAAE,MAAM,gBAAgB,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACrE,UAAU;YAAC;SAAW;QACtB,SAAS,oHAAA,CAAA,cAAW;IACtB;IAEA,MAAM,WAAW,kBAAkB,SAAS,EAAE;IAE9C,qBACE,6LAAC;;0BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAmB;;;;;;kCACjC,6LAAC,qIAAA,CAAA,SAAM;wBAAC,OAAO;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;;8CACT,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;YAK5C,2BAAa,6LAAC;;;;;YACd,yBACC,6LAAC,oIAAA,CAAA,QAAK;gBAAC,SAAQ;;kCACb,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC,oIAAA,CAAA,aAAU;kCAAC;;;;;;kCACZ,6LAAC,oIAAA,CAAA,mBAAgB;kCAAE,OAAO,WAAW;;;;;;;;;;;;YAGxC,CAAC,aAAa,CAAC,WAAW,SAAS,MAAM,KAAK,mBAC7C,6LAAC,mIAAA,CAAA,UAAO;gBAAC,WAAU;;kCACjB,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;0CACxB,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAO;;;;;;;;;;;;kCAE9B,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC,mIAAA,CAAA,kBAAe;sCAAC;;;;;;;;;;;kCAEnB,6LAAC,mIAAA,CAAA,aAAU;wBAAC,WAAU;kCACnB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BAAC,OAAO;sCACZ,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;;kDACT,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;YAMlD,CAAC,aAAa,CAAC,WAAW,SAAS,MAAM,GAAG,mBAC3C,6LAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;wBAAqC,SAAS;uBAA7B,QAAQ,UAAU;;;;;;;;;;;;;;;;AAOhD;GAvDwB;;QACwC,8KAAA,CAAA,WAAQ;;;MADhD", "debugId": null}}]}