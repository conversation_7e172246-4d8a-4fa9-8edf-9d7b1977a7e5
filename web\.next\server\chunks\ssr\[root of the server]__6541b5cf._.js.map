{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/lib/query-provider.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport type { PropsWithChildren } from 'react';\r\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\r\nimport { ReactQueryDevtools } from '@tanstack/react-query-devtools';\r\nimport React from 'react';\r\n\r\nexport function Providers({ children }: PropsWithChildren) {\r\n  const [client] = React.useState(() => new QueryClient({\r\n    defaultOptions: {\r\n      queries: {\r\n        staleTime: 5 * 60 * 1000, // 5 minutes\r\n        refetchOnWindowFocus: false, // Optional: disable refetch on window focus\r\n      },\r\n    },\r\n  }));\r\n\r\n  return (\r\n    <QueryClientProvider client={client}>\r\n      {children}\r\n      <ReactQueryDevtools initialIsOpen={false} />\r\n    </QueryClientProvider>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AACA;AALA;;;;;AAOO,SAAS,UAAU,EAAE,QAAQ,EAAqB;IACvD,MAAM,CAAC,OAAO,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,IAAM,IAAI,6KAAA,CAAA,cAAW,CAAC;YACpD,gBAAgB;gBACd,SAAS;oBACP,WAAW,IAAI,KAAK;oBACpB,sBAAsB;gBACxB;YACF;QACF;IAEA,qBACE,8OAAC,sLAAA,CAAA,sBAAmB;QAAC,QAAQ;;YAC1B;0BACD,8OAAC,oLAAA,CAAA,qBAAkB;gBAAC,eAAe;;;;;;;;;;;;AAGzC", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/hooks/use-toast.ts"], "sourcesContent": ["\"use client\"\r\n\r\n// Inspired by react-hot-toast library\r\nimport * as React from \"react\"\r\n\r\nimport type {\r\n  ToastActionElement,\r\n  ToastProps,\r\n} from \"@/components/ui/toast\"\r\n\r\nconst TOAST_LIMIT = 1\r\nconst TOAST_REMOVE_DELAY = 1000000\r\n\r\ntype ToasterToast = ToastProps & {\r\n  id: string\r\n  title?: React.ReactNode\r\n  description?: React.ReactNode\r\n  action?: ToastActionElement\r\n}\r\n\r\nconst actionTypes = {\r\n  ADD_TOAST: \"ADD_TOAST\",\r\n  UPDATE_TOAST: \"UPDATE_TOAST\",\r\n  DISMISS_TOAST: \"DISMISS_TOAST\",\r\n  REMOVE_TOAST: \"REMOVE_TOAST\",\r\n} as const\r\n\r\nlet count = 0\r\n\r\nfunction genId() {\r\n  count = (count + 1) % Number.MAX_SAFE_INTEGER\r\n  return count.toString()\r\n}\r\n\r\ntype ActionType = typeof actionTypes\r\n\r\ntype Action =\r\n  | {\r\n      type: ActionType[\"ADD_TOAST\"]\r\n      toast: ToasterToast\r\n    }\r\n  | {\r\n      type: ActionType[\"UPDATE_TOAST\"]\r\n      toast: Partial<ToasterToast>\r\n    }\r\n  | {\r\n      type: ActionType[\"DISMISS_TOAST\"]\r\n      toastId?: ToasterToast[\"id\"]\r\n    }\r\n  | {\r\n      type: ActionType[\"REMOVE_TOAST\"]\r\n      toastId?: ToasterToast[\"id\"]\r\n    }\r\n\r\ninterface State {\r\n  toasts: ToasterToast[]\r\n}\r\n\r\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()\r\n\r\nconst addToRemoveQueue = (toastId: string) => {\r\n  if (toastTimeouts.has(toastId)) {\r\n    return\r\n  }\r\n\r\n  const timeout = setTimeout(() => {\r\n    toastTimeouts.delete(toastId)\r\n    dispatch({\r\n      type: \"REMOVE_TOAST\",\r\n      toastId: toastId,\r\n    })\r\n  }, TOAST_REMOVE_DELAY)\r\n\r\n  toastTimeouts.set(toastId, timeout)\r\n}\r\n\r\nexport const reducer = (state: State, action: Action): State => {\r\n  switch (action.type) {\r\n    case \"ADD_TOAST\":\r\n      return {\r\n        ...state,\r\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\r\n      }\r\n\r\n    case \"UPDATE_TOAST\":\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.map((t) =>\r\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\r\n        ),\r\n      }\r\n\r\n    case \"DISMISS_TOAST\": {\r\n      const { toastId } = action\r\n\r\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\r\n      // but I'll keep it here for simplicity\r\n      if (toastId) {\r\n        addToRemoveQueue(toastId)\r\n      } else {\r\n        state.toasts.forEach((toast) => {\r\n          addToRemoveQueue(toast.id)\r\n        })\r\n      }\r\n\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.map((t) =>\r\n          t.id === toastId || toastId === undefined\r\n            ? {\r\n                ...t,\r\n                open: false,\r\n              }\r\n            : t\r\n        ),\r\n      }\r\n    }\r\n    case \"REMOVE_TOAST\":\r\n      if (action.toastId === undefined) {\r\n        return {\r\n          ...state,\r\n          toasts: [],\r\n        }\r\n      }\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\r\n      }\r\n  }\r\n}\r\n\r\nconst listeners: Array<(state: State) => void> = []\r\n\r\nlet memoryState: State = { toasts: [] }\r\n\r\nfunction dispatch(action: Action) {\r\n  memoryState = reducer(memoryState, action)\r\n  listeners.forEach((listener) => {\r\n    listener(memoryState)\r\n  })\r\n}\r\n\r\ntype Toast = Omit<ToasterToast, \"id\">\r\n\r\nfunction toast({ ...props }: Toast) {\r\n  const id = genId()\r\n\r\n  const update = (props: ToasterToast) =>\r\n    dispatch({\r\n      type: \"UPDATE_TOAST\",\r\n      toast: { ...props, id },\r\n    })\r\n  const dismiss = () => dispatch({ type: \"DISMISS_TOAST\", toastId: id })\r\n\r\n  dispatch({\r\n    type: \"ADD_TOAST\",\r\n    toast: {\r\n      ...props,\r\n      id,\r\n      open: true,\r\n      onOpenChange: (open) => {\r\n        if (!open) dismiss()\r\n      },\r\n    },\r\n  })\r\n\r\n  return {\r\n    id: id,\r\n    dismiss,\r\n    update,\r\n  }\r\n}\r\n\r\nfunction useToast() {\r\n  const [state, setState] = React.useState<State>(memoryState)\r\n\r\n  React.useEffect(() => {\r\n    listeners.push(setState)\r\n    return () => {\r\n      const index = listeners.indexOf(setState)\r\n      if (index > -1) {\r\n        listeners.splice(index, 1)\r\n      }\r\n    }\r\n  }, [state])\r\n\r\n  return {\r\n    ...state,\r\n    toast,\r\n    dismiss: (toastId?: string) => dispatch({ type: \"DISMISS_TOAST\", toastId }),\r\n  }\r\n}\r\n\r\nexport { useToast, toast }\r\n"], "names": [], "mappings": ";;;;;AAEA,sCAAsC;AACtC;AAHA;;AAUA,MAAM,cAAc;AACpB,MAAM,qBAAqB;AAS3B,MAAM,cAAc;IAClB,WAAW;IACX,cAAc;IACd,eAAe;IACf,cAAc;AAChB;AAEA,IAAI,QAAQ;AAEZ,SAAS;IACP,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,gBAAgB;IAC7C,OAAO,MAAM,QAAQ;AACvB;AA0BA,MAAM,gBAAgB,IAAI;AAE1B,MAAM,mBAAmB,CAAC;IACxB,IAAI,cAAc,GAAG,CAAC,UAAU;QAC9B;IACF;IAEA,MAAM,UAAU,WAAW;QACzB,cAAc,MAAM,CAAC;QACrB,SAAS;YACP,MAAM;YACN,SAAS;QACX;IACF,GAAG;IAEH,cAAc,GAAG,CAAC,SAAS;AAC7B;AAEO,MAAM,UAAU,CAAC,OAAc;IACpC,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ;oBAAC,OAAO,KAAK;uBAAK,MAAM,MAAM;iBAAC,CAAC,KAAK,CAAC,GAAG;YACnD;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,OAAO,KAAK,CAAC,EAAE,GAAG;wBAAE,GAAG,CAAC;wBAAE,GAAG,OAAO,KAAK;oBAAC,IAAI;YAE3D;QAEF,KAAK;YAAiB;gBACpB,MAAM,EAAE,OAAO,EAAE,GAAG;gBAEpB,2EAA2E;gBAC3E,uCAAuC;gBACvC,IAAI,SAAS;oBACX,iBAAiB;gBACnB,OAAO;oBACL,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;wBACpB,iBAAiB,MAAM,EAAE;oBAC3B;gBACF;gBAEA,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,WAAW,YAAY,YAC5B;4BACE,GAAG,CAAC;4BACJ,MAAM;wBACR,IACA;gBAER;YACF;QACA,KAAK;YACH,IAAI,OAAO,OAAO,KAAK,WAAW;gBAChC,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,EAAE;gBACZ;YACF;YACA,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,OAAO;YAC5D;IACJ;AACF;AAEA,MAAM,YAA2C,EAAE;AAEnD,IAAI,cAAqB;IAAE,QAAQ,EAAE;AAAC;AAEtC,SAAS,SAAS,MAAc;IAC9B,cAAc,QAAQ,aAAa;IACnC,UAAU,OAAO,CAAC,CAAC;QACjB,SAAS;IACX;AACF;AAIA,SAAS,MAAM,EAAE,GAAG,OAAc;IAChC,MAAM,KAAK;IAEX,MAAM,SAAS,CAAC,QACd,SAAS;YACP,MAAM;YACN,OAAO;gBAAE,GAAG,KAAK;gBAAE;YAAG;QACxB;IACF,MAAM,UAAU,IAAM,SAAS;YAAE,MAAM;YAAiB,SAAS;QAAG;IAEpE,SAAS;QACP,MAAM;QACN,OAAO;YACL,GAAG,KAAK;YACR;YACA,MAAM;YACN,cAAc,CAAC;gBACb,IAAI,CAAC,MAAM;YACb;QACF;IACF;IAEA,OAAO;QACL,IAAI;QACJ;QACA;IACF;AACF;AAEA,SAAS;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAS;IAEhD,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,UAAU,IAAI,CAAC;QACf,OAAO;YACL,MAAM,QAAQ,UAAU,OAAO,CAAC;YAChC,IAAI,QAAQ,CAAC,GAAG;gBACd,UAAU,MAAM,CAAC,OAAO;YAC1B;QACF;IACF,GAAG;QAAC;KAAM;IAEV,OAAO;QACL,GAAG,KAAK;QACR;QACA,SAAS,CAAC,UAAqB,SAAS;gBAAE,MAAM;gBAAiB;YAAQ;IAC3E;AACF", "debugId": null}}, {"offset": {"line": 217, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 233, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/components/ui/toast.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as ToastPrimitives from \"@radix-ui/react-toast\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst ToastProvider = ToastPrimitives.Provider\r\n\r\nconst ToastViewport = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Viewport>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Viewport\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nToastViewport.displayName = ToastPrimitives.Viewport.displayName\r\n\r\nconst toastVariants = cva(\r\n  \"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"border bg-background text-foreground\",\r\n        destructive:\r\n          \"destructive group border-destructive bg-destructive text-destructive-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Toast = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Root>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> &\r\n    VariantProps<typeof toastVariants>\r\n>(({ className, variant, ...props }, ref) => {\r\n  return (\r\n    <ToastPrimitives.Root\r\n      ref={ref}\r\n      className={cn(toastVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nToast.displayName = ToastPrimitives.Root.displayName\r\n\r\nconst ToastAction = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Action>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Action\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nToastAction.displayName = ToastPrimitives.Action.displayName\r\n\r\nconst ToastClose = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Close>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Close\r\n    ref={ref}\r\n    className={cn(\r\n      \"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\",\r\n      className\r\n    )}\r\n    toast-close=\"\"\r\n    {...props}\r\n  >\r\n    <X className=\"h-4 w-4\" />\r\n  </ToastPrimitives.Close>\r\n))\r\nToastClose.displayName = ToastPrimitives.Close.displayName\r\n\r\nconst ToastTitle = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Title>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Title\r\n    ref={ref}\r\n    className={cn(\"text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n))\r\nToastTitle.displayName = ToastPrimitives.Title.displayName\r\n\r\nconst ToastDescription = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Description>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm opacity-90\", className)}\r\n    {...props}\r\n  />\r\n))\r\nToastDescription.displayName = ToastPrimitives.Description.displayName\r\n\r\ntype ToastProps = React.ComponentPropsWithoutRef<typeof Toast>\r\n\r\ntype ToastActionElement = React.ReactElement<typeof ToastAction>\r\n\r\nexport {\r\n  type ToastProps,\r\n  type ToastActionElement,\r\n  ToastProvider,\r\n  ToastViewport,\r\n  Toast,\r\n  ToastTitle,\r\n  ToastDescription,\r\n  ToastClose,\r\n  ToastAction,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AAPA;;;;;;;AASA,MAAM,gBAAgB,iKAAA,CAAA,WAAwB;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qIACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,iKAAA,CAAA,WAAwB,CAAC,WAAW;AAEhE,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,6lBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE;IACnC,qBACE,8OAAC,iKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AACA,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAoB,CAAC,WAAW;AAEpD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,SAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sgBACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,iKAAA,CAAA,SAAsB,CAAC,WAAW;AAE5D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yVACA;QAEF,eAAY;QACX,GAAG,KAAK;kBAET,cAAA,8OAAC,4LAAA,CAAA,IAAC;YAAC,WAAU;;;;;;;;;;;AAGjB,WAAW,WAAW,GAAG,iKAAA,CAAA,QAAqB,CAAC,WAAW;AAE1D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG,iKAAA,CAAA,QAAqB,CAAC,WAAW;AAE1D,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB;QACnC,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,iKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 346, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/components/ui/toaster.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useToast } from \"@/hooks/use-toast\"\r\nimport {\r\n  Toast,\r\n  ToastClose,\r\n  ToastDescription,\r\n  ToastProvider,\r\n  ToastTitle,\r\n  ToastViewport,\r\n} from \"@/components/ui/toast\"\r\n\r\nexport function Toaster() {\r\n  const { toasts } = useToast()\r\n\r\n  return (\r\n    <ToastProvider>\r\n      {toasts.map(function ({ id, title, description, action, ...props }) {\r\n        return (\r\n          <Toast key={id} {...props}>\r\n            <div className=\"grid gap-1\">\r\n              {title && <ToastTitle>{title}</ToastTitle>}\r\n              {description && (\r\n                <ToastDescription>{description}</ToastDescription>\r\n              )}\r\n            </div>\r\n            {action}\r\n            <ToastClose />\r\n          </Toast>\r\n        )\r\n      })}\r\n      <ToastViewport />\r\n    </ToastProvider>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAYO,SAAS;IACd,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IAE1B,qBACE,8OAAC,iIAAA,CAAA,gBAAa;;YACX,OAAO,GAAG,CAAC,SAAU,EAAE,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,OAAO;gBAChE,qBACE,8OAAC,iIAAA,CAAA,QAAK;oBAAW,GAAG,KAAK;;sCACvB,8OAAC;4BAAI,WAAU;;gCACZ,uBAAS,8OAAC,iIAAA,CAAA,aAAU;8CAAE;;;;;;gCACtB,6BACC,8OAAC,iIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;wBAGtB;sCACD,8OAAC,iIAAA,CAAA,aAAU;;;;;;mBARD;;;;;YAWhB;0BACA,8OAAC,iIAAA,CAAA,gBAAa;;;;;;;;;;;AAGpB", "debugId": null}}, {"offset": {"line": 442, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/hooks/use-mobile.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nconst MO<PERSON>LE_BREAKPOINT = 768\r\n\r\nexport function useIsMobile() {\r\n  const [isMobile, setIsMobile] = React.useState(false)\r\n  const [isInitialized, setIsInitialized] = React.useState(false)\r\n\r\n  React.useEffect(() => {\r\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\r\n    const onChange = () => {\r\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    }\r\n    mql.addEventListener(\"change\", onChange)\r\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    setIsInitialized(true)\r\n    return () => mql.removeEventListener(\"change\", onChange)\r\n  }, [])\r\n\r\n  // Return false during SSR to prevent hydration mismatches\r\n  if (!isInitialized) {\r\n    return false\r\n  }\r\n\r\n  return isMobile\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAEzD,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;QACvE,MAAM,WAAW;YACf,YAAY,OAAO,UAAU,GAAG;QAClC;QACA,IAAI,gBAAgB,CAAC,UAAU;QAC/B,YAAY,OAAO,UAAU,GAAG;QAChC,iBAAiB;QACjB,OAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;IACjD,GAAG,EAAE;IAEL,0DAA0D;IAC1D,IAAI,CAAC,eAAe;QAClB,OAAO;IACT;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 473, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 533, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kYACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 562, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Separator = React.forwardRef<\r\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\r\n>(\r\n  (\r\n    { className, orientation = \"horizontal\", decorative = true, ...props },\r\n    ref\r\n  ) => (\r\n    <SeparatorPrimitive.Root\r\n      ref={ref}\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"shrink-0 bg-border\",\r\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n)\r\nSeparator.displayName = SeparatorPrimitive.Root.displayName\r\n\r\nexport { Separator }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI/B,CACE,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EACtE,oBAEA,8OAAC,qKAAA,CAAA,OAAuB;QACtB,KAAK;QACL,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG,qKAAA,CAAA,OAAuB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 593, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Sheet = SheetPrimitive.Root\r\n\r\nconst SheetTrigger = SheetPrimitive.Trigger\r\n\r\nconst SheetClose = SheetPrimitive.Close\r\n\r\nconst SheetPortal = SheetPrimitive.Portal\r\n\r\nconst SheetOverlay = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <SheetPrimitive.Overlay\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  />\r\n))\r\nSheetOverlay.displayName = SheetPrimitive.Overlay.displayName\r\n\r\nconst sheetVariants = cva(\r\n  \"fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\r\n  {\r\n    variants: {\r\n      side: {\r\n        top: \"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top\",\r\n        bottom:\r\n          \"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom\",\r\n        left: \"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm\",\r\n        right:\r\n          \"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      side: \"right\",\r\n    },\r\n  }\r\n)\r\n\r\ninterface SheetContentProps\r\n  extends React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content>,\r\n    VariantProps<typeof sheetVariants> {}\r\n\r\nconst SheetContent = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Content>,\r\n  SheetContentProps\r\n>(({ side = \"right\", className, children, ...props }, ref) => (\r\n  <SheetPortal>\r\n    <SheetOverlay />\r\n    <SheetPrimitive.Content\r\n      ref={ref}\r\n      className={cn(sheetVariants({ side }), className)}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SheetPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </SheetPrimitive.Close>\r\n    </SheetPrimitive.Content>\r\n  </SheetPortal>\r\n))\r\nSheetContent.displayName = SheetPrimitive.Content.displayName\r\n\r\nconst SheetHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-2 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nSheetHeader.displayName = \"SheetHeader\"\r\n\r\nconst SheetFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nSheetFooter.displayName = \"SheetFooter\"\r\n\r\nconst SheetTitle = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <SheetPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\"text-lg font-semibold text-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSheetTitle.displayName = SheetPrimitive.Title.displayName\r\n\r\nconst SheetDescription = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <SheetPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSheetDescription.displayName = SheetPrimitive.Description.displayName\r\n\r\nexport {\r\n  Sheet,\r\n  SheetPortal,\r\n  SheetOverlay,\r\n  SheetTrigger,\r\n  SheetClose,\r\n  SheetContent,\r\n  SheetHeader,\r\n  SheetFooter,\r\n  SheetTitle,\r\n  SheetDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AAPA;;;;;;;AASA,MAAM,QAAQ,kKAAA,CAAA,OAAmB;AAEjC,MAAM,eAAe,kKAAA,CAAA,UAAsB;AAE3C,MAAM,aAAa,kKAAA,CAAA,QAAoB;AAEvC,MAAM,cAAc,kKAAA,CAAA,SAAqB;AAEzC,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,UAAsB;QACrB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;AAGT,aAAa,WAAW,GAAG,kKAAA,CAAA,UAAsB,CAAC,WAAW;AAE7D,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,oMACA;IACE,UAAU;QACR,MAAM;YACJ,KAAK;YACL,QACE;YACF,MAAM;YACN,OACE;QACJ;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AAOF,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,OAAO,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpD,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAsB;gBACrB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;oBAAE;gBAAK,IAAI;gBACtC,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,aAAa,WAAW,GAAG,kKAAA,CAAA,UAAsB,CAAC,WAAW;AAE7D,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yCAAyC;QACtD,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG,kKAAA,CAAA,QAAoB,CAAC,WAAW;AAEzD,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,cAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,kKAAA,CAAA,cAA0B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 742, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) {\r\n  return (\r\n    <div\r\n      className={cn(\"animate-pulse rounded-md bg-muted\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 766, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst TooltipProvider = TooltipPrimitive.Provider\r\n\r\nconst Tooltip = TooltipPrimitive.Root\r\n\r\nconst TooltipTrigger = TooltipPrimitive.Trigger\r\n\r\nconst TooltipContent = React.forwardRef<\r\n  React.ElementRef<typeof TooltipPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\r\n>(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <TooltipPrimitive.Content\r\n    ref={ref}\r\n    sideOffset={sideOffset}\r\n    className={cn(\r\n      \"z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,kBAAkB,mKAAA,CAAA,WAAyB;AAEjD,MAAM,UAAU,mKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,mKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,8OAAC,mKAAA,CAAA,UAAwB;QACvB,KAAK;QACL,YAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sYACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,mKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 802, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/lib/client-utils.ts"], "sourcesContent": ["/**\r\n * Client-side utilities that safely handle browser APIs\r\n * These functions check for client-side environment before accessing browser APIs\r\n */\r\n\r\nexport function getCookie(name: string): string | null {\r\n  if (typeof window === 'undefined' || typeof document === 'undefined') {\r\n    return null;\r\n  }\r\n  \r\n  const value = `; ${document.cookie}`;\r\n  const parts = value.split(`; ${name}=`);\r\n  if (parts.length === 2) {\r\n    return parts.pop()?.split(';').shift() || null;\r\n  }\r\n  return null;\r\n}\r\n\r\nexport function setCookie(name: string, value: string, options: { path?: string; maxAge?: number } = {}): void {\r\n  if (typeof window === 'undefined' || typeof document === 'undefined') {\r\n    return;\r\n  }\r\n  \r\n  const { path = '/', maxAge } = options;\r\n  let cookieString = `${name}=${value}; path=${path}`;\r\n  \r\n  if (maxAge) {\r\n    cookieString += `; max-age=${maxAge}`;\r\n  }\r\n  \r\n  document.cookie = cookieString;\r\n}\r\n\r\nexport function isClient(): boolean {\r\n  return typeof window !== 'undefined';\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAEM,SAAS,UAAU,IAAY;IACpC,wCAAsE;QACpE,OAAO;IACT;;IAEA,MAAM;IACN,MAAM;AAKR;AAEO,SAAS,UAAU,IAAY,EAAE,KAAa,EAAE,UAA8C,CAAC,CAAC;IACrG,wCAAsE;QACpE;IACF;;IAEA,MAAQ,kBAAY;IACpB,IAAI;AAON;AAEO,SAAS;IACd,OAAO,gBAAkB;AAC3B", "debugId": null}}, {"offset": {"line": 835, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/components/ui/sidebar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { VariantProps, cva } from \"class-variance-authority\"\r\nimport { PanelLeft } from \"lucide-react\"\r\n\r\nimport { useIsMobile } from \"@/hooks/use-mobile\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport { Separator } from \"@/components/ui/separator\"\r\nimport { Sheet, SheetContent } from \"@/components/ui/sheet\"\r\nimport { Skeleton } from \"@/components/ui/skeleton\"\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\"\r\nimport { getCookie, setCookie, isClient } from \"@/lib/client-utils\"\r\n\r\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\"\r\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7\r\nconst SIDEBAR_WIDTH = \"16rem\"\r\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\"\r\nconst SIDEBAR_WIDTH_ICON = \"3rem\"\r\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\"\r\n\r\ntype SidebarContext = {\r\n  state: \"expanded\" | \"collapsed\"\r\n  open: boolean\r\n  setOpen: (open: boolean) => void\r\n  openMobile: boolean\r\n  setOpenMobile: (open: boolean) => void\r\n  isMobile: boolean\r\n  toggleSidebar: () => void\r\n}\r\n\r\nconst SidebarContext = React.createContext<SidebarContext | null>(null)\r\n\r\nfunction useSidebar() {\r\n  const context = React.useContext(SidebarContext)\r\n  if (!context) {\r\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\")\r\n  }\r\n\r\n  return context\r\n}\r\n\r\nconst SidebarProvider = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\"> & {\r\n    defaultOpen?: boolean\r\n    open?: boolean\r\n    onOpenChange?: (open: boolean) => void\r\n  }\r\n>(\r\n  (\r\n    {\r\n      defaultOpen = true,\r\n      open: openProp,\r\n      onOpenChange: setOpenProp,\r\n      className,\r\n      style,\r\n      children,\r\n      ...props\r\n    },\r\n    ref\r\n  ) => {\r\n    const isMobile = useIsMobile()\r\n    const [openMobile, setOpenMobile] = React.useState(false)\r\n    const [isHydrated, setIsHydrated] = React.useState(false)\r\n\r\n    // Initialize sidebar state from cookie on client side only\r\n    const [_open, _setOpen] = React.useState(() => {\r\n      if (!isClient()) {\r\n        return defaultOpen;\r\n      }\r\n      const savedState = getCookie(SIDEBAR_COOKIE_NAME);\r\n      return savedState !== null ? savedState === 'true' : defaultOpen;\r\n    })\r\n\r\n    // Mark as hydrated after first render\r\n    React.useEffect(() => {\r\n      setIsHydrated(true)\r\n      // Re-read cookie after hydration to ensure consistency\r\n      const savedState = getCookie(SIDEBAR_COOKIE_NAME);\r\n      if (savedState !== null && !openProp) {\r\n        _setOpen(savedState === 'true');\r\n      }\r\n    }, [openProp])\r\n\r\n    const open = openProp ?? _open\r\n    const setOpen = React.useCallback(\r\n      (value: boolean | ((value: boolean) => boolean)) => {\r\n        const openState = typeof value === \"function\" ? value(open) : value\r\n        if (setOpenProp) {\r\n          setOpenProp(openState)\r\n        } else {\r\n          _setOpen(openState)\r\n        }\r\n\r\n        // This sets the cookie to keep the sidebar state.\r\n        setCookie(SIDEBAR_COOKIE_NAME, openState.toString(), {\r\n          path: '/',\r\n          maxAge: SIDEBAR_COOKIE_MAX_AGE\r\n        })\r\n      },\r\n      [setOpenProp, open]\r\n    )\r\n\r\n    // Helper to toggle the sidebar.\r\n    const toggleSidebar = React.useCallback(() => {\r\n      return isMobile\r\n        ? setOpenMobile((open) => !open)\r\n        : setOpen((open) => !open)\r\n    }, [isMobile, setOpen, setOpenMobile])\r\n\r\n    // Adds a keyboard shortcut to toggle the sidebar.\r\n    React.useEffect(() => {\r\n      if (!isClient()) return;\r\n      \r\n      const handleKeyDown = (event: KeyboardEvent) => {\r\n        if (\r\n          event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\r\n          (event.metaKey || event.ctrlKey)\r\n        ) {\r\n          event.preventDefault()\r\n          toggleSidebar()\r\n        }\r\n      }\r\n\r\n      window.addEventListener(\"keydown\", handleKeyDown)\r\n      return () => window.removeEventListener(\"keydown\", handleKeyDown)\r\n    }, [toggleSidebar])\r\n\r\n    // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\r\n    // This makes it easier to style the sidebar with Tailwind classes.\r\n    const state = open ? \"expanded\" : \"collapsed\"\r\n\r\n    const contextValue = React.useMemo<SidebarContext>(\r\n      () => ({\r\n        state,\r\n        open,\r\n        setOpen,\r\n        isMobile,\r\n        openMobile,\r\n        setOpenMobile,\r\n        toggleSidebar,\r\n      }),\r\n      [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\r\n    )\r\n\r\n    // Prevent hydration mismatches by using a consistent state during SSR\r\n    if (!isHydrated) {\r\n      return (\r\n        <SidebarContext.Provider value={{\r\n          state: defaultOpen ? \"expanded\" : \"collapsed\",\r\n          open: defaultOpen,\r\n          setOpen: () => {},\r\n          isMobile: false,\r\n          openMobile: false,\r\n          setOpenMobile: () => {},\r\n          toggleSidebar: () => {},\r\n        }}>\r\n          <TooltipProvider delayDuration={0}>\r\n            <div\r\n              style={\r\n                {\r\n                  \"--sidebar-width\": SIDEBAR_WIDTH,\r\n                  \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\r\n                  ...style,\r\n                } as React.CSSProperties\r\n              }\r\n              className={cn(\r\n                \"group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar\",\r\n                className\r\n              )}\r\n              ref={ref}\r\n              {...props}\r\n            >\r\n              {children}\r\n            </div>\r\n          </TooltipProvider>\r\n        </SidebarContext.Provider>\r\n      )\r\n    }\r\n\r\n    return (\r\n      <SidebarContext.Provider value={contextValue}>\r\n        <TooltipProvider delayDuration={0}>\r\n          <div\r\n            style={\r\n              {\r\n                \"--sidebar-width\": SIDEBAR_WIDTH,\r\n                \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\r\n                ...style,\r\n              } as React.CSSProperties\r\n            }\r\n            className={cn(\r\n              \"group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar\",\r\n              className\r\n            )}\r\n            ref={ref}\r\n            {...props}\r\n          >\r\n            {children}\r\n          </div>\r\n        </TooltipProvider>\r\n      </SidebarContext.Provider>\r\n    )\r\n  }\r\n)\r\nSidebarProvider.displayName = \"SidebarProvider\"\r\n\r\nconst Sidebar = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\"> & {\r\n    side?: \"left\" | \"right\"\r\n    variant?: \"sidebar\" | \"floating\" | \"inset\"\r\n    collapsible?: \"offcanvas\" | \"icon\" | \"none\"\r\n  }\r\n>(\r\n  (\r\n    {\r\n      side = \"left\",\r\n      variant = \"sidebar\",\r\n      collapsible = \"offcanvas\",\r\n      className,\r\n      children,\r\n      ...props\r\n    },\r\n    ref\r\n  ) => {\r\n    const { isMobile, state, openMobile, setOpenMobile } = useSidebar()\r\n\r\n    if (collapsible === \"none\") {\r\n      return (\r\n        <div\r\n          className={cn(\r\n            \"flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground\",\r\n            className\r\n          )}\r\n          ref={ref}\r\n          {...props}\r\n        >\r\n          {children}\r\n        </div>\r\n      )\r\n    }\r\n\r\n    if (isMobile) {\r\n      return (\r\n        <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\r\n          <SheetContent\r\n            data-sidebar=\"sidebar\"\r\n            data-mobile=\"true\"\r\n            className=\"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden\"\r\n            style={\r\n              {\r\n                \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\r\n              } as React.CSSProperties\r\n            }\r\n            side={side}\r\n          >\r\n            <div className=\"flex h-full w-full flex-col\">{children}</div>\r\n          </SheetContent>\r\n        </Sheet>\r\n      )\r\n    }\r\n\r\n    return (\r\n      <div\r\n        ref={ref}\r\n        className=\"group peer hidden md:block text-sidebar-foreground\"\r\n        data-state={state}\r\n        data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\r\n        data-variant={variant}\r\n        data-side={side}\r\n      >\r\n        {/* This is what handles the sidebar gap on desktop */}\r\n        <div\r\n          className={cn(\r\n            \"duration-200 relative h-svh w-[--sidebar-width] bg-transparent transition-[width] ease-linear\",\r\n            \"group-data-[collapsible=offcanvas]:w-0\",\r\n            \"group-data-[side=right]:rotate-180\",\r\n            variant === \"floating\" || variant === \"inset\"\r\n              ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]\"\r\n              : \"group-data-[collapsible=icon]:w-[--sidebar-width-icon]\"\r\n          )}\r\n        />\r\n        <div\r\n          className={cn(\r\n            \"duration-200 fixed inset-y-0 z-10 hidden h-svh w-[--sidebar-width] transition-[left,right,width] ease-linear md:flex\",\r\n            side === \"left\"\r\n              ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\r\n              : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\r\n            // Adjust the padding for floating and inset variants.\r\n            variant === \"floating\" || variant === \"inset\"\r\n              ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]\"\r\n              : \"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l\",\r\n            className\r\n          )}\r\n          {...props}\r\n        >\r\n          <div\r\n            data-sidebar=\"sidebar\"\r\n            className=\"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow\"\r\n          >\r\n            {children}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    )\r\n  }\r\n)\r\nSidebar.displayName = \"Sidebar\"\r\n\r\nconst SidebarTrigger = React.forwardRef<\r\n  React.ElementRef<typeof Button>,\r\n  React.ComponentProps<typeof Button>\r\n>(({ className, onClick, ...props }, ref) => {\r\n  const { toggleSidebar } = useSidebar()\r\n\r\n  return (\r\n    <Button\r\n      ref={ref}\r\n      data-sidebar=\"trigger\"\r\n      variant=\"ghost\"\r\n      size=\"icon\"\r\n      className={cn(\"h-7 w-7\", className)}\r\n      onClick={(event) => {\r\n        onClick?.(event)\r\n        toggleSidebar()\r\n      }}\r\n      {...props}\r\n    >\r\n      <PanelLeft />\r\n      <span className=\"sr-only\">Toggle Sidebar</span>\r\n    </Button>\r\n  )\r\n})\r\nSidebarTrigger.displayName = \"SidebarTrigger\"\r\n\r\nconst SidebarRail = React.forwardRef<\r\n  HTMLButtonElement,\r\n  React.ComponentProps<\"button\">\r\n>(({ className, ...props }, ref) => {\r\n  const { toggleSidebar } = useSidebar()\r\n\r\n  return (\r\n    <button\r\n      ref={ref}\r\n      data-sidebar=\"rail\"\r\n      aria-label=\"Toggle Sidebar\"\r\n      tabIndex={-1}\r\n      onClick={toggleSidebar}\r\n      title=\"Toggle Sidebar\"\r\n      className={cn(\r\n        \"absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex\",\r\n        \"[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize\",\r\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\r\n        \"group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar\",\r\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\r\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarRail.displayName = \"SidebarRail\"\r\n\r\nconst SidebarInset = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"main\">\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <main\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative flex min-h-svh flex-1 flex-col bg-background\",\r\n        \"peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarInset.displayName = \"SidebarInset\"\r\n\r\nconst SidebarInput = React.forwardRef<\r\n  React.ElementRef<typeof Input>,\r\n  React.ComponentProps<typeof Input>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <Input\r\n      ref={ref}\r\n      data-sidebar=\"input\"\r\n      className={cn(\r\n        \"h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarInput.displayName = \"SidebarInput\"\r\n\r\nconst SidebarHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\">\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <div\r\n      ref={ref}\r\n      data-sidebar=\"header\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarHeader.displayName = \"SidebarHeader\"\r\n\r\nconst SidebarFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\">\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <div\r\n      ref={ref}\r\n      data-sidebar=\"footer\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarFooter.displayName = \"SidebarFooter\"\r\n\r\nconst SidebarSeparator = React.forwardRef<\r\n  React.ElementRef<typeof Separator>,\r\n  React.ComponentProps<typeof Separator>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <Separator\r\n      ref={ref}\r\n      data-sidebar=\"separator\"\r\n      className={cn(\"mx-2 w-auto bg-sidebar-border\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarSeparator.displayName = \"SidebarSeparator\"\r\n\r\nconst SidebarContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\">\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <div\r\n      ref={ref}\r\n      data-sidebar=\"content\"\r\n      className={cn(\r\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarContent.displayName = \"SidebarContent\"\r\n\r\nconst SidebarGroup = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\">\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <div\r\n      ref={ref}\r\n      data-sidebar=\"group\"\r\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarGroup.displayName = \"SidebarGroup\"\r\n\r\nconst SidebarGroupLabel = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\"> & { asChild?: boolean }\r\n>(({ className, asChild = false, ...props }, ref) => {\r\n  const Comp = asChild ? Slot : \"div\"\r\n\r\n  return (\r\n    <Comp\r\n      ref={ref}\r\n      data-sidebar=\"group-label\"\r\n      className={cn(\r\n        \"duration-200 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarGroupLabel.displayName = \"SidebarGroupLabel\"\r\n\r\nconst SidebarGroupAction = React.forwardRef<\r\n  HTMLButtonElement,\r\n  React.ComponentProps<\"button\"> & { asChild?: boolean }\r\n>(({ className, asChild = false, ...props }, ref) => {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      ref={ref}\r\n      data-sidebar=\"group-action\"\r\n      className={cn(\r\n        \"absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 after:md:hidden\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarGroupAction.displayName = \"SidebarGroupAction\"\r\n\r\nconst SidebarGroupContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\">\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    data-sidebar=\"group-content\"\r\n    className={cn(\"w-full text-sm\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSidebarGroupContent.displayName = \"SidebarGroupContent\"\r\n\r\nconst SidebarMenu = React.forwardRef<\r\n  HTMLUListElement,\r\n  React.ComponentProps<\"ul\">\r\n>(({ className, ...props }, ref) => (\r\n  <ul\r\n    ref={ref}\r\n    data-sidebar=\"menu\"\r\n    className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSidebarMenu.displayName = \"SidebarMenu\"\r\n\r\nconst SidebarMenuItem = React.forwardRef<\r\n  HTMLLIElement,\r\n  React.ComponentProps<\"li\">\r\n>(({ className, ...props }, ref) => (\r\n  <li\r\n    ref={ref}\r\n    data-sidebar=\"menu-item\"\r\n    className={cn(\"group/menu-item relative\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSidebarMenuItem.displayName = \"SidebarMenuItem\"\r\n\r\nconst sidebarMenuButtonVariants = cva(\r\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\r\n        outline:\r\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\r\n      },\r\n      size: {\r\n        default: \"h-8 text-sm\",\r\n        sm: \"h-7 text-xs\",\r\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:!p-0\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst SidebarMenuButton = React.forwardRef<\r\n  HTMLButtonElement,\r\n  React.ComponentProps<\"button\"> & {\r\n    asChild?: boolean\r\n    isActive?: boolean\r\n    tooltip?: string | React.ComponentProps<typeof TooltipContent>\r\n  } & VariantProps<typeof sidebarMenuButtonVariants>\r\n>(\r\n  (\r\n    {\r\n      asChild = false,\r\n      isActive = false,\r\n      variant = \"default\",\r\n      size = \"default\",\r\n      tooltip,\r\n      className,\r\n      ...props\r\n    },\r\n    ref\r\n  ) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    const { isMobile, state } = useSidebar()\r\n\r\n    const button = (\r\n      <Comp\r\n        ref={ref}\r\n        data-sidebar=\"menu-button\"\r\n        data-size={size}\r\n        data-active={isActive}\r\n        className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\r\n        {...props}\r\n      />\r\n    )\r\n\r\n    if (!tooltip) {\r\n      return button\r\n    }\r\n\r\n    if (typeof tooltip === \"string\") {\r\n      tooltip = {\r\n        children: tooltip,\r\n      }\r\n    }\r\n\r\n    return (\r\n      <Tooltip>\r\n        <TooltipTrigger asChild>{button}</TooltipTrigger>\r\n        <TooltipContent\r\n          side=\"right\"\r\n          align=\"center\"\r\n          hidden={state !== \"collapsed\" || isMobile}\r\n          {...tooltip}\r\n        />\r\n      </Tooltip>\r\n    )\r\n  }\r\n)\r\nSidebarMenuButton.displayName = \"SidebarMenuButton\"\r\n\r\nconst SidebarMenuAction = React.forwardRef<\r\n  HTMLButtonElement,\r\n  React.ComponentProps<\"button\"> & {\r\n    asChild?: boolean\r\n    showOnHover?: boolean\r\n  }\r\n>(({ className, asChild = false, showOnHover = false, ...props }, ref) => {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      ref={ref}\r\n      data-sidebar=\"menu-action\"\r\n      className={cn(\r\n        \"absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 after:md:hidden\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        showOnHover &&\r\n          \"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarMenuAction.displayName = \"SidebarMenuAction\"\r\n\r\nconst SidebarMenuBadge = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\">\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    data-sidebar=\"menu-badge\"\r\n    className={cn(\r\n      \"absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground select-none pointer-events-none\",\r\n      \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\r\n      \"peer-data-[size=sm]/menu-button:top-1\",\r\n      \"peer-data-[size=default]/menu-button:top-1.5\",\r\n      \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n      \"group-data-[collapsible=icon]:hidden\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nSidebarMenuBadge.displayName = \"SidebarMenuBadge\"\r\n\r\nconst SidebarMenuSkeleton = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\"> & {\r\n    showIcon?: boolean\r\n  }\r\n>(({ className, showIcon = false, ...props }, ref) => {\r\n  // Random width between 50 to 90%.\r\n  const width = React.useMemo(() => {\r\n    return `${Math.floor(Math.random() * 40) + 50}%`\r\n  }, [])\r\n\r\n  return (\r\n    <div\r\n      ref={ref}\r\n      data-sidebar=\"menu-skeleton\"\r\n      className={cn(\"rounded-md h-8 flex gap-2 px-2 items-center\", className)}\r\n      {...props}\r\n    >\r\n      {showIcon && (\r\n        <Skeleton\r\n          className=\"size-4 rounded-md\"\r\n          data-sidebar=\"menu-skeleton-icon\"\r\n        />\r\n      )}\r\n      <Skeleton\r\n        className=\"h-4 flex-1 max-w-[--skeleton-width]\"\r\n        data-sidebar=\"menu-skeleton-text\"\r\n        style={\r\n          {\r\n            \"--skeleton-width\": width,\r\n          } as React.CSSProperties\r\n        }\r\n      />\r\n    </div>\r\n  )\r\n})\r\nSidebarMenuSkeleton.displayName = \"SidebarMenuSkeleton\"\r\n\r\nconst SidebarMenuSub = React.forwardRef<\r\n  HTMLUListElement,\r\n  React.ComponentProps<\"ul\">\r\n>(({ className, ...props }, ref) => (\r\n  <ul\r\n    ref={ref}\r\n    data-sidebar=\"menu-sub\"\r\n    className={cn(\r\n      \"mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5\",\r\n      \"group-data-[collapsible=icon]:hidden\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nSidebarMenuSub.displayName = \"SidebarMenuSub\"\r\n\r\nconst SidebarMenuSubItem = React.forwardRef<\r\n  HTMLLIElement,\r\n  React.ComponentProps<\"li\">\r\n>(({ ...props }, ref) => <li ref={ref} {...props} />)\r\nSidebarMenuSubItem.displayName = \"SidebarMenuSubItem\"\r\n\r\nconst SidebarMenuSubButton = React.forwardRef<\r\n  HTMLAnchorElement,\r\n  React.ComponentProps<\"a\"> & {\r\n    asChild?: boolean\r\n    size?: \"sm\" | \"md\"\r\n    isActive?: boolean\r\n  }\r\n>(({ asChild = false, size = \"md\", isActive, className, ...props }, ref) => {\r\n  const Comp = asChild ? Slot : \"a\"\r\n\r\n  return (\r\n    <Comp\r\n      ref={ref}\r\n      data-sidebar=\"menu-sub-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(\r\n        \"flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 [&>svg]:text-sidebar-accent-foreground\",\r\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\r\n        size === \"sm\" && \"text-xs\",\r\n        size === \"md\" && \"text-sm\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarMenuSubButton.displayName = \"SidebarMenuSubButton\"\r\n\r\nexport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarGroup,\r\n  SidebarGroupAction,\r\n  SidebarGroupContent,\r\n  SidebarGroupLabel,\r\n  SidebarHeader,\r\n  SidebarInput,\r\n  SidebarInset,\r\n  SidebarMenu,\r\n  SidebarMenuAction,\r\n  SidebarMenuBadge,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSkeleton,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n  SidebarProvider,\r\n  SidebarRail,\r\n  SidebarSeparator,\r\n  SidebarTrigger,\r\n  useSidebar,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AApBA;;;;;;;;;;;;;;;AAsBA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAYlC,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAyB;AAElE,SAAS;IACP,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAEA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAQrC,CACE,EACE,cAAc,IAAI,EAClB,MAAM,QAAQ,EACd,cAAc,WAAW,EACzB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OACJ,EACD;IAEA,MAAM,WAAW,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAEnD,2DAA2D;IAC3D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;QACvC,IAAI,CAAC,CAAA,GAAA,6HAAA,CAAA,WAAQ,AAAD,KAAK;YACf,OAAO;QACT;QACA,MAAM,aAAa,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD,EAAE;QAC7B,OAAO,eAAe,OAAO,eAAe,SAAS;IACvD;IAEA,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,cAAc;QACd,uDAAuD;QACvD,MAAM,aAAa,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD,EAAE;QAC7B,IAAI,eAAe,QAAQ,CAAC,UAAU;YACpC,SAAS,eAAe;QAC1B;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,OAAO,YAAY;IACzB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAC9B,CAAC;QACC,MAAM,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ;QAC9D,IAAI,aAAa;YACf,YAAY;QACd,OAAO;YACL,SAAS;QACX;QAEA,kDAAkD;QAClD,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD,EAAE,qBAAqB,UAAU,QAAQ,IAAI;YACnD,MAAM;YACN,QAAQ;QACV;IACF,GACA;QAAC;QAAa;KAAK;IAGrB,gCAAgC;IAChC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE;QACtC,OAAO,WACH,cAAc,CAAC,OAAS,CAAC,QACzB,QAAQ,CAAC,OAAS,CAAC;IACzB,GAAG;QAAC;QAAU;QAAS;KAAc;IAErC,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI,CAAC,CAAA,GAAA,6HAAA,CAAA,WAAQ,AAAD,KAAK;QAEjB,MAAM,gBAAgB,CAAC;YACrB,IACE,MAAM,GAAG,KAAK,6BACd,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAC/B;gBACA,MAAM,cAAc;gBACpB;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG;QAAC;KAAc;IAElB,yEAAyE;IACzE,mEAAmE;IACnE,MAAM,QAAQ,OAAO,aAAa;IAElC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAC/B,IAAM,CAAC;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF,CAAC,GACD;QAAC;QAAO;QAAM;QAAS;QAAU;QAAY;QAAe;KAAc;IAG5E,sEAAsE;IACtE,IAAI,CAAC,YAAY;QACf,qBACE,8OAAC,eAAe,QAAQ;YAAC,OAAO;gBAC9B,OAAO,cAAc,aAAa;gBAClC,MAAM;gBACN,SAAS,KAAO;gBAChB,UAAU;gBACV,YAAY;gBACZ,eAAe,KAAO;gBACtB,eAAe,KAAO;YACxB;sBACE,cAAA,8OAAC,mIAAA,CAAA,kBAAe;gBAAC,eAAe;0BAC9B,cAAA,8OAAC;oBACC,OACE;wBACE,mBAAmB;wBACnB,wBAAwB;wBACxB,GAAG,KAAK;oBACV;oBAEF,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;oBAEF,KAAK;oBACJ,GAAG,KAAK;8BAER;;;;;;;;;;;;;;;;IAKX;IAEA,qBACE,8OAAC,eAAe,QAAQ;QAAC,OAAO;kBAC9B,cAAA,8OAAC,mIAAA,CAAA,kBAAe;YAAC,eAAe;sBAC9B,cAAA,8OAAC;gBACC,OACE;oBACE,mBAAmB;oBACnB,wBAAwB;oBACxB,GAAG,KAAK;gBACV;gBAEF,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;gBAEF,KAAK;gBACJ,GAAG,KAAK;0BAER;;;;;;;;;;;;;;;;AAKX;AAEF,gBAAgB,WAAW,GAAG;AAE9B,MAAM,wBAAU,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAQ7B,CACE,EACE,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,cAAc,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,GAAG,OACJ,EACD;IAEA,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEvD,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,8OAAC;YACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;YAEF,KAAK;YACJ,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,IAAI,UAAU;QACZ,qBACE,8OAAC,iIAAA,CAAA,QAAK;YAAC,MAAM;YAAY,cAAc;YAAgB,GAAG,KAAK;sBAC7D,cAAA,8OAAC,iIAAA,CAAA,eAAY;gBACX,gBAAa;gBACb,eAAY;gBACZ,WAAU;gBACV,OACE;oBACE,mBAAmB;gBACrB;gBAEF,MAAM;0BAEN,cAAA,8OAAC;oBAAI,WAAU;8BAA+B;;;;;;;;;;;;;;;;IAItD;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;QACV,cAAY;QACZ,oBAAkB,UAAU,cAAc,cAAc;QACxD,gBAAc;QACd,aAAW;;0BAGX,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iGACA,0CACA,sCACA,YAAY,cAAc,YAAY,UAClC,yFACA;;;;;;0BAGR,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wHACA,SAAS,SACL,mFACA,oFACJ,sDAAsD;gBACtD,YAAY,cAAc,YAAY,UAClC,kGACA,2HACJ;gBAED,GAAG,KAAK;0BAET,cAAA,8OAAC;oBACC,gBAAa;oBACb,WAAU;8BAET;;;;;;;;;;;;;;;;;AAKX;AAEF,QAAQ,WAAW,GAAG;AAEtB,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE;IACnC,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,KAAK;QACL,gBAAa;QACb,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,WAAW;QACzB,SAAS,CAAC;YACR,UAAU;YACV;QACF;QACC,GAAG,KAAK;;0BAET,8OAAC,gNAAA,CAAA,YAAS;;;;;0BACV,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;AACA,eAAe,WAAW,GAAG;AAE7B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,cAAW;QACX,UAAU,CAAC;QACX,SAAS;QACT,OAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mPACA,8EACA,0HACA,2JACA,6DACA,6DACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,YAAY,WAAW,GAAG;AAE1B,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA,gRACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,aAAa,WAAW,GAAG;AAE3B,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,aAAa,WAAW,GAAG;AAE3B,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AACA,cAAc,WAAW,GAAG;AAE5B,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AACA,cAAc,WAAW,GAAG;AAE5B,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC,qIAAA,CAAA,YAAS;QACR,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AACA,iBAAiB,WAAW,GAAG;AAE/B,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,eAAe,WAAW,GAAG;AAE7B,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AACA,aAAa,WAAW,GAAG;AAE3B,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IAC3C,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sOACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,kBAAkB,WAAW,GAAG;AAEhC,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IAC3C,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4RACA,kDAAkD;QAClD,iDACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,mBAAmB,WAAW,GAAG;AAEjC,MAAM,oCAAsB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGzC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;QAC/B,GAAG,KAAK;;;;;;AAGb,oBAAoB,WAAW,GAAG;AAElC,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAA4B,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EAClC,qzBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAQvC,CACE,EACE,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,OAAO,EACP,SAAS,EACT,GAAG,OACJ,EACD;IAEA,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE5B,MAAM,uBACJ,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;YAAE;YAAS;QAAK,IAAI;QAC3D,GAAG,KAAK;;;;;;IAIb,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,IAAI,OAAO,YAAY,UAAU;QAC/B,UAAU;YACR,UAAU;QACZ;IACF;IAEA,qBACE,8OAAC,mIAAA,CAAA,UAAO;;0BACN,8OAAC,mIAAA,CAAA,iBAAc;gBAAC,OAAO;0BAAE;;;;;;0BACzB,8OAAC,mIAAA,CAAA,iBAAc;gBACb,MAAK;gBACL,OAAM;gBACN,QAAQ,UAAU,eAAe;gBAChC,GAAG,OAAO;;;;;;;;;;;;AAInB;AAEF,kBAAkB,WAAW,GAAG;AAEhC,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAMvC,CAAC,EAAE,SAAS,EAAE,UAAU,KAAK,EAAE,cAAc,KAAK,EAAE,GAAG,OAAO,EAAE;IAChE,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kVACA,kDAAkD;QAClD,iDACA,yCACA,gDACA,2CACA,wCACA,eACE,4LACF;QAED,GAAG,KAAK;;;;;;AAGf;AACA,kBAAkB,WAAW,GAAG;AAEhC,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0KACA,4HACA,yCACA,gDACA,2CACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG;AAE/B,MAAM,oCAAsB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAKzC,CAAC,EAAE,SAAS,EAAE,WAAW,KAAK,EAAE,GAAG,OAAO,EAAE;IAC5C,kCAAkC;IAClC,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QAC1B,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC,CAAC;IAClD,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;YAER,0BACC,8OAAC,oIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;;;;;;0BAGjB,8OAAC,oIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;gBACb,OACE;oBACE,oBAAoB;gBACtB;;;;;;;;;;;;AAKV;AACA,oBAAoB,WAAW,GAAG;AAElC,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,GAAG,OAAO,EAAE,oBAAQ,8OAAC;QAAG,KAAK;QAAM,GAAG,KAAK;;;;;;AAChD,mBAAmB,WAAW,GAAG;AAEjC,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAO1C,CAAC,EAAE,UAAU,KAAK,EAAE,OAAO,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAClE,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+eACA,0FACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1527, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/components/AppSidebar.tsx"], "sourcesContent": ["// src/components/AppSidebar.tsx\r\n\"use client\";\r\n\r\nimport Link from 'next/link';\r\nimport { usePathname } from 'next/navigation';\r\nimport {\r\n  Sidebar,\r\n  SidebarHeader,\r\n  SidebarContent,\r\n  SidebarMenu,\r\n  SidebarMenuItem,\r\n  SidebarMenuButton,\r\n  SidebarFooter,\r\n  SidebarSeparator,\r\n} from '@/components/ui/sidebar';\r\nimport { Bot, FolderKanban, Home, Settings, TestTube2, LayoutDashboard, Flame, Wand2, FileText, Code2 } from 'lucide-react'; // Added Code2 for CodeGen\r\nimport type { NavItem } from '@/lib/types';\r\n\r\nconst mainNavItems: NavItem[] = [\r\n  { title: 'Dashboard', href: '/', icon: <LayoutDashboard /> },\r\n  { title: 'Projects', href: '/projects', icon: <FolderKanban /> },\r\n  { title: 'AI Tools', href: '/ai-tools', icon: <Bot /> },\r\n  { title: 'CodeGen Recorder', href: '/codegen', icon: <Code2 /> },\r\n  { title: 'Smoke Test Playground', href: '/smoke-test-playground', icon: <Flame /> },\r\n  { title: 'Prompt Management', href: '/prompts', icon: <FileText /> },\r\n  { title: 'QA Assistant', href: '/qa-assistant', icon: <Wand2 /> }, // New Item\r\n];\r\n\r\nconst settingsNavItem: NavItem = {\r\n  title: 'Settings',\r\n  href: '/settings',\r\n  icon: <Settings />,\r\n};\r\n\r\nexport function AppSidebar() {\r\n  const pathname = usePathname();\r\n\r\n  const isActive = (href: string) => {\r\n    if (href === '/') return pathname === href;\r\n    return pathname.startsWith(href);\r\n  };\r\n\r\n  return (\r\n    <Sidebar collapsible=\"icon\">\r\n      <SidebarHeader className=\"p-4\">\r\n        <Link href=\"/\" className=\"flex items-center gap-2 glow-text group-data-[collapsible=icon]:justify-center\">\r\n          <TestTube2 className=\"h-7 w-7 text-primary\" />\r\n          <span className=\"text-xl font-semibold group-data-[collapsible=icon]:hidden\">QAK</span>\r\n        </Link>\r\n      </SidebarHeader>\r\n      <SidebarContent>\r\n        <SidebarMenu>\r\n          {mainNavItems.map((item) => (\r\n            <SidebarMenuItem key={item.href}>\r\n              <Link href={item.href} legacyBehavior passHref>\r\n                <SidebarMenuButton\r\n                  asChild\r\n                  isActive={isActive(item.href)}\r\n                  tooltip={{ children: item.title, className: \"group-data-[collapsible=icon]:block hidden\" }}\r\n                >\r\n                  <a>\r\n                    {item.icon}\r\n                    <span>{item.title}</span>\r\n                  </a>\r\n                </SidebarMenuButton>\r\n              </Link>\r\n            </SidebarMenuItem>\r\n          ))}\r\n        </SidebarMenu>\r\n      </SidebarContent>\r\n      <SidebarSeparator />\r\n      <SidebarFooter>\r\n        <SidebarMenu>\r\n          <SidebarMenuItem>\r\n            <Link href={settingsNavItem.href} legacyBehavior passHref>\r\n              <SidebarMenuButton \r\n                asChild \r\n                isActive={isActive(settingsNavItem.href)}\r\n                tooltip={{ children: settingsNavItem.title, className: \"group-data-[collapsible=icon]:block hidden\" }}\r\n              >\r\n                <a>\r\n                  {settingsNavItem.icon}\r\n                  <span>{settingsNavItem.title}</span>\r\n                </a>\r\n              </SidebarMenuButton>\r\n            </Link>\r\n          </SidebarMenuItem>\r\n        </SidebarMenu>\r\n      </SidebarFooter>\r\n    </Sidebar>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,gCAAgC;;;;;AAGhC;AACA;AACA;AAUA,4UAA6H,0BAA0B;AAAvJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAdA;;;;;;AAiBA,MAAM,eAA0B;IAC9B;QAAE,OAAO;QAAa,MAAM;QAAK,oBAAM,8OAAC,4NAAA,CAAA,kBAAe;;;;;IAAI;IAC3D;QAAE,OAAO;QAAY,MAAM;QAAa,oBAAM,8OAAC,sNAAA,CAAA,eAAY;;;;;IAAI;IAC/D;QAAE,OAAO;QAAY,MAAM;QAAa,oBAAM,8OAAC,gMAAA,CAAA,MAAG;;;;;IAAI;IACtD;QAAE,OAAO;QAAoB,MAAM;QAAY,oBAAM,8OAAC,0MAAA,CAAA,QAAK;;;;;IAAI;IAC/D;QAAE,OAAO;QAAyB,MAAM;QAA0B,oBAAM,8OAAC,oMAAA,CAAA,QAAK;;;;;IAAI;IAClF;QAAE,OAAO;QAAqB,MAAM;QAAY,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;;;;;IAAI;IACnE;QAAE,OAAO;QAAgB,MAAM;QAAiB,oBAAM,8OAAC,+MAAA,CAAA,QAAK;;;;;IAAI;CACjE;AAED,MAAM,kBAA2B;IAC/B,OAAO;IACP,MAAM;IACN,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;;;;;AACjB;AAEO,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK,OAAO,aAAa;QACtC,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,8OAAC,mIAAA,CAAA,UAAO;QAAC,aAAY;;0BACnB,8OAAC,mIAAA,CAAA,gBAAa;gBAAC,WAAU;0BACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;oBAAI,WAAU;;sCACvB,8OAAC,2NAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,8OAAC;4BAAK,WAAU;sCAA6D;;;;;;;;;;;;;;;;;0BAGjF,8OAAC,mIAAA,CAAA,iBAAc;0BACb,cAAA,8OAAC,mIAAA,CAAA,cAAW;8BACT,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,mIAAA,CAAA,kBAAe;sCACd,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAM,KAAK,IAAI;gCAAE,cAAc;gCAAC,QAAQ;0CAC5C,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;oCAChB,OAAO;oCACP,UAAU,SAAS,KAAK,IAAI;oCAC5B,SAAS;wCAAE,UAAU,KAAK,KAAK;wCAAE,WAAW;oCAA6C;8CAEzF,cAAA,8OAAC;;4CACE,KAAK,IAAI;0DACV,8OAAC;0DAAM,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;2BATH,KAAK,IAAI;;;;;;;;;;;;;;;0BAiBrC,8OAAC,mIAAA,CAAA,mBAAgB;;;;;0BACjB,8OAAC,mIAAA,CAAA,gBAAa;0BACZ,cAAA,8OAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,mIAAA,CAAA,kBAAe;kCACd,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAM,gBAAgB,IAAI;4BAAE,cAAc;4BAAC,QAAQ;sCACvD,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;gCAChB,OAAO;gCACP,UAAU,SAAS,gBAAgB,IAAI;gCACvC,SAAS;oCAAE,UAAU,gBAAgB,KAAK;oCAAE,WAAW;gCAA6C;0CAEpG,cAAA,8OAAC;;wCACE,gBAAgB,IAAI;sDACrB,8OAAC;sDAAM,gBAAgB,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9C", "debugId": null}}, {"offset": {"line": 1793, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/components/AppHeader.tsx"], "sourcesContent": ["\r\n\"use client\";\r\n\r\nimport { SidebarTrigger } from \"@/components/ui/sidebar\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { Sun, Moon, Github } from \"lucide-react\";\r\nimport { useTheme } from \"next-themes\"; // Assuming next-themes is or will be installed\r\nimport Link from \"next/link\";\r\n\r\n// Placeholder for ThemeProvider setup - typically in layout.tsx or a similar top-level component\r\n// import { ThemeProvider as NextThemesProvider } from \"next-themes\"\r\n// ...\r\n// <NextThemesProvider attribute=\"class\" defaultTheme=\"dark\" enableSystem>\r\n// ...\r\n// </NextThemesProvider>\r\n\r\nexport function AppHeader() {\r\n  // const { setTheme, theme } = useTheme(); // Uncomment when next-themes is integrated\r\n\r\n  return (\r\n    <header className=\"sticky top-0 z-10 flex h-16 items-center gap-4 border-b bg-background/80 backdrop-blur-sm px-4 md:px-6\">\r\n      <div className=\"md:hidden\">\r\n        <SidebarTrigger />\r\n      </div>\r\n      <div className=\"flex-1\">\r\n        {/* Can add breadcrumbs or page title here */}\r\n      </div>\r\n      <div className=\"flex items-center gap-2\">\r\n        {/* \r\n        // Theme Toggle - requires next-themes setup\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"icon\"\r\n          onClick={() => setTheme(theme === \"light\" ? \"dark\" : \"light\")}\r\n          aria-label=\"Toggle theme\"\r\n        >\r\n          <Sun className=\"h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\r\n          <Moon className=\"absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\r\n        </Button> \r\n        */}\r\n        <Button variant=\"ghost\" size=\"icon\" asChild>\r\n          <Link href=\"https://github.com/nahuelcio\" target=\"_blank\" aria-label=\"Nahuel Cio GitHub Profile\">\r\n            <Github className=\"h-5 w-5\" />\r\n          </Link>\r\n        </Button>\r\n      </div>\r\n    </header>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAEA;AANA;;;;;;AAeO,SAAS;IACd,sFAAsF;IAEtF,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,mIAAA,CAAA,iBAAc;;;;;;;;;;0BAEjB,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;0BAab,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,MAAK;oBAAO,OAAO;8BACzC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAA+B,QAAO;wBAAS,cAAW;kCACnE,cAAA,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9B", "debugId": null}}]}