"""Utilidades para ejecutar tests de manera síncrona."""

import os
from typing import Dict, Optional, Any
from src.Core.test_service import TestService


def run_smoke_test_sync(instructions: str, url: Optional[str] = None, user_story: Optional[str] = None, 
                       config_id: Optional[str] = None, configuration: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """Ejecuta un smoke test de manera síncrona en un proceso separado."""
    try:
        # Crear el servicio de pruebas
        test_service = TestService(api_key=os.environ.get("GOOGLE_API_KEY"))

        # Usar el método sincrónico que maneja su propio bucle de eventos
        result = test_service.run_smoke_test(
            instructions=instructions,
            url=url,
            user_story=user_story,
            config_id=config_id,
            configuration=configuration
        )

        # Asegurar que siempre devolvemos un resultado válido
        if result is None:
            return {
                "success": False,
                "error": "El test no devolvió ningún resultado"
            }
        
        # Si el resultado ya indica éxito/fallo, devolverlo tal como está
        if isinstance(result, dict) and 'success' in result:
            return result
        
        # Si el resultado no tiene el formato esperado, envolverlo
        return {
            "success": True,
            "result": result
        }

    except Exception as e:
        error_msg = str(e)
        print(f"Error en run_smoke_test_sync: {error_msg}")
        
        # No re-lanzar la excepción, solo devolver el error
        return {
            "success": False,
            "error": error_msg,
            "test_id": None,
            "history": None,
            "screenshot_paths": [],
            "history_path": None
        }


def run_full_test_sync(gherkin_scenario: str, url: Optional[str] = None) -> Dict[str, Any]:
    """Ejecuta un full test de manera síncrona en un proceso separado."""
    try:
        # Crear el servicio de pruebas
        test_service = TestService(api_key=os.environ.get("GOOGLE_API_KEY"))

        # Usar el método sincrónico que maneja su propio bucle de eventos
        result = test_service.run_full_test(
            gherkin_scenario=gherkin_scenario,
            url=url
        )

        return result

    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }
