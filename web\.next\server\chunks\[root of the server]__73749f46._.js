module.exports = {

"[project]/.next-internal/server/app/favicon.ico/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route.runtime.dev.js [external] (next/dist/compiled/next-server/app-route.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page.runtime.dev.js [external] (next/dist/compiled/next-server/app-page.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/app/favicon--route-entry.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "dynamic": (()=>dynamic)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-rsc] (ecmascript)");
;
const contentType = "image/x-icon";
const cacheControl = "public, max-age=0, must-revalidate";
const buffer = Buffer.from("AAABAAEAEBAAAAEAIABoBAAAFgAAACgAAAAQAAAAIAAAAAEAIAAAAAAAAAQAABMLAAATCwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/yXBNv8kwYT/JcGI/yXBiP8lwYj/JcGI/yXBiP8lwYj/JMGE/yXBNgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP8rs5T/VaoD/ym0H/8qqgYAAAAAAAAAAAAAAAAAAAAA/1WqA/8rs5QAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD/L6eAAAAAAP8wpsT/L6ZlAAAAAP8qqhL/L6Zw/yqqEgAAAAD/L6eAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/zWZigAAAAD/N5cg/ySRBwAAAAD/NZhy/zeXSv82mXEAAAAA/zWZigAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP88ijv/O4uF/z9/CAAAAAAAAAAA/zWGE/88i3H/PY4Z/zuLhf89jDoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/z9/HP9BfZT/RH8eAAAAAAAAAAD/RH8e/0F9lP8/fxwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD/VX8G/0hxhQAAAAAAAAAA/0hxhf9VfwYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP9NY4AAAAAA/01kWf9MZJ0AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD/U1WAAAAAAP9VVR7/UladAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/1lJgAAAAAD/WEdZ/1lJnQAAAAAAAAAA/6rfMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/186MP9fO5b/Xjx3/148d/9fO5b/Xzow/7arLv+4reD/tqsuAAAAAAAAAAAAAAAAAAAAAAAAAAD/scuK/7PPG/9pLRH/ZC2Y/2Qumf9kLpn/ZC2Y/2ktEQAAAAD/xHowAAAAAAAAAAAAAAAAAAAAAAAAAAD/uJ0v/7uf2/+6nooAAAAA/2keEf9pIHb/aSB2/2keEQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP/DcS8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA//8AAOAHAADh5wAA6RcAAOkXAADjBwAA8Y8AAPmfAAD9PwAA/T8AAP03AAD4AwAA4BcAAMQ/AADv/wAA//8AAA==", 'base64');
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
}
function GET() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["NextResponse"](buffer, {
        headers: {
            'Content-Type': contentType,
            'Cache-Control': cacheControl
        }
    });
}
const dynamic = 'force-static';
}}),

};

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__73749f46._.js.map