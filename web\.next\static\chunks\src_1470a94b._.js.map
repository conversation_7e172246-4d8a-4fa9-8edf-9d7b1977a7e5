{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border bg-card text-card-foreground shadow-sm relative\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nconst DevCard = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, children, ...props }, ref) => (\r\n  <Card ref={ref} className={cn(\"flex flex-col\", className)} {...props}>\r\n    {children}\r\n\r\n  </Card>\r\n))\r\nDevCard.displayName = \"DevCard\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent, DevCard }\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qEACA;QAED,GAAG,KAAK;;;;;;KAVP;AAaN,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,wBAAU,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAG7B,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;QAAK,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAAa,GAAG,KAAK;kBACjE;;;;;;;AAIL,QAAQ,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/app/projects/loading.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from \"@/components/ui/card\";\r\n\r\nexport default function ProjectsLoading() {\r\n  return (\r\n    <div>\r\n      <div className=\"flex justify-between items-center mb-6\">\r\n        <Skeleton className=\"h-10 w-1/4\" /> \r\n        <Skeleton className=\"h-9 w-36\" /> \r\n      </div>\r\n      <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\">\r\n        {[...Array(3)].map((_, i) => (\r\n          <Card key={i}>\r\n            <CardHeader>\r\n              <Skeleton className=\"h-6 w-3/4 mb-2\" />\r\n              <Skeleton className=\"h-4 w-full\" />\r\n              <Skeleton className=\"h-4 w-2/3 mt-1\" />\r\n            </CardHeader>\r\n            <CardContent>\r\n              <Skeleton className=\"h-4 w-1/4 mb-2\" />\r\n              <div className=\"flex gap-1 mt-1\">\r\n                <Skeleton className=\"h-5 w-12 rounded-full\" />\r\n                <Skeleton className=\"h-5 w-16 rounded-full\" />\r\n              </div>\r\n              <Skeleton className=\"h-3 w-1/2 mt-3\" />\r\n            </CardContent>\r\n            <CardFooter>\r\n              <Skeleton className=\"h-9 w-full\" />\r\n            </CardFooter>\r\n          </Card>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,qBACE,6LAAC;;0BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC,uIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;;0BAEtB,6LAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,uIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC,uIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC,uIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAEtB,6LAAC,mIAAA,CAAA,cAAW;;kDACV,6LAAC,uIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;kDAEtB,6LAAC,uIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAEtB,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC,uIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;uBAfb;;;;;;;;;;;;;;;;AAsBrB;KA/BwB", "debugId": null}}]}