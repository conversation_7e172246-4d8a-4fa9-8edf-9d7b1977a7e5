"""Rutas de API para verificación de salud del sistema."""

import os
from datetime import datetime
from fastapi import APIRouter

# Router para verificación de salud
router = APIRouter(prefix="/api", tags=["health"])


@router.get("/health", summary="Verificar estado de la API")
async def get_status():
    """Verifica el estado de la API y devuelve información básica."""
    return {
        "status": "online",
        "version": "2.0.0",
        "timestamp": datetime.now().isoformat(),
        "api_key_configured": bool(os.environ.get("GOOGLE_API_KEY"))
    }
