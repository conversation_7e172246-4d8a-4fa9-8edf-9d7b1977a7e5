"""Utilidades para transformar respuestas entre backend y frontend."""

import os
import tempfile
import json
from typing import Dict, List, Optional, Any


def transform_backend_response_to_frontend_format(backend_result: Dict[str, Any]) -> Dict[str, Any]:
    """
    Transforma la respuesta del backend al formato esperado por el frontend.

    Args:
        backend_result: Resultado del backend

    Returns:
        Datos en el formato esperado por el frontend
    """
    # Extraer datos del historial si existe
    history = backend_result.get("history", {})

    # Transformar acciones
    actions = []
    if "model_actions" in history:
        for i, action_data in enumerate(history["model_actions"]):
            action_type = "unknown"
            details = action_data

            # Determinar el tipo de acción
            if isinstance(action_data, dict):
                for key in action_data.keys():
                    if key in ["click_element", "input_text", "navigate", "get_xpath_of_element"]:
                        action_type = key
                        break

            actions.append({
                "step": i + 1,
                "type": action_type,
                "details": details
            })

    # Transformar resultados
    results = []
    if "extracted_content" in history:
        for i, content in enumerate(history["extracted_content"]):
            results.append({
                "step": i + 1,
                "content": str(content),
                "success": True  # Asumir éxito si no hay información específica
            })

    # Transformar elementos
    elements = []
    if "model_actions" in history:
        for i, action_data in enumerate(history["model_actions"]):
            if isinstance(action_data, dict) and "interacted_element" in action_data:
                element_info = action_data["interacted_element"]
                if element_info:
                    # Extraer información del elemento
                    element_str = str(element_info)
                    import re
                    xpath_match = re.search(r"xpath=['\"]([^'\"]+)['\"]", element_str)
                    tag_match = re.search(r"tag=['\"]([^'\"]+)['\"]", element_str)

                    elements.append({
                        "step": i + 1,
                        "tag_name": tag_match.group(1) if tag_match else "unknown",
                        "xpath": xpath_match.group(1) if xpath_match else "",
                        "attributes": {}  # Podríamos extraer más atributos si están disponibles
                    })

    # Transformar URLs
    urls = []
    if "urls" in history:
        for i, url in enumerate(history["urls"]):
            urls.append({
                "step": i + 1,
                "url": str(url),
                "title": ""  # No tenemos título en el historial actual
            })

    # Transformar errores
    errors = history.get("errors", [])

    # Transformar capturas de pantalla
    screenshots = []
    
    # Primero verificar si hay capturas en el backend_result.screenshot_paths (para compatibilidad)
    screenshot_paths = backend_result.get("screenshot_paths", [])
    
    # También verificar si hay capturas ya procesadas en el history
    if "screenshots" in history and history["screenshots"]:
        screenshot_paths.extend(history["screenshots"])
    
    # Si no hay capturas en ningún lado, intentar extraerlas directamente de los datos de historial
    if not screenshot_paths and "history" in backend_result:
        # Usar la función de extracción de capturas de pantalla
        from src.Utilities.project_manager_service import extract_screenshots_from_json
        
        try:
            # Limpiar los datos antes de crear el archivo temporal para evitar errores de serialización
            cleaned_backend_result = clean_data_for_json_serialization(backend_result)
            
            # Crear un archivo temporal con los datos de historial limpiados
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
                json.dump(cleaned_backend_result, temp_file, indent=2)
                temp_file_path = temp_file.name
            
            # Extraer capturas de pantalla
            extracted_screenshots = extract_screenshots_from_json(temp_file_path)
            screenshot_paths.extend(extracted_screenshots)
            
            # Limpiar archivo temporal
            os.unlink(temp_file_path)
        except Exception as e:
            print(f"Error al extraer capturas de pantalla: {str(e)}")
    
    # Si aún no hay capturas pero tenemos un history_path, intentar extraer del archivo de historial
    if not screenshot_paths and "history_path" in backend_result:
        from src.Utilities.project_manager_service import extract_screenshots_from_json
        
        try:
            history_file_path = backend_result["history_path"]
            if history_file_path and os.path.exists(history_file_path):
                # Extraer capturas directamente del archivo de historial
                extracted_screenshots = extract_screenshots_from_json(history_file_path)
                screenshot_paths.extend(extracted_screenshots)
        except Exception as e:
            print(f"Error al extraer capturas de pantalla desde history_path: {str(e)}")
    
    # Procesar las rutas de capturas de pantalla
    for path in screenshot_paths:
        # Convertir rutas de archivos a URLs servibles
        if isinstance(path, str):
            if path.startswith("data:image"):
                # Es una imagen base64, mantener como está
                screenshots.append(path)
            elif path.startswith("http"):
                # Es una URL, mantener como está
                screenshots.append(path)
            elif path.startswith("tests/"):
                # Es una ruta relativa desde el directorio tests
                screenshots.append(f"/api/screenshots/{path[6:]}")  # Remover "tests/" del inicio
            elif "/" in path:
                # Es una ruta que contiene directorios
                # Extraer solo la parte después de "tests" si existe
                if "tests/" in path:
                    relative_path = path.split("tests/", 1)[1]
                    screenshots.append(f"/api/screenshots/{relative_path}")
                else:
                    screenshots.append(f"/api/screenshots/{path}")
            else:
                # Es solo un nombre de archivo
                screenshots.append(f"/api/screenshots/{path}")
        else:
            # Convertir a string si no es string
            screenshots.append(str(path))

    # Crear metadata
    # Usar los metadatos del backend si están disponibles, sino calcular
    backend_metadata = backend_result.get("metadata", {})
    
    # Calcular total_steps como el máximo entre acciones y resultados
    # para manejar casos donde uno esté vacío pero el otro tenga datos
    total_steps_calculated = max(len(actions), len(results))
    
    # Usar el total_steps del backend si existe, sino usar el calculado
    total_steps = backend_metadata.get("total_steps", total_steps_calculated)

    metadata = {
        "start_time": backend_metadata.get("start_time"),
        "end_time": backend_metadata.get("end_time"),
        "total_steps": total_steps,
        "success": backend_result.get("success", False)
    }

    # Crear la respuesta en el formato esperado por el frontend
    frontend_response = {
        "actions": actions,
        "results": results,
        "elements": elements,
        "urls": urls,
        "errors": errors,
        "screenshots": screenshots,
        "metadata": metadata,
        "generated_gherkin": backend_result.get("gherkin_scenario", ""),
        "generatedGherkin": backend_result.get("gherkin_scenario", ""),  # Ambos formatos por compatibilidad
        "test_id": backend_result.get("test_id"),
        "history_path": backend_result.get("history_path")
    }

    return frontend_response


def clean_data_for_json_serialization(data: Any) -> Any:
    """
    Limpia los datos para que sean serializables en JSON.
    Convierte objetos no serializables a strings o diccionarios.
    Implementa manejo específico para objetos DOMHistoryElement.

    Args:
        data: Datos a limpiar

    Returns:
        Datos limpios serializables en JSON
    """
    if data is None:
        return None
    elif isinstance(data, (str, int, float, bool)):
        return data
    elif isinstance(data, dict):
        return {key: clean_data_for_json_serialization(value) for key, value in data.items()}
    elif isinstance(data, (list, tuple)):
        return [clean_data_for_json_serialization(item) for item in data]
    elif hasattr(data, '__class__') and 'DOMHistoryElement' in str(data.__class__):
        # Manejar específicamente objetos DOMHistoryElement
        try:
            # Intentar extraer información útil del objeto
            element_str = str(data)
            # Buscar xpath en la representación string
            import re
            xpath_match = re.search(r"xpath=['\"]([^'\"]+)['\"]", element_str)
            tag_match = re.search(r"tag=['\"]([^'\"]+)['\"]", element_str)

            result = {
                "type": "DOMHistoryElement",
                "string_representation": element_str
            }

            if xpath_match:
                result["xpath"] = xpath_match.group(1)
            if tag_match:
                result["tag"] = tag_match.group(1)

            # Intentar extraer atributos específicos si están disponibles
            try:
                if hasattr(data, 'tag_name'):
                    result["tag_name"] = str(data.tag_name) if data.tag_name else ""
                if hasattr(data, 'xpath'):
                    result["xpath"] = str(data.xpath) if data.xpath else ""
                if hasattr(data, 'attributes'):
                    result["attributes"] = clean_data_for_json_serialization(data.attributes) if data.attributes else {}
            except:
                pass  # Ignorar errores al acceder a atributos específicos

            return result
        except Exception as e:
            return {"type": "DOMHistoryElement", "error": f"Could not serialize: {str(e)}"}
    elif hasattr(data, '__dict__'):
        # Para objetos con atributos, convertir a diccionario
        try:
            return {key: clean_data_for_json_serialization(value) for key, value in data.__dict__.items()}
        except Exception as e:
            return {"type": str(type(data)), "serialization_error": str(e)}
    else:
        # Para cualquier otro tipo, convertir a string de manera segura
        try:
            return str(data)
        except Exception as e:
            return f"<Non-serializable object: {type(data).__name__}>"


def convert_screenshot_paths_to_urls(screenshots: List[str]) -> List[str]:
    """
    Convierte rutas de archivos de capturas de pantalla a URLs servibles.
    
    Args:
        screenshots: Lista de rutas de capturas de pantalla
        
    Returns:
        Lista de URLs servibles
    """
    converted_screenshots = []
    
    for screenshot_path in screenshots:
        if isinstance(screenshot_path, str):
            if screenshot_path.startswith("data:image"):
                # Es una imagen base64, mantener como está
                converted_screenshots.append(screenshot_path)
            elif screenshot_path.startswith("http"):
                # Es una URL, mantener como está
                converted_screenshots.append(screenshot_path)
            elif screenshot_path.startswith("tests/"):
                # Es una ruta relativa desde el directorio tests
                converted_screenshots.append(f"/api/screenshots/{screenshot_path[6:]}")
            elif "/" in screenshot_path:
                # Es una ruta que contiene directorios
                if "tests/" in screenshot_path:
                    relative_path = screenshot_path.split("tests/", 1)[1]
                    converted_screenshots.append(f"/api/screenshots/{relative_path}")
                else:
                    converted_screenshots.append(f"/api/screenshots/{screenshot_path}")
            else:
                # Es solo un nombre de archivo
                converted_screenshots.append(f"/api/screenshots/{screenshot_path}")
        else:
            converted_screenshots.append(str(screenshot_path))
    
    return converted_screenshots
