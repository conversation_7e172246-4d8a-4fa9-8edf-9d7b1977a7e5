from typing import Dict, List
import os
import requests
import re
from dotenv import load_dotenv

load_dotenv()

class PRDiffAnalyzer:
    def __init__(self):
        self.github_token = os.getenv('GITHUB_TOKEN')
        self.github_repo = os.getenv('GITHUB_REPO')  # formato: "owner/repo"
        self.github_api_base = "https://api.github.com"

        if not self.github_token or not self.github_repo:
            raise ValueError("GITHUB_TOKEN and GITHUB_REPO environment variables are required")

        self.headers = {
            'Authorization': f'token {self.github_token}',
            'Accept': 'application/vnd.github.v3+json'
        }

    def get_pr_diff(self, pr_number: int) -> Dict:
        """
        Obtiene el diff y análisis del PR directamente de la API de GitHub
        """
        try:
            # Obtener detalles del PR
            pr_url = f"{self.github_api_base}/repos/{self.github_repo}/pulls/{pr_number}"
            pr_response = requests.get(pr_url, headers=self.headers)
            
            if pr_response.status_code != 200:
                raise Exception(f"Error getting PR details: {pr_response.json()}")

            pr_data = pr_response.json()

            # Obtener el diff
            diff_headers = {**self.headers, 'Accept': 'application/vnd.github.v3.diff'}
            diff_response = requests.get(
                f"{self.github_api_base}/repos/{self.github_repo}/pulls/{pr_number}.diff",
                headers=diff_headers
            )
            
            if diff_response.status_code != 200:
                raise Exception("Error getting PR diff")

            diff_content = diff_response.text

            # Analizar el contenido
            analysis = {
                "pr_number": pr_number,
                "pr_title": pr_data['title'],
                "pr_description": pr_data['body'] or "",
                "author": pr_data['user']['login'],
                "state": pr_data['state'],
                "diff_content": diff_content,
                "affected_files": self._get_affected_files(diff_content),
                "changes_summary": self._analyze_changes(diff_content)
            }

            # Agregar contexto del repositorio
            analysis["repository_context"] = self._get_repository_context(analysis["affected_files"])
            
            return analysis
        except Exception as e:
            raise Exception(f"Error analyzing PR: {str(e)}")

    def _get_affected_files(self, diff_content: str) -> List[str]:
        """
        Extrae la lista de archivos afectados del diff
        """
        files = []
        for line in diff_content.split('\n'):
            if line.startswith('diff --git'):
                file_path = line.split()[-1].lstrip('b/')
                files.append(file_path)
        return files

    def _analyze_changes(self, diff_content: str) -> Dict:
        """
        Analiza los cambios para determinar el tipo y alcance de las modificaciones
        """
        changes = {
            "additions": 0,
            "deletions": 0,
            "modified_functions": [],
            "api_changes": False,
            "test_changes": False,
            "risk_level": "low"
        }

        current_file = ""
        in_function = False
        current_function = None

        for line in diff_content.split('\n'):
            if line.startswith('diff --git'):
                current_file = line.split()[-1].lstrip('b/')
                in_function = False
                current_function = None
            elif line.startswith('+') and not line.startswith('+++'):
                changes["additions"] += 1
                
                # Detectar definiciones de funciones/clases
                if re.search(r'^\+\s*(def|class)\s+\w+', line):
                    func_match = re.search(r'^\+\s*(def|class)\s+(\w+)', line)
                    if func_match:
                        current_function = {
                            "name": func_match.group(2),
                            "type": func_match.group(1),
                            "file": current_file
                        }
                        changes["modified_functions"].append(current_function)
                        in_function = True
                
            elif line.startswith('-') and not line.startswith('---'):
                changes["deletions"] += 1

            # Detectar cambios en API
            if 'api/' in current_file.lower() or '/api/' in current_file.lower():
                changes["api_changes"] = True
                changes["risk_level"] = "high"

            # Detectar cambios en tests
            if '/tests/' in current_file or '_test' in current_file:
                changes["test_changes"] = True
        
        return changes

    def _get_repository_context(self, affected_files: List[str]) -> Dict:
        """
        Obtiene contexto adicional del repositorio para los archivos afectados
        """
        context = {}
        for file_path in affected_files:
            try:
                # Obtener el contenido del archivo en la rama base
                file_url = f"{self.github_api_base}/repos/{self.github_repo}/contents/{file_path}"
                response = requests.get(file_url, headers=self.headers)
                
                if response.status_code == 200:
                    file_info = response.json()
                    context[file_path] = {
                        "exists": True,
                        "type": "file",
                        "size": file_info.get('size', 0),
                        "path": file_info.get('path', file_path)
                    }
            except Exception as e:
                context[file_path] = {
                    "exists": False,
                    "type": "unknown",
                    "error": str(e)
                }
        return context

    def suggest_test_cases(self, pr_analysis: Dict) -> List[Dict]:
        """
        Sugiere casos de prueba basados en los cambios del PR
        """
        test_suggestions = []
        changes = pr_analysis["changes_summary"]

        # Sugerencias basadas en cambios en la API
        if changes["api_changes"]:
            test_suggestions.append({
                "type": "Integration",
                "category": "API",
                "description": "Pruebas de integración para endpoints modificados",
                "priority": "High",
                "suggested_framework": "pytest",
                "coverage_focus": ["response_validation", "error_handling"]
            })

        # Sugerencias para funciones modificadas
        for func in changes["modified_functions"]:
            test_suggestions.append({
                "type": "Unit",
                "category": "Function",
                "description": f"Pruebas unitarias para: {func['name']}",
                "priority": "Medium",
                "function_info": func,
                "suggested_framework": "pytest",
                "coverage_focus": ["input_validation", "edge_cases"]
            })

        # Si hay muchos cambios sin pruebas
        if changes["additions"] > 10 and not changes["test_changes"]:
            test_suggestions.append({
                "type": "Coverage",
                "category": "General",
                "description": "Aumentar cobertura de pruebas para los nuevos cambios",
                "priority": "High",
                "suggested_framework": "pytest",
                "coverage_focus": ["unit_tests", "integration_tests"]
            })

        return test_suggestions

    def suggest_test_cases(self, pr_analysis: Dict) -> List[Dict]:
        """
        Suggests test cases based on the PR changes
        """
        test_suggestions = []

        # If API changes detected, suggest API tests
        if pr_analysis["changes_summary"]["api_changes"]:
            test_suggestions.append({
                "type": "API",
                "description": "Add integration tests for modified API endpoints",
                "priority": "High"
            })

        # For each modified function, suggest unit tests
        for func_name in pr_analysis["changes_summary"]["modified_functions"]:
            test_suggestions.append({
                "type": "Unit",
                "description": f"Add unit tests for modified function: {func_name}",
                "priority": "Medium"
            })

        return test_suggestions
