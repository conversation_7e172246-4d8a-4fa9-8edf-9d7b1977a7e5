{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border bg-card text-card-foreground shadow-sm relative\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nconst DevCard = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, children, ...props }, ref) => (\r\n  <Card ref={ref} className={cn(\"flex flex-col\", className)} {...props}>\r\n    {children}\r\n\r\n  </Card>\r\n))\r\nDevCard.displayName = \"DevCard\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent, DevCard }\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qEACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,wBAAU,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG7B,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;QAAK,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAAa,GAAG,KAAK;kBACjE;;;;;;AAIL,QAAQ,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\r\n        outline: \"text-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Tabs = TabsPrimitive.Root\r\n\r\nconst TabsList = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.List\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsList.displayName = TabsPrimitive.List.displayName\r\n\r\nconst TabsTrigger = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\r\n\r\nconst TabsContent = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Content\r\n    ref={ref}\r\n    className={cn(\r\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsContent.displayName = TabsPrimitive.Content.displayName\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/lib/config.ts"], "sourcesContent": ["export const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api';\r\n\r\n"], "names": [], "mappings": ";;;AAAO,MAAM,eAAe,QAAQ,GAAG,CAAC,wBAAwB,IAAI", "debugId": null}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/lib/api.ts"], "sourcesContent": ["import { API_BASE_URL } from '@/lib/config';\r\nimport type {\r\n  ApiResponse,\r\n  Project, ProjectCreateInput, ProjectUpdateInput,\r\n  TestSuite, TestSuiteCreateInput, TestSuiteUpdateInput,\r\n  TestCase, TestCaseCreateInput, TestCaseUpdateInput, TestCaseStatusUpdateInput,\r\n  ApiHealth,\r\n  TestCaseExecutionResponse,\r\n  SuiteExecutionResponse,\r\n  TestExecutionHistoryData,\r\n  GenerateGherkinInput, GenerateGherkinOutput,\r\n  GenerateCodeInput, GenerateCodeOutput,\r\n  EnhanceUserStoryInput, EnhanceUserStoryOutput,\r\n  ExecuteSmokeTestInput, ExecuteSmokeTestOutput,\r\n  GenerateManualTestCasesInput, GenerateManualTestCasesOutput,\r\n  SummarizeTestResultsInput, SummarizeTestResultsOutput,\r\n  PlaywrightCodegenRequest,\r\n  CodegenSessionInfo,\r\n  CodegenTestCaseRequest,\r\n  CodegenStatsResponse,\r\n  CodegenSessionListResponse,\r\n  CodegenHealthResponse,\r\n  CodegenHistoryResponse,\r\n  CodegenHistorySessionDetailResponse,\r\n  CodegenExecutionRequest,\r\n  CodegenExecutionResponse,\r\n  CodegenExecutionInfo,\r\n  CodegenExecutionListResponse\r\n} from '@/lib/types';\r\n\r\n\r\nasync function fetchApi<T>(url: string, options?: RequestInit): Promise<T> {\r\n  const response = await fetch(`${API_BASE_URL}${url}`, {\r\n    ...options,\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      'ngrok-skip-browser-warning': 'true', // 🔧 Agregar header para evitar bloqueo de ngrok\r\n      ...(options?.headers),\r\n    },\r\n  });\r\n\r\n  if (!response.ok) {\r\n    let errorData;\r\n    let errorMessage = `HTTP error! status: ${response.status} for URL: ${url}`;\r\n    try {\r\n      const contentType = response.headers.get(\"content-type\");\r\n      if (contentType && contentType.indexOf(\"application/json\") !== -1) {\r\n        errorData = await response.json();\r\n        errorMessage = errorData?.error || errorData?.details || errorMessage;\r\n      } else {\r\n        const errorText = await response.text();\r\n        // If HTML error page, include a snippet.\r\n        if (errorText.toLowerCase().includes(\"<!doctype html\")) {\r\n            errorMessage = `${errorMessage}. Server returned an HTML error page. Snippet: ${errorText.substring(0, 200)}...`;\r\n        } else {\r\n            errorMessage = `${errorMessage}. Response: ${errorText.substring(0, 500)}${errorText.length > 500 ? '...' : ''}`;\r\n        }\r\n      }\r\n    } catch (e) {\r\n      // If parsing as JSON or text fails, stick with the basic error from statusText or status code.\r\n      errorMessage = `${response.statusText || `HTTP error! status: ${response.status} for URL: ${url}`}. Could not parse error response.`;\r\n    }\r\n    throw new Error(errorMessage);\r\n  }\r\n  // Handle cases where response is OK but content might be empty for 204 No Content\r\n  if (response.status === 204) {\r\n    return {} as T; // Or handle as appropriate for your application\r\n  }\r\n  return response.json();\r\n}\r\n\r\n// Project Endpoints\r\nexport const getProjects = (): Promise<ApiResponse<Project>> => fetchApi<ApiResponse<Project>>('/projects/');\r\nexport const createProject = (data: ProjectCreateInput): Promise<Project> => fetchApi<Project>('/projects/', { method: 'POST', body: JSON.stringify(data) });\r\nexport const getProjectById = (projectId: string): Promise<Project> => fetchApi<Project>(`/projects/${projectId}`);\r\nexport const updateProject = (projectId: string, data: ProjectUpdateInput): Promise<Project> => fetchApi<Project>(`/projects/${projectId}`, { method: 'PUT', body: JSON.stringify(data) });\r\nexport const deleteProject = (projectId: string): Promise<void> => fetchApi<void>(`/projects/${projectId}`, { method: 'DELETE' });\r\n\r\n// Test Suite Endpoints\r\nexport const getSuitesByProjectId = (projectId: string): Promise<ApiResponse<TestSuite>> => fetchApi<ApiResponse<TestSuite>>(`/projects/${projectId}/suites`);\r\nexport const createSuite = (projectId: string, data: TestSuiteCreateInput): Promise<TestSuite> => fetchApi<TestSuite>(`/projects/${projectId}/suites/`, { method: 'POST', body: JSON.stringify(data) });\r\nexport const getSuiteById = (projectId: string, suiteId: string): Promise<TestSuite> => fetchApi<TestSuite>(`/projects/${projectId}/suites/${suiteId}`);\r\nexport const updateSuite = (projectId: string, suiteId: string, data: TestSuiteUpdateInput): Promise<TestSuite> => fetchApi<TestSuite>(`/projects/${projectId}/suites/${suiteId}`, { method: 'PUT', body: JSON.stringify(data) });\r\nexport const deleteSuite = (projectId: string, suiteId: string): Promise<void> => fetchApi<void>(`/projects/${projectId}/suites/${suiteId}`, { method: 'DELETE' });\r\nexport const executeSuite = (projectId: string, suiteId: string): Promise<SuiteExecutionResponse> => fetchApi<SuiteExecutionResponse>(`/projects/${projectId}/suites/${suiteId}/execute`, { method: 'POST' });\r\n\r\n// Test Case Endpoints\r\nexport const getTestCasesBySuiteId = (projectId: string, suiteId: string): Promise<ApiResponse<TestCase>> => fetchApi<ApiResponse<TestCase>>(`/projects/${projectId}/suites/${suiteId}/tests`);\r\nexport const createTestCase = (projectId: string, suiteId: string, data: TestCaseCreateInput): Promise<TestCase> => fetchApi<TestCase>(`/projects/${projectId}/suites/${suiteId}/tests/`, { method: 'POST', body: JSON.stringify(data) });\r\nexport const getTestCaseById = (projectId: string, suiteId: string, testId: string): Promise<TestCase> => fetchApi<TestCase>(`/projects/${projectId}/suites/${suiteId}/tests/${testId}`);\r\nexport const updateTestCase = (projectId: string, suiteId: string, testId: string, data: TestCaseUpdateInput): Promise<TestCase> => fetchApi<TestCase>(`/projects/${projectId}/suites/${suiteId}/tests/${testId}`, { method: 'PUT', body: JSON.stringify(data) });\r\nexport const updateTestCaseStatus = (projectId: string, suiteId: string, testId: string, data: TestCaseStatusUpdateInput): Promise<TestCase> => fetchApi<TestCase>(`/projects/${projectId}/suites/${suiteId}/tests/${testId}/status`, { method: 'PATCH', body: JSON.stringify(data) });\r\nexport const deleteTestCase = (projectId: string, suiteId: string, testId: string): Promise<void> => fetchApi<void>(`/projects/${projectId}/suites/${suiteId}/tests/${testId}`, { method: 'DELETE' });\r\nexport const executeTestCase = (projectId: string, suiteId: string, testId: string): Promise<TestCaseExecutionResponse> => fetchApi<TestCaseExecutionResponse>(`/projects/${projectId}/suites/${suiteId}/tests/${testId}/execute`, { method: 'POST' });\r\n\r\n// New function to execute test case using smoke test logic\r\nexport const executeTestCaseAsSmokeTest = async (projectId: string, suiteId: string, testId: string): Promise<ExecuteSmokeTestOutput> => {\r\n  // First, get the test case details\r\n  const testCase = await getTestCaseById(projectId, suiteId, testId);\r\n\r\n  // Create smoke test input from test case data\r\n  const smokeTestInput: ExecuteSmokeTestInput = {\r\n    instructions: testCase.instrucciones || '',\r\n    baseUrl: testCase.url || '',\r\n    userStory: testCase.historia_de_usuario || ''\r\n  };\r\n\r\n  // Execute using the same smoke test logic\r\n  return callExecuteSmokeTest(smokeTestInput);\r\n};\r\n\r\n// API Health\r\nexport const getApiHealth = (): Promise<ApiHealth> => fetchApi<ApiHealth>('/health');\r\n\r\nexport const getTestExecutionHistoryDetails = async (historyPath: string): Promise<TestExecutionHistoryData> => {\r\n  // Use the new backend endpoint that processes the history and extracts screenshots\r\n  const response = await fetchApi<TestExecutionHistoryData>(`/history/${encodeURIComponent(historyPath)}`);\r\n  return response;\r\n};\r\n\r\n\r\n// AI Tool Endpoints\r\n\r\n// Uses the API endpoint from OpenAPI spec\r\nexport async function callEnhanceUserStory(input: EnhanceUserStoryInput): Promise<EnhanceUserStoryOutput> {\r\n  // Converting our input to match the API schema\r\n  const apiInput = {\r\n    user_story: input.userStory,\r\n    language: input.language\r\n  };\r\n\r\n  try {\r\n    // Using the documented endpoint\r\n    const result = await fetchApi<any>('/stories/enhance', {\r\n      method: 'POST',\r\n      body: JSON.stringify(apiInput),\r\n    });\r\n    return {\r\n      enhancedUserStory: result.enhanced_story || result.enhancedUserStory || '',\r\n    };\r\n  } catch (error) {\r\n    throw new Error(`Failed to enhance user story: ${(error as Error).message}`);\r\n  }\r\n}\r\n\r\nexport async function callGenerateManualTestCases(input: GenerateManualTestCasesInput): Promise<GenerateManualTestCasesOutput> {\r\n  // Converting our input to match the API schema\r\n  const apiInput = {\r\n    enhanced_story: input.userStory,\r\n    language: input.language\r\n  };\r\n\r\n  try {\r\n    // Using the documented endpoint\r\n    const result = await fetchApi<any>('/stories/generate-manual-tests', {\r\n      method: 'POST',\r\n      body: JSON.stringify(apiInput),\r\n    });    // Convert API response to our expected output format\r\n    let manualTestCases: (string | any)[] = [];\r\n    \r\n    // Handle different response formats from the API\r\n    if (result && result.manual_tests) {\r\n      if (Array.isArray(result.manual_tests)) {\r\n        // Check if it's an array of objects (new format) or strings (old format)\r\n        if (result.manual_tests.length > 0 && typeof result.manual_tests[0] === 'object') {\r\n          // New format: array of test case objects - keep as objects\r\n          manualTestCases = result.manual_tests;\r\n        } else {\r\n          // Old format: direct array of strings\r\n          manualTestCases = result.manual_tests;\r\n        }\r\n      } else if (typeof result.manual_tests === 'string') {\r\n        // String response - might be JSON or plain text\r\n        try {\r\n          // Try parsing as JSON\r\n          const parsed = JSON.parse(result.manual_tests);\r\n          if (Array.isArray(parsed)) {\r\n            if (parsed.length > 0 && typeof parsed[0] === 'object') {\r\n              // Array of objects - keep as objects\r\n              manualTestCases = parsed;\r\n            } else {\r\n              // Array of strings\r\n              manualTestCases = parsed;\r\n            }\r\n          } else {\r\n            // Create a single item array if it's an object\r\n            manualTestCases = [parsed];\r\n          }\r\n        } catch (e) {\r\n          // If parsing fails, split lines\r\n          manualTestCases = result.manual_tests.split('\\n').filter((line: string) => line.trim().length > 0);\r\n        }\r\n      }\r\n    } else if (result && Array.isArray(result.manualTestCases)) {\r\n      // Alternative field name\r\n      manualTestCases = result.manualTestCases;\r\n    } else if (result && typeof result === 'object') {\r\n      // Try to use the result directly\r\n      manualTestCases = [result];\r\n    }\r\n\r\n    return { manualTestCases };\r\n  } catch (error) {\r\n    console.error(\"Manual test generation error:\", error);\r\n    throw new Error(`Failed to generate manual test cases: ${(error as Error).message}`);\r\n  }\r\n}\r\n\r\n// Uses the API endpoint from OpenAPI spec\r\nexport async function callGenerateGherkin(input: GenerateGherkinInput): Promise<GenerateGherkinOutput> {\r\n  // For generating Gherkin from manual test cases\r\n  if (input.instructions) {\r\n    try {\r\n      // Using the documented endpoint for generating Gherkin from manual tests\r\n      const apiInput = {\r\n        manual_tests: Array.isArray(input.instructions)\r\n          ? input.instructions.join('\\n')\r\n          : input.instructions,\r\n        language: input.language\r\n      };\r\n\r\n      const result = await fetchApi<any>('/stories/generate-gherkin', {\r\n        method: 'POST',\r\n        body: JSON.stringify(apiInput),\r\n      });\r\n\r\n      // Convert API response to our expected output format\r\n      return {\r\n        gherkin: result.gherkin || result.gherkin_scenario || '',\r\n      };\r\n    } catch (error) {\r\n      throw new Error(`Failed to generate Gherkin: ${(error as Error).message}`);\r\n    }\r\n  }\r\n  // For generating Gherkin directly from instructions and URL\r\n  else {\r\n    try {\r\n      // Using the documented endpoint for generating Gherkin directly\r\n      const apiInput = {\r\n        instructions: input.userStory || '',\r\n        url: input.url || '',\r\n        user_story: input.userStory || '',\r\n        language: input.language\r\n      };\r\n\r\n      const result = await fetchApi<any>('/generate/gherkin', {\r\n        method: 'POST',\r\n        body: JSON.stringify(apiInput),\r\n      });\r\n\r\n      // Convert API response to our expected output format\r\n      return {\r\n        gherkin: result.gherkin || result.gherkin_scenario || '',\r\n      };\r\n    } catch (error) {\r\n      throw new Error(`Failed to generate Gherkin: ${(error as Error).message}`);\r\n    }\r\n  }\r\n}\r\n\r\n\r\n// Uses the API endpoint from OpenAPI spec for code generation\r\nexport async function callGenerateCode(input: GenerateCodeInput): Promise<GenerateCodeOutput> {\r\n  try {\r\n    const apiInput = {\r\n      framework: input.framework,\r\n      gherkin_scenario: input.gherkin_scenario,\r\n      test_history: input.test_history || {}\r\n    };\r\n\r\n    const result = await fetchApi<any>('/generate/code', {\r\n      method: 'POST',\r\n      body: JSON.stringify(apiInput),\r\n    });\r\n\r\n    return {\r\n      code: result.code || '',\r\n    };\r\n  } catch (error) {\r\n    let errorMessage = `Failed to generate code: ${(error as Error).message}`;\r\n    throw new Error(errorMessage);\r\n  }\r\n}\r\n\r\nexport async function callExecuteSmokeTest(input: ExecuteSmokeTestInput): Promise<ExecuteSmokeTestOutput> {\r\n  const apiInput = {\r\n    instructions: input.instructions,\r\n    url: input.baseUrl,\r\n    user_story: input.userStory,\r\n    config_id: input.configId,\r\n    configuration: input.configuration\r\n  };\r\n\r\n  try {\r\n    // Using the documented smoke test endpoint\r\n    const result = await fetchApi<any>('/tests/smoke', {\r\n      method: 'POST',\r\n      body: JSON.stringify(apiInput),\r\n    });\r\n\r\n    // Convert API response to our expected output format based on TestExecutionHistoryData schema\r\n    const executionData: ExecuteSmokeTestOutput = {\r\n      actions: result.actions || [],\r\n      results: result.results || [],\r\n      elements: result.elements || [],\r\n      urls: result.urls || [],\r\n      errors: result.errors || [],\r\n      screenshots: result.screenshots || [],\r\n      metadata: {\r\n        start_time: result.start_time || new Date().toISOString(),\r\n        end_time: result.end_time || new Date().toISOString(),\r\n        total_steps: result.total_steps || 0,\r\n        success: result.success !== undefined ? result.success : true\r\n      },\r\n      generatedGherkin: result.generated_gherkin || result.generatedGherkin || '',\r\n      userStory: input.userStory\r\n    };\r\n\r\n    return executionData;\r\n  } catch (error) {\r\n    throw new Error(`Failed to execute smoke test: ${(error as Error).message}`);\r\n  }\r\n}\r\n\r\n// Uses the API endpoint from OpenAPI spec for full test execution\r\nexport async function callExecuteFullTest(input: { gherkin: string, url?: string }): Promise<ExecuteSmokeTestOutput> {\r\n  // Converting our input to match the API schema for full tests\r\n  const apiInput = {\r\n    gherkin_scenario: input.gherkin,\r\n    url: input.url\r\n  };\r\n\r\n  try {\r\n    // Using the documented full test endpoint\r\n    const result = await fetchApi<any>('/tests/full', {\r\n      method: 'POST',\r\n      body: JSON.stringify(apiInput),\r\n    });\r\n\r\n    // Convert API response to our expected output format based on TestExecutionHistoryData schema\r\n    const executionData: ExecuteSmokeTestOutput = {\r\n      actions: result.actions || [],\r\n      results: result.results || [],\r\n      elements: result.elements || [],\r\n      urls: result.urls || [],\r\n      errors: result.errors || [],\r\n      screenshots: result.screenshots || [],\r\n      metadata: {\r\n        start_time: result.start_time || new Date().toISOString(),\r\n        end_time: result.end_time || new Date().toISOString(),\r\n        total_steps: result.total_steps || 0,\r\n        success: result.success !== undefined ? result.success : true\r\n      },\r\n      generatedGherkin: input.gherkin // Use the input Gherkin since this was a full test\r\n    };\r\n\r\n    return executionData;\r\n  } catch (error) {\r\n    throw new Error(`Failed to execute full test: ${(error as Error).message}`);\r\n  }\r\n}\r\n\r\n// Function to save test history to a project\r\nexport async function saveTestHistory(input: {\r\n  projectId: string,\r\n  suiteId: string,\r\n  name: string,\r\n  description: string,\r\n  gherkin: string,\r\n  testHistory: any\r\n}): Promise<any> {\r\n  // Convert our input to match the API schema\r\n  const apiInput = {\r\n    project_id: input.projectId,\r\n    suite_id: input.suiteId,\r\n    name: input.name,\r\n    description: input.description,\r\n    gherkin: input.gherkin,\r\n    test_history: input.testHistory\r\n  };\r\n\r\n  try {\r\n    return await fetchApi<any>('/projects/save-history', {\r\n      method: 'POST',\r\n      body: JSON.stringify(apiInput),\r\n    });\r\n  } catch (error) {\r\n    throw new Error(`Failed to save test history: ${(error as Error).message}`);\r\n  }\r\n}\r\n\r\n// Uses the API endpoint for summarizing test results\r\nexport async function callSummarizeTestResults(input: SummarizeTestResultsInput): Promise<SummarizeTestResultsOutput> {\r\n  // Converting our input to match the API schema\r\n  const apiInput = {\r\n    test_results: input.testResults\r\n  };\r\n\r\n  try {\r\n    const result = await fetchApi<any>('/tests/summarize', {\r\n      method: 'POST',\r\n      body: JSON.stringify(apiInput),\r\n    });\r\n\r\n    return {\r\n      summary: result.summary || '',\r\n    };\r\n  } catch (error) {\r\n    throw new Error(`Failed to summarize test results: ${(error as Error).message}`);\r\n  }\r\n}\r\n\r\n// Prompt Management API Functions\r\nexport async function fetchPrompts(): Promise<any> {\r\n  try {\r\n    return await fetchApi<any>('/prompts/');\r\n  } catch (error) {\r\n    throw new Error(`Failed to fetch prompts: ${(error as Error).message}`);\r\n  }\r\n}\r\n\r\nexport async function fetchPromptDetail(category: string, promptId: string): Promise<any> {\r\n  try {\r\n    return await fetchApi<any>(`/prompts/${category}/${promptId}`);\r\n  } catch (error) {\r\n    throw new Error(`Failed to fetch prompt details: ${(error as Error).message}`);\r\n  }\r\n}\r\n\r\nexport async function updatePrompt(\r\n  category: string, \r\n  promptId: string, \r\n  data: {\r\n    content: Record<string, string>;\r\n    metadata?: Record<string, any>;\r\n    commit_message?: string;\r\n  }\r\n): Promise<any> {\r\n  try {\r\n    return await fetchApi<any>(`/prompts/${category}/${promptId}`, {\r\n      method: 'PUT',\r\n      body: JSON.stringify(data),\r\n    });\r\n  } catch (error) {\r\n    throw new Error(`Failed to update prompt: ${(error as Error).message}`);\r\n  }\r\n}\r\n\r\nexport async function createPrompt(data: {\r\n  category: string;\r\n  prompt_id: string;\r\n  name: string;\r\n  description: string;\r\n  languages?: string[];\r\n  content: Record<string, string>;\r\n  metadata?: Record<string, any>;\r\n}): Promise<any> {\r\n  try {\r\n    return await fetchApi<any>('/prompts/', {\r\n      method: 'POST',\r\n      body: JSON.stringify(data),\r\n    });\r\n  } catch (error) {\r\n    throw new Error(`Failed to create prompt: ${(error as Error).message}`);\r\n  }\r\n}\r\n\r\nexport async function deletePrompt(category: string, promptId: string): Promise<any> {\r\n  try {\r\n    return await fetchApi<any>(`/prompts/${category}/${promptId}`, {\r\n      method: 'DELETE',\r\n    });\r\n  } catch (error) {\r\n    throw new Error(`Failed to delete prompt: ${(error as Error).message}`);\r\n  }\r\n}\r\n\r\nexport async function validatePrompt(category: string, promptId: string): Promise<any> {\r\n  try {\r\n    return await fetchApi<any>(`/prompts/${category}/${promptId}/validate`, {\r\n      method: 'POST',\r\n    });\r\n  } catch (error) {\r\n    throw new Error(`Failed to validate prompt: ${(error as Error).message}`);\r\n  }\r\n}\r\n\r\nexport async function validateAllPrompts(): Promise<any> {\r\n  try {\r\n    return await fetchApi<any>('/prompts/validate-all', {\r\n      method: 'POST',\r\n    });\r\n  } catch (error) {\r\n    throw new Error(`Failed to validate all prompts: ${(error as Error).message}`);\r\n  }\r\n}\r\n\r\n// === CONFIGURATION API ENDPOINTS ===\r\n\r\n// Get all configurations (predefined + custom)\r\nexport const getAllConfigurations = (): Promise<{predefined: any[], custom: any[]}> => fetchApi<{predefined: any[], custom: any[]}>('/config/');\r\n\r\n// Get all predefined configurations\r\nexport const getPredefinedConfigurations = (): Promise<any[]> => fetchApi<any[]>('/config/predefined');\r\n\r\n// Get specific predefined configuration\r\nexport const getPredefinedConfiguration = (configType: string): Promise<any> => fetchApi<any>(`/config/predefined/${configType}`);\r\n\r\n// Get all custom configurations\r\nexport const getCustomConfigurations = (): Promise<any[]> => fetchApi<any[]>('/config/custom');\r\n\r\n// Get specific custom configuration\r\nexport const getCustomConfiguration = (configId: string): Promise<any> => fetchApi<any>(`/config/custom/${configId}`);\r\n\r\n// Create custom configuration\r\nexport const createCustomConfiguration = (data: any): Promise<any> => fetchApi<any>('/config/custom', { \r\n  method: 'POST', \r\n  body: JSON.stringify(data) \r\n});\r\n\r\n// Update custom configuration\r\nexport const updateCustomConfiguration = (configId: string, data: any): Promise<any> => fetchApi<any>(`/config/custom/${configId}`, { \r\n  method: 'PUT', \r\n  body: JSON.stringify(data) \r\n});\r\n\r\n// Delete custom configuration\r\nexport const deleteCustomConfiguration = (configId: string): Promise<any> => fetchApi<any>(`/config/custom/${configId}`, { \r\n  method: 'DELETE' \r\n});\r\n\r\n// Validate configuration\r\nexport const validateConfiguration = (data: any): Promise<any> => fetchApi<any>('/config/validate', { \r\n  method: 'POST', \r\n  body: JSON.stringify(data) \r\n});\r\n\r\n// Test configuration\r\nexport const testConfiguration = (data: any): Promise<any> => fetchApi<any>('/config/test', { \r\n  method: 'POST', \r\n  body: JSON.stringify(data) \r\n});\r\n\r\n// Get environment defaults\r\nexport const getEnvironmentDefaults = (): Promise<any> => fetchApi<any>('/config/defaults');\r\n\r\n// ==========================================\r\n// CodeGen API Functions\r\n// ==========================================\r\n\r\n// Start a new codegen session\r\nexport const startCodegenSession = (data: PlaywrightCodegenRequest): Promise<CodegenSessionInfo> => \r\n  fetchApi<CodegenSessionInfo>('/codegen/start', {\r\n    method: 'POST',\r\n    body: JSON.stringify(data)\r\n  });\r\n\r\n// Get session status\r\nexport const getCodegenSession = (sessionId: string): Promise<CodegenSessionInfo> => \r\n  fetchApi<CodegenSessionInfo>(`/codegen/session/${sessionId}`);\r\n\r\n// Stop session\r\nexport const stopCodegenSession = (sessionId: string): Promise<{ message: string; session_id: string }> => \r\n  fetchApi<{ message: string; session_id: string }>(`/codegen/session/${sessionId}/stop`, {\r\n    method: 'POST'\r\n  });\r\n\r\n// Get generated code\r\nexport const getCodegenGeneratedCode = (sessionId: string): Promise<{\r\n  session_id: string;\r\n  status: string;\r\n  generated_code?: string;\r\n  target_language: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}> => fetchApi(`/codegen/session/${sessionId}/code`);\r\n\r\n// Convert to test case\r\nexport const convertCodegenToTestcase = (data: CodegenTestCaseRequest): Promise<Record<string, any>> => \r\n  fetchApi<Record<string, any>>(`/codegen/session/${data.session_id}/convert`, {\r\n    method: 'POST',\r\n    body: JSON.stringify(data)\r\n  });\r\n\r\n// Cleanup session\r\nexport const cleanupCodegenSession = (sessionId: string): Promise<{ message: string; session_id: string }> => \r\n  fetchApi<{ message: string; session_id: string }>(`/codegen/session/${sessionId}`, {\r\n    method: 'DELETE'\r\n  });\r\n\r\n// List active sessions\r\nexport const listCodegenSessions = (): Promise<CodegenSessionListResponse> => \r\n  fetchApi<CodegenSessionListResponse>('/codegen/sessions');\r\n\r\n// Get statistics\r\nexport const getCodegenStats = (): Promise<CodegenStatsResponse> => \r\n  fetchApi<CodegenStatsResponse>('/codegen/stats');\r\n\r\n// Bulk cleanup\r\nexport const bulkCleanupCodegenSessions = (): Promise<{\r\n  message: string;\r\n  sessions_cleaned: number;\r\n  sessions_remaining: number;\r\n}> => fetchApi('/codegen/bulk-cleanup', { method: 'POST' });\r\n\r\n// Health check\r\nexport const getCodegenHealth = (): Promise<CodegenHealthResponse> => \r\n  fetchApi<CodegenHealthResponse>('/codegen/health');\r\n\r\n// History endpoints\r\nexport const getCodegenHistory = (limit?: number): Promise<CodegenHistoryResponse> => \r\n  fetchApi<CodegenHistoryResponse>(`/codegen/history${limit ? `?limit=${limit}` : ''}`);\r\n\r\nexport const getCodegenHistorySession = (sessionId: string): Promise<CodegenHistorySessionDetailResponse> => \r\n  fetchApi<CodegenHistorySessionDetailResponse>(`/codegen/history/${sessionId}`);\r\n\r\n// Execution endpoints\r\nexport const executeCodegenTest = (data: CodegenExecutionRequest): Promise<CodegenExecutionResponse> => \r\n  fetchApi<CodegenExecutionResponse>('/codegen/execute', {\r\n    method: 'POST',\r\n    body: JSON.stringify(data)\r\n  });\r\n\r\nexport const getCodegenExecution = (executionId: string): Promise<CodegenExecutionInfo> => \r\n  fetchApi<CodegenExecutionInfo>(`/codegen/execution/${executionId}`);\r\n\r\nexport const listCodegenExecutions = (): Promise<CodegenExecutionListResponse> => \r\n  fetchApi<CodegenExecutionListResponse>('/codegen/executions');\r\n\r\nexport const stopCodegenExecution = (executionId: string): Promise<{ message: string; execution_id: string }> => \r\n  fetchApi<{ message: string; execution_id: string }>(`/codegen/execution/${executionId}/stop`, {\r\n    method: 'POST'\r\n  });\r\n\r\nexport const cleanupCodegenExecution = (executionId: string): Promise<{ message: string; execution_id: string }> => \r\n  fetchApi<{ message: string; execution_id: string }>(`/codegen/execution/${executionId}`, {\r\n    method: 'DELETE'\r\n  });\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AA+BA,eAAe,SAAY,GAAW,EAAE,OAAqB;IAC3D,MAAM,WAAW,MAAM,MAAM,GAAG,oHAAA,CAAA,eAAY,GAAG,KAAK,EAAE;QACpD,GAAG,OAAO;QACV,SAAS;YACP,gBAAgB;YAChB,8BAA8B;YAC9B,GAAI,SAAS,OAAO;QACtB;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,IAAI;QACJ,IAAI,eAAe,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,UAAU,EAAE,KAAK;QAC3E,IAAI;YACF,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC;YACzC,IAAI,eAAe,YAAY,OAAO,CAAC,wBAAwB,CAAC,GAAG;gBACjE,YAAY,MAAM,SAAS,IAAI;gBAC/B,eAAe,WAAW,SAAS,WAAW,WAAW;YAC3D,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,yCAAyC;gBACzC,IAAI,UAAU,WAAW,GAAG,QAAQ,CAAC,mBAAmB;oBACpD,eAAe,GAAG,aAAa,+CAA+C,EAAE,UAAU,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC;gBACpH,OAAO;oBACH,eAAe,GAAG,aAAa,YAAY,EAAE,UAAU,SAAS,CAAC,GAAG,OAAO,UAAU,MAAM,GAAG,MAAM,QAAQ,IAAI;gBACpH;YACF;QACF,EAAE,OAAO,GAAG;YACV,+FAA+F;YAC/F,eAAe,GAAG,SAAS,UAAU,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,iCAAiC,CAAC;QACtI;QACA,MAAM,IAAI,MAAM;IAClB;IACA,kFAAkF;IAClF,IAAI,SAAS,MAAM,KAAK,KAAK;QAC3B,OAAO,CAAC,GAAQ,gDAAgD;IAClE;IACA,OAAO,SAAS,IAAI;AACtB;AAGO,MAAM,cAAc,IAAqC,SAA+B;AACxF,MAAM,gBAAgB,CAAC,OAA+C,SAAkB,cAAc;QAAE,QAAQ;QAAQ,MAAM,KAAK,SAAS,CAAC;IAAM;AACnJ,MAAM,iBAAiB,CAAC,YAAwC,SAAkB,CAAC,UAAU,EAAE,WAAW;AAC1G,MAAM,gBAAgB,CAAC,WAAmB,OAA+C,SAAkB,CAAC,UAAU,EAAE,WAAW,EAAE;QAAE,QAAQ;QAAO,MAAM,KAAK,SAAS,CAAC;IAAM;AACjL,MAAM,gBAAgB,CAAC,YAAqC,SAAe,CAAC,UAAU,EAAE,WAAW,EAAE;QAAE,QAAQ;IAAS;AAGxH,MAAM,uBAAuB,CAAC,YAAuD,SAAiC,CAAC,UAAU,EAAE,UAAU,OAAO,CAAC;AACrJ,MAAM,cAAc,CAAC,WAAmB,OAAmD,SAAoB,CAAC,UAAU,EAAE,UAAU,QAAQ,CAAC,EAAE;QAAE,QAAQ;QAAQ,MAAM,KAAK,SAAS,CAAC;IAAM;AAC9L,MAAM,eAAe,CAAC,WAAmB,UAAwC,SAAoB,CAAC,UAAU,EAAE,UAAU,QAAQ,EAAE,SAAS;AAC/I,MAAM,cAAc,CAAC,WAAmB,SAAiB,OAAmD,SAAoB,CAAC,UAAU,EAAE,UAAU,QAAQ,EAAE,SAAS,EAAE;QAAE,QAAQ;QAAO,MAAM,KAAK,SAAS,CAAC;IAAM;AACxN,MAAM,cAAc,CAAC,WAAmB,UAAmC,SAAe,CAAC,UAAU,EAAE,UAAU,QAAQ,EAAE,SAAS,EAAE;QAAE,QAAQ;IAAS;AACzJ,MAAM,eAAe,CAAC,WAAmB,UAAqD,SAAiC,CAAC,UAAU,EAAE,UAAU,QAAQ,EAAE,QAAQ,QAAQ,CAAC,EAAE;QAAE,QAAQ;IAAO;AAGpM,MAAM,wBAAwB,CAAC,WAAmB,UAAoD,SAAgC,CAAC,UAAU,EAAE,UAAU,QAAQ,EAAE,QAAQ,MAAM,CAAC;AACtL,MAAM,iBAAiB,CAAC,WAAmB,SAAiB,OAAiD,SAAmB,CAAC,UAAU,EAAE,UAAU,QAAQ,EAAE,QAAQ,OAAO,CAAC,EAAE;QAAE,QAAQ;QAAQ,MAAM,KAAK,SAAS,CAAC;IAAM;AAChO,MAAM,kBAAkB,CAAC,WAAmB,SAAiB,SAAsC,SAAmB,CAAC,UAAU,EAAE,UAAU,QAAQ,EAAE,QAAQ,OAAO,EAAE,QAAQ;AAChL,MAAM,iBAAiB,CAAC,WAAmB,SAAiB,QAAgB,OAAiD,SAAmB,CAAC,UAAU,EAAE,UAAU,QAAQ,EAAE,QAAQ,OAAO,EAAE,QAAQ,EAAE;QAAE,QAAQ;QAAO,MAAM,KAAK,SAAS,CAAC;IAAM;AACxP,MAAM,uBAAuB,CAAC,WAAmB,SAAiB,QAAgB,OAAuD,SAAmB,CAAC,UAAU,EAAE,UAAU,QAAQ,EAAE,QAAQ,OAAO,EAAE,OAAO,OAAO,CAAC,EAAE;QAAE,QAAQ;QAAS,MAAM,KAAK,SAAS,CAAC;IAAM;AAC7Q,MAAM,iBAAiB,CAAC,WAAmB,SAAiB,SAAkC,SAAe,CAAC,UAAU,EAAE,UAAU,QAAQ,EAAE,QAAQ,OAAO,EAAE,QAAQ,EAAE;QAAE,QAAQ;IAAS;AAC5L,MAAM,kBAAkB,CAAC,WAAmB,SAAiB,SAAuD,SAAoC,CAAC,UAAU,EAAE,UAAU,QAAQ,EAAE,QAAQ,OAAO,EAAE,OAAO,QAAQ,CAAC,EAAE;QAAE,QAAQ;IAAO;AAG7O,MAAM,6BAA6B,OAAO,WAAmB,SAAiB;IACnF,mCAAmC;IACnC,MAAM,WAAW,MAAM,gBAAgB,WAAW,SAAS;IAE3D,8CAA8C;IAC9C,MAAM,iBAAwC;QAC5C,cAAc,SAAS,aAAa,IAAI;QACxC,SAAS,SAAS,GAAG,IAAI;QACzB,WAAW,SAAS,mBAAmB,IAAI;IAC7C;IAEA,0CAA0C;IAC1C,OAAO,qBAAqB;AAC9B;AAGO,MAAM,eAAe,IAA0B,SAAoB;AAEnE,MAAM,iCAAiC,OAAO;IACnD,mFAAmF;IACnF,MAAM,WAAW,MAAM,SAAmC,CAAC,SAAS,EAAE,mBAAmB,cAAc;IACvG,OAAO;AACT;AAMO,eAAe,qBAAqB,KAA4B;IACrE,+CAA+C;IAC/C,MAAM,WAAW;QACf,YAAY,MAAM,SAAS;QAC3B,UAAU,MAAM,QAAQ;IAC1B;IAEA,IAAI;QACF,gCAAgC;QAChC,MAAM,SAAS,MAAM,SAAc,oBAAoB;YACrD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,OAAO;YACL,mBAAmB,OAAO,cAAc,IAAI,OAAO,iBAAiB,IAAI;QAC1E;IACF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,AAAC,MAAgB,OAAO,EAAE;IAC7E;AACF;AAEO,eAAe,4BAA4B,KAAmC;IACnF,+CAA+C;IAC/C,MAAM,WAAW;QACf,gBAAgB,MAAM,SAAS;QAC/B,UAAU,MAAM,QAAQ;IAC1B;IAEA,IAAI;QACF,gCAAgC;QAChC,MAAM,SAAS,MAAM,SAAc,kCAAkC;YACnE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB,IAAO,qDAAqD;QAC5D,IAAI,kBAAoC,EAAE;QAE1C,iDAAiD;QACjD,IAAI,UAAU,OAAO,YAAY,EAAE;YACjC,IAAI,MAAM,OAAO,CAAC,OAAO,YAAY,GAAG;gBACtC,yEAAyE;gBACzE,IAAI,OAAO,YAAY,CAAC,MAAM,GAAG,KAAK,OAAO,OAAO,YAAY,CAAC,EAAE,KAAK,UAAU;oBAChF,2DAA2D;oBAC3D,kBAAkB,OAAO,YAAY;gBACvC,OAAO;oBACL,sCAAsC;oBACtC,kBAAkB,OAAO,YAAY;gBACvC;YACF,OAAO,IAAI,OAAO,OAAO,YAAY,KAAK,UAAU;gBAClD,gDAAgD;gBAChD,IAAI;oBACF,sBAAsB;oBACtB,MAAM,SAAS,KAAK,KAAK,CAAC,OAAO,YAAY;oBAC7C,IAAI,MAAM,OAAO,CAAC,SAAS;wBACzB,IAAI,OAAO,MAAM,GAAG,KAAK,OAAO,MAAM,CAAC,EAAE,KAAK,UAAU;4BACtD,qCAAqC;4BACrC,kBAAkB;wBACpB,OAAO;4BACL,mBAAmB;4BACnB,kBAAkB;wBACpB;oBACF,OAAO;wBACL,+CAA+C;wBAC/C,kBAAkB;4BAAC;yBAAO;oBAC5B;gBACF,EAAE,OAAO,GAAG;oBACV,gCAAgC;oBAChC,kBAAkB,OAAO,YAAY,CAAC,KAAK,CAAC,MAAM,MAAM,CAAC,CAAC,OAAiB,KAAK,IAAI,GAAG,MAAM,GAAG;gBAClG;YACF;QACF,OAAO,IAAI,UAAU,MAAM,OAAO,CAAC,OAAO,eAAe,GAAG;YAC1D,yBAAyB;YACzB,kBAAkB,OAAO,eAAe;QAC1C,OAAO,IAAI,UAAU,OAAO,WAAW,UAAU;YAC/C,iCAAiC;YACjC,kBAAkB;gBAAC;aAAO;QAC5B;QAEA,OAAO;YAAE;QAAgB;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM,IAAI,MAAM,CAAC,sCAAsC,EAAE,AAAC,MAAgB,OAAO,EAAE;IACrF;AACF;AAGO,eAAe,oBAAoB,KAA2B;IACnE,gDAAgD;IAChD,IAAI,MAAM,YAAY,EAAE;QACtB,IAAI;YACF,yEAAyE;YACzE,MAAM,WAAW;gBACf,cAAc,MAAM,OAAO,CAAC,MAAM,YAAY,IAC1C,MAAM,YAAY,CAAC,IAAI,CAAC,QACxB,MAAM,YAAY;gBACtB,UAAU,MAAM,QAAQ;YAC1B;YAEA,MAAM,SAAS,MAAM,SAAc,6BAA6B;gBAC9D,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,qDAAqD;YACrD,OAAO;gBACL,SAAS,OAAO,OAAO,IAAI,OAAO,gBAAgB,IAAI;YACxD;QACF,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,AAAC,MAAgB,OAAO,EAAE;QAC3E;IACF,OAEK;QACH,IAAI;YACF,gEAAgE;YAChE,MAAM,WAAW;gBACf,cAAc,MAAM,SAAS,IAAI;gBACjC,KAAK,MAAM,GAAG,IAAI;gBAClB,YAAY,MAAM,SAAS,IAAI;gBAC/B,UAAU,MAAM,QAAQ;YAC1B;YAEA,MAAM,SAAS,MAAM,SAAc,qBAAqB;gBACtD,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,qDAAqD;YACrD,OAAO;gBACL,SAAS,OAAO,OAAO,IAAI,OAAO,gBAAgB,IAAI;YACxD;QACF,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,AAAC,MAAgB,OAAO,EAAE;QAC3E;IACF;AACF;AAIO,eAAe,iBAAiB,KAAwB;IAC7D,IAAI;QACF,MAAM,WAAW;YACf,WAAW,MAAM,SAAS;YAC1B,kBAAkB,MAAM,gBAAgB;YACxC,cAAc,MAAM,YAAY,IAAI,CAAC;QACvC;QAEA,MAAM,SAAS,MAAM,SAAc,kBAAkB;YACnD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,OAAO;YACL,MAAM,OAAO,IAAI,IAAI;QACvB;IACF,EAAE,OAAO,OAAO;QACd,IAAI,eAAe,CAAC,yBAAyB,EAAE,AAAC,MAAgB,OAAO,EAAE;QACzE,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,eAAe,qBAAqB,KAA4B;IACrE,MAAM,WAAW;QACf,cAAc,MAAM,YAAY;QAChC,KAAK,MAAM,OAAO;QAClB,YAAY,MAAM,SAAS;QAC3B,WAAW,MAAM,QAAQ;QACzB,eAAe,MAAM,aAAa;IACpC;IAEA,IAAI;QACF,2CAA2C;QAC3C,MAAM,SAAS,MAAM,SAAc,gBAAgB;YACjD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,8FAA8F;QAC9F,MAAM,gBAAwC;YAC5C,SAAS,OAAO,OAAO,IAAI,EAAE;YAC7B,SAAS,OAAO,OAAO,IAAI,EAAE;YAC7B,UAAU,OAAO,QAAQ,IAAI,EAAE;YAC/B,MAAM,OAAO,IAAI,IAAI,EAAE;YACvB,QAAQ,OAAO,MAAM,IAAI,EAAE;YAC3B,aAAa,OAAO,WAAW,IAAI,EAAE;YACrC,UAAU;gBACR,YAAY,OAAO,UAAU,IAAI,IAAI,OAAO,WAAW;gBACvD,UAAU,OAAO,QAAQ,IAAI,IAAI,OAAO,WAAW;gBACnD,aAAa,OAAO,WAAW,IAAI;gBACnC,SAAS,OAAO,OAAO,KAAK,YAAY,OAAO,OAAO,GAAG;YAC3D;YACA,kBAAkB,OAAO,iBAAiB,IAAI,OAAO,gBAAgB,IAAI;YACzE,WAAW,MAAM,SAAS;QAC5B;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,AAAC,MAAgB,OAAO,EAAE;IAC7E;AACF;AAGO,eAAe,oBAAoB,KAAwC;IAChF,8DAA8D;IAC9D,MAAM,WAAW;QACf,kBAAkB,MAAM,OAAO;QAC/B,KAAK,MAAM,GAAG;IAChB;IAEA,IAAI;QACF,0CAA0C;QAC1C,MAAM,SAAS,MAAM,SAAc,eAAe;YAChD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,8FAA8F;QAC9F,MAAM,gBAAwC;YAC5C,SAAS,OAAO,OAAO,IAAI,EAAE;YAC7B,SAAS,OAAO,OAAO,IAAI,EAAE;YAC7B,UAAU,OAAO,QAAQ,IAAI,EAAE;YAC/B,MAAM,OAAO,IAAI,IAAI,EAAE;YACvB,QAAQ,OAAO,MAAM,IAAI,EAAE;YAC3B,aAAa,OAAO,WAAW,IAAI,EAAE;YACrC,UAAU;gBACR,YAAY,OAAO,UAAU,IAAI,IAAI,OAAO,WAAW;gBACvD,UAAU,OAAO,QAAQ,IAAI,IAAI,OAAO,WAAW;gBACnD,aAAa,OAAO,WAAW,IAAI;gBACnC,SAAS,OAAO,OAAO,KAAK,YAAY,OAAO,OAAO,GAAG;YAC3D;YACA,kBAAkB,MAAM,OAAO,CAAC,mDAAmD;QACrF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,AAAC,MAAgB,OAAO,EAAE;IAC5E;AACF;AAGO,eAAe,gBAAgB,KAOrC;IACC,4CAA4C;IAC5C,MAAM,WAAW;QACf,YAAY,MAAM,SAAS;QAC3B,UAAU,MAAM,OAAO;QACvB,MAAM,MAAM,IAAI;QAChB,aAAa,MAAM,WAAW;QAC9B,SAAS,MAAM,OAAO;QACtB,cAAc,MAAM,WAAW;IACjC;IAEA,IAAI;QACF,OAAO,MAAM,SAAc,0BAA0B;YACnD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,AAAC,MAAgB,OAAO,EAAE;IAC5E;AACF;AAGO,eAAe,yBAAyB,KAAgC;IAC7E,+CAA+C;IAC/C,MAAM,WAAW;QACf,cAAc,MAAM,WAAW;IACjC;IAEA,IAAI;QACF,MAAM,SAAS,MAAM,SAAc,oBAAoB;YACrD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,OAAO;YACL,SAAS,OAAO,OAAO,IAAI;QAC7B;IACF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,AAAC,MAAgB,OAAO,EAAE;IACjF;AACF;AAGO,eAAe;IACpB,IAAI;QACF,OAAO,MAAM,SAAc;IAC7B,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,AAAC,MAAgB,OAAO,EAAE;IACxE;AACF;AAEO,eAAe,kBAAkB,QAAgB,EAAE,QAAgB;IACxE,IAAI;QACF,OAAO,MAAM,SAAc,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,UAAU;IAC/D,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,gCAAgC,EAAE,AAAC,MAAgB,OAAO,EAAE;IAC/E;AACF;AAEO,eAAe,aACpB,QAAgB,EAChB,QAAgB,EAChB,IAIC;IAED,IAAI;QACF,OAAO,MAAM,SAAc,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,UAAU,EAAE;YAC7D,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,AAAC,MAAgB,OAAO,EAAE;IACxE;AACF;AAEO,eAAe,aAAa,IAQlC;IACC,IAAI;QACF,OAAO,MAAM,SAAc,aAAa;YACtC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,AAAC,MAAgB,OAAO,EAAE;IACxE;AACF;AAEO,eAAe,aAAa,QAAgB,EAAE,QAAgB;IACnE,IAAI;QACF,OAAO,MAAM,SAAc,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,UAAU,EAAE;YAC7D,QAAQ;QACV;IACF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,AAAC,MAAgB,OAAO,EAAE;IACxE;AACF;AAEO,eAAe,eAAe,QAAgB,EAAE,QAAgB;IACrE,IAAI;QACF,OAAO,MAAM,SAAc,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,SAAS,SAAS,CAAC,EAAE;YACtE,QAAQ;QACV;IACF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,AAAC,MAAgB,OAAO,EAAE;IAC1E;AACF;AAEO,eAAe;IACpB,IAAI;QACF,OAAO,MAAM,SAAc,yBAAyB;YAClD,QAAQ;QACV;IACF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,gCAAgC,EAAE,AAAC,MAAgB,OAAO,EAAE;IAC/E;AACF;AAKO,MAAM,uBAAuB,IAAmD,SAA6C;AAG7H,MAAM,8BAA8B,IAAsB,SAAgB;AAG1E,MAAM,6BAA6B,CAAC,aAAqC,SAAc,CAAC,mBAAmB,EAAE,YAAY;AAGzH,MAAM,0BAA0B,IAAsB,SAAgB;AAGtE,MAAM,yBAAyB,CAAC,WAAmC,SAAc,CAAC,eAAe,EAAE,UAAU;AAG7G,MAAM,4BAA4B,CAAC,OAA4B,SAAc,kBAAkB;QACpG,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AAGO,MAAM,4BAA4B,CAAC,UAAkB,OAA4B,SAAc,CAAC,eAAe,EAAE,UAAU,EAAE;QAClI,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AAGO,MAAM,4BAA4B,CAAC,WAAmC,SAAc,CAAC,eAAe,EAAE,UAAU,EAAE;QACvH,QAAQ;IACV;AAGO,MAAM,wBAAwB,CAAC,OAA4B,SAAc,oBAAoB;QAClG,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AAGO,MAAM,oBAAoB,CAAC,OAA4B,SAAc,gBAAgB;QAC1F,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AAGO,MAAM,yBAAyB,IAAoB,SAAc;AAOjE,MAAM,sBAAsB,CAAC,OAClC,SAA6B,kBAAkB;QAC7C,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AAGK,MAAM,oBAAoB,CAAC,YAChC,SAA6B,CAAC,iBAAiB,EAAE,WAAW;AAGvD,MAAM,qBAAqB,CAAC,YACjC,SAAkD,CAAC,iBAAiB,EAAE,UAAU,KAAK,CAAC,EAAE;QACtF,QAAQ;IACV;AAGK,MAAM,0BAA0B,CAAC,YAOlC,SAAS,CAAC,iBAAiB,EAAE,UAAU,KAAK,CAAC;AAG5C,MAAM,2BAA2B,CAAC,OACvC,SAA8B,CAAC,iBAAiB,EAAE,KAAK,UAAU,CAAC,QAAQ,CAAC,EAAE;QAC3E,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AAGK,MAAM,wBAAwB,CAAC,YACpC,SAAkD,CAAC,iBAAiB,EAAE,WAAW,EAAE;QACjF,QAAQ;IACV;AAGK,MAAM,sBAAsB,IACjC,SAAqC;AAGhC,MAAM,kBAAkB,IAC7B,SAA+B;AAG1B,MAAM,6BAA6B,IAIpC,SAAS,yBAAyB;QAAE,QAAQ;IAAO;AAGlD,MAAM,mBAAmB,IAC9B,SAAgC;AAG3B,MAAM,oBAAoB,CAAC,QAChC,SAAiC,CAAC,gBAAgB,EAAE,QAAQ,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI;AAE/E,MAAM,2BAA2B,CAAC,YACvC,SAA8C,CAAC,iBAAiB,EAAE,WAAW;AAGxE,MAAM,qBAAqB,CAAC,OACjC,SAAmC,oBAAoB;QACrD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AAEK,MAAM,sBAAsB,CAAC,cAClC,SAA+B,CAAC,mBAAmB,EAAE,aAAa;AAE7D,MAAM,wBAAwB,IACnC,SAAuC;AAElC,MAAM,uBAAuB,CAAC,cACnC,SAAoD,CAAC,mBAAmB,EAAE,YAAY,KAAK,CAAC,EAAE;QAC5F,QAAQ;IACV;AAEK,MAAM,0BAA0B,CAAC,cACtC,SAAoD,CAAC,mBAAmB,EAAE,aAAa,EAAE;QACvF,QAAQ;IACV", "debugId": null}}, {"offset": {"line": 756, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Dialog = DialogPrimitive.Root\r\n\r\nconst DialogTrigger = DialogPrimitive.Trigger\r\n\r\nconst DialogPortal = DialogPrimitive.Portal\r\n\r\nconst DialogClose = DialogPrimitive.Close\r\n\r\nconst DialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\r\n\r\nconst DialogContent = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n))\r\nDialogContent.displayName = DialogPrimitive.Content.displayName\r\n\r\nconst DialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nDialogHeader.displayName = \"DialogHeader\"\r\n\r\nconst DialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nDialogFooter.displayName = \"DialogFooter\"\r\n\r\nconst DialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-lg font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName\r\n\r\nconst DialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName\r\n\r\nexport {\r\n  Dialog,\r\n  DialogPortal,\r\n  DialogOverlay,\r\n  DialogClose,\r\n  DialogTrigger,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogFooter,\r\n  DialogTitle,\r\n  DialogDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,kKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,kKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,kKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 888, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 920, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/components/ui/form.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport {\r\n  Controller,\r\n  FormProvider,\r\n  useFormContext,\r\n  type ControllerProps,\r\n  type FieldPath,\r\n  type FieldValues,\r\n} from \"react-hook-form\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Label } from \"@/components/ui/label\"\r\n\r\nconst Form = FormProvider\r\n\r\ntype FormFieldContextValue<\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\r\n> = {\r\n  name: TName\r\n}\r\n\r\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\r\n  {} as FormFieldContextValue\r\n)\r\n\r\nconst FormField = <\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\r\n>({\r\n  ...props\r\n}: ControllerProps<TFieldValues, TName>) => {\r\n  return (\r\n    <FormFieldContext.Provider value={{ name: props.name }}>\r\n      <Controller {...props} />\r\n    </FormFieldContext.Provider>\r\n  )\r\n}\r\n\r\nconst useFormField = () => {\r\n  const fieldContext = React.useContext(FormFieldContext)\r\n  const itemContext = React.useContext(FormItemContext)\r\n  const { getFieldState, formState } = useFormContext()\r\n\r\n  const fieldState = getFieldState(fieldContext.name, formState)\r\n\r\n  if (!fieldContext) {\r\n    throw new Error(\"useFormField should be used within <FormField>\")\r\n  }\r\n\r\n  const { id } = itemContext\r\n\r\n  return {\r\n    id,\r\n    name: fieldContext.name,\r\n    formItemId: `${id}-form-item`,\r\n    formDescriptionId: `${id}-form-item-description`,\r\n    formMessageId: `${id}-form-item-message`,\r\n    ...fieldState,\r\n  }\r\n}\r\n\r\ntype FormItemContextValue = {\r\n  id: string\r\n}\r\n\r\nconst FormItemContext = React.createContext<FormItemContextValue>(\r\n  {} as FormItemContextValue\r\n)\r\n\r\nconst FormItem = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => {\r\n  const id = React.useId()\r\n\r\n  return (\r\n    <FormItemContext.Provider value={{ id }}>\r\n      <div ref={ref} className={cn(\"space-y-2\", className)} {...props} />\r\n    </FormItemContext.Provider>\r\n  )\r\n})\r\nFormItem.displayName = \"FormItem\"\r\n\r\nconst FormLabel = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root>\r\n>(({ className, ...props }, ref) => {\r\n  const { error, formItemId } = useFormField()\r\n\r\n  return (\r\n    <Label\r\n      ref={ref}\r\n      className={cn(error && \"text-destructive\", className)}\r\n      htmlFor={formItemId}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nFormLabel.displayName = \"FormLabel\"\r\n\r\nconst FormControl = React.forwardRef<\r\n  React.ElementRef<typeof Slot>,\r\n  React.ComponentPropsWithoutRef<typeof Slot>\r\n>(({ ...props }, ref) => {\r\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\r\n\r\n  return (\r\n    <Slot\r\n      ref={ref}\r\n      id={formItemId}\r\n      aria-describedby={\r\n        !error\r\n          ? `${formDescriptionId}`\r\n          : `${formDescriptionId} ${formMessageId}`\r\n      }\r\n      aria-invalid={!!error}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nFormControl.displayName = \"FormControl\"\r\n\r\nconst FormDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => {\r\n  const { formDescriptionId } = useFormField()\r\n\r\n  return (\r\n    <p\r\n      ref={ref}\r\n      id={formDescriptionId}\r\n      className={cn(\"text-sm text-muted-foreground\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nFormDescription.displayName = \"FormDescription\"\r\n\r\nconst FormMessage = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, children, ...props }, ref) => {\r\n  const { error, formMessageId } = useFormField()\r\n  const body = error ? String(error?.message ?? \"\") : children\r\n\r\n  if (!body) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <p\r\n      ref={ref}\r\n      id={formMessageId}\r\n      className={cn(\"text-sm font-medium text-destructive\", className)}\r\n      {...props}\r\n    >\r\n      {body}\r\n    </p>\r\n  )\r\n})\r\nFormMessage.displayName = \"FormMessage\"\r\n\r\nexport {\r\n  useFormField,\r\n  Form,\r\n  FormItem,\r\n  FormLabel,\r\n  FormControl,\r\n  FormDescription,\r\n  FormMessage,\r\n  FormField,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AASA;AACA;AAfA;;;;;;;AAiBA,MAAM,OAAO,8JAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EACzC,CAAC;AAGH,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,8OAAC,8JAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;AAEA,MAAM,eAAe;IACnB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,iBAAc,AAAD;IAElD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;AAMA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EACxC,CAAC;AAGH,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,KAAK,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAErB,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,8OAAC;YAAI,KAAK;YAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAa,GAAG,KAAK;;;;;;;;;;;AAGrE;AACA,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,SAAS,oBAAoB;QAC3C,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;AACA,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,GAAG,OAAO,EAAE;IACf,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,8OAAC,gKAAA,CAAA,OAAI;QACH,KAAK;QACL,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;AACA,YAAY,WAAW,GAAG;AAE1B,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,8OAAC;QACC,KAAK;QACL,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AACA,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACpC,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM;IAEpD,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;kBAER;;;;;;AAGP;AACA,YAAY,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1073, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport {cn} from '@/lib/utils';\r\n\r\nconst Textarea = React.forwardRef<HTMLTextAreaElement, React.ComponentProps<'textarea'>>(\r\n  ({className, ...props}, ref) => {\r\n    return (\r\n      <textarea\r\n        className={cn(\r\n          'flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nTextarea.displayName = 'Textarea';\r\n\r\nexport {Textarea};\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAC,SAAS,EAAE,GAAG,OAAM,EAAE;IACtB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qTACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1101, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Select = SelectPrimitive.Root\r\n\r\nconst SelectGroup = SelectPrimitive.Group\r\n\r\nconst SelectValue = SelectPrimitive.Value\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n))\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n))\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n))\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ className, children, position = \"popper\", ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n        className\r\n      )}\r\n      position={position}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          \"p-1\",\r\n          position === \"popper\" &&\r\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n))\r\nSelectContent.displayName = SelectPrimitive.Content.displayName\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName\r\n\r\nconst SelectItem = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <SelectPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </SelectPrimitive.ItemIndicator>\r\n    </span>\r\n\r\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n  </SelectPrimitive.Item>\r\n))\r\nSelectItem.displayName = SelectPrimitive.Item.displayName\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\r\n\r\nexport {\r\n  Select,\r\n  SelectGroup,\r\n  SelectValue,\r\n  SelectTrigger,\r\n  SelectContent,\r\n  SelectLabel,\r\n  SelectItem,\r\n  SelectSeparator,\r\n  SelectScrollUpButton,\r\n  SelectScrollDownButton,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1293, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/app/codegen/components/NewSessionDialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from 'react';\r\nimport { useForm } from 'react-hook-form';\r\nimport { zodResolver } from '@hookform/resolvers/zod';\r\nimport { z } from 'zod';\r\n\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog';\r\nimport {\r\n  Form,\r\n  FormControl,\r\n  FormDescription,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n} from '@/components/ui/form';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select';\r\nimport { Switch } from '@/components/ui/switch';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\r\nimport { Separator } from '@/components/ui/separator';\r\n\r\nimport type { PlaywrightCodegenRequest } from '@/lib/types';\r\n\r\nconst formSchema = z.object({\r\n  url: z.string().url().optional().or(z.literal('')),\r\n  target_language: z.enum(['javascript', 'typescript', 'python', 'java', 'csharp']),\r\n  device: z.string().optional(),\r\n  viewport_size: z.string().regex(/^\\d+,\\d+$/).optional().or(z.literal('')),\r\n  headless: z.boolean(),\r\n  timezone: z.string().optional(),\r\n  geolocation: z.string().regex(/^-?\\d+\\.?\\d*,-?\\d+\\.?\\d*$/).optional().or(z.literal('')),\r\n  language: z.string().optional(),\r\n  color_scheme: z.enum(['light', 'dark']).optional(),\r\n  load_storage: z.string().optional(),\r\n  save_storage: z.string().optional(),\r\n  project_id: z.string().optional(),\r\n  test_suite: z.string().optional(),\r\n  user_story: z.string().optional(),\r\n});\r\n\r\ntype FormData = z.infer<typeof formSchema>;\r\n\r\ninterface NewSessionDialogProps {\r\n  open: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n  onStartSession: (request: PlaywrightCodegenRequest) => void;\r\n  isStarting: boolean;\r\n}\r\n\r\nexport function NewSessionDialog({\r\n  open,\r\n  onOpenChange,\r\n  onStartSession,\r\n  isStarting\r\n}: NewSessionDialogProps) {\r\n  const [activeTab, setActiveTab] = useState(\"basic\");\r\n\r\n  const form = useForm<FormData>({\r\n    resolver: zodResolver(formSchema),\r\n    defaultValues: {\r\n      target_language: 'javascript',\r\n      headless: true, // Default to headless mode for Playwright Codegen\r\n      url: '',\r\n      viewport_size: '',\r\n      geolocation: '',\r\n      load_storage: '',\r\n      save_storage: '',\r\n      project_id: '',\r\n      test_suite: '',\r\n      user_story: '',\r\n    },\r\n  });\r\n\r\n  const onSubmit = (data: FormData) => {\r\n    const request: PlaywrightCodegenRequest = {\r\n      ...data,\r\n      url: data.url || undefined,\r\n      viewport_size: data.viewport_size || undefined,\r\n      geolocation: data.geolocation || undefined,\r\n      load_storage: data.load_storage || undefined,\r\n      save_storage: data.save_storage || undefined,\r\n      project_id: data.project_id || undefined,\r\n      test_suite: data.test_suite || undefined,\r\n      user_story: data.user_story || undefined,\r\n    };\r\n    onStartSession(request);\r\n  };\r\n\r\n  const commonDevices = [\r\n    { label: 'Desktop Chrome', value: 'Desktop Chrome' },\r\n    { label: 'Desktop Firefox', value: 'Desktop Firefox' },\r\n    { label: 'Desktop Safari', value: 'Desktop Safari' },\r\n    { label: 'iPhone 13', value: 'iPhone 13' },\r\n    { label: 'iPhone 13 Pro', value: 'iPhone 13 Pro' },\r\n    { label: 'iPad Pro', value: 'iPad Pro' },\r\n    { label: 'Galaxy S21', value: 'Galaxy S21' },\r\n    { label: 'Pixel 5', value: 'Pixel 5' },\r\n  ];\r\n\r\n  const timezones = [\r\n    { label: 'America/New_York', value: 'America/New_York' },\r\n    { label: 'America/Los_Angeles', value: 'America/Los_Angeles' },\r\n    { label: 'Europe/London', value: 'Europe/London' },\r\n    { label: 'Europe/Paris', value: 'Europe/Paris' },\r\n    { label: 'Asia/Tokyo', value: 'Asia/Tokyo' },\r\n    { label: 'Asia/Shanghai', value: 'Asia/Shanghai' },\r\n    { label: 'Australia/Sydney', value: 'Australia/Sydney' },\r\n  ];\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={onOpenChange}>\r\n      <DialogContent className=\"max-w-2xl max-h-[90vh] overflow-y-auto\">\r\n        <DialogHeader>\r\n          <DialogTitle>Start Recording Session</DialogTitle>\r\n          <DialogDescription>\r\n            Playwright will run in headless mode and automatically record interactions based on the provided URL. The generated test code will be available after the session completes.\r\n          </DialogDescription>\r\n        </DialogHeader>\r\n\r\n        <Form {...form}>\r\n          <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\r\n            <Tabs value={activeTab} onValueChange={setActiveTab}>\r\n              <TabsList className=\"grid w-full grid-cols-3\">\r\n                <TabsTrigger value=\"basic\">Basic</TabsTrigger>\r\n                <TabsTrigger value=\"browser\">Browser</TabsTrigger>\r\n                <TabsTrigger value=\"integration\">Integration</TabsTrigger>\r\n              </TabsList>\r\n\r\n              <TabsContent value=\"basic\" className=\"space-y-4\">\r\n                <FormField\r\n                  control={form.control}\r\n                  name=\"target_language\"\r\n                  render={({ field }) => (\r\n                    <FormItem>\r\n                      <FormLabel>Target Language</FormLabel>\r\n                      <Select onValueChange={field.onChange} defaultValue={field.value}>\r\n                        <FormControl>\r\n                          <SelectTrigger>\r\n                            <SelectValue placeholder=\"Select target language\" />\r\n                          </SelectTrigger>\r\n                        </FormControl>\r\n                        <SelectContent>\r\n                          <SelectItem value=\"javascript\">JavaScript</SelectItem>\r\n                          <SelectItem value=\"typescript\">TypeScript</SelectItem>\r\n                          <SelectItem value=\"python\">Python</SelectItem>\r\n                          <SelectItem value=\"java\">Java</SelectItem>\r\n                          <SelectItem value=\"csharp\">C#</SelectItem>\r\n                        </SelectContent>\r\n                      </Select>\r\n                      <FormDescription>\r\n                        The programming language for generated test code\r\n                      </FormDescription>\r\n                      <FormMessage />\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n\r\n                <FormField\r\n                  control={form.control}\r\n                  name=\"url\"\r\n                  render={({ field }) => (\r\n                    <FormItem>\r\n                      <FormLabel>Starting URL (Optional)</FormLabel>\r\n                      <FormControl>\r\n                        <Input placeholder=\"https://example.com\" {...field} />\r\n                      </FormControl>\r\n                      <FormDescription>\r\n                        The initial URL to navigate to when starting the recording\r\n                      </FormDescription>\r\n                      <FormMessage />\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n\r\n              </TabsContent>\r\n\r\n              <TabsContent value=\"browser\" className=\"space-y-4\">\r\n                <FormField\r\n                  control={form.control}\r\n                  name=\"device\"\r\n                  render={({ field }) => (\r\n                    <FormItem>\r\n                      <FormLabel>Device Emulation</FormLabel>\r\n                      <Select onValueChange={field.onChange} value={field.value}>\r\n                        <FormControl>\r\n                          <SelectTrigger>\r\n                            <SelectValue placeholder=\"Select device to emulate\" />\r\n                          </SelectTrigger>\r\n                        </FormControl>\r\n                        <SelectContent>\r\n                          {commonDevices.map((device) => (\r\n                            <SelectItem key={device.value} value={device.value}>\r\n                              {device.label}\r\n                            </SelectItem>\r\n                          ))}\r\n                        </SelectContent>\r\n                      </Select>\r\n                      <FormDescription>\r\n                        Emulate a specific device during recording\r\n                      </FormDescription>\r\n                      <FormMessage />\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n\r\n                <FormField\r\n                  control={form.control}\r\n                  name=\"viewport_size\"\r\n                  render={({ field }) => (\r\n                    <FormItem>\r\n                      <FormLabel>Viewport Size</FormLabel>\r\n                      <FormControl>\r\n                        <Input placeholder=\"1920,1080\" {...field} />\r\n                      </FormControl>\r\n                      <FormDescription>\r\n                        Browser viewport size as width,height (e.g., \"1920,1080\")\r\n                      </FormDescription>\r\n                      <FormMessage />\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n\r\n                <FormField\r\n                  control={form.control}\r\n                  name=\"timezone\"\r\n                  render={({ field }) => (\r\n                    <FormItem>\r\n                      <FormLabel>Timezone</FormLabel>\r\n                      <Select onValueChange={field.onChange} value={field.value}>\r\n                        <FormControl>\r\n                          <SelectTrigger>\r\n                            <SelectValue placeholder=\"Select timezone\" />\r\n                          </SelectTrigger>\r\n                        </FormControl>\r\n                        <SelectContent>\r\n                          {timezones.map((timezone) => (\r\n                            <SelectItem key={timezone.value} value={timezone.value}>\r\n                              {timezone.label}\r\n                            </SelectItem>\r\n                          ))}\r\n                        </SelectContent>\r\n                      </Select>\r\n                      <FormDescription>\r\n                        Browser timezone for the recording session\r\n                      </FormDescription>\r\n                      <FormMessage />\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n\r\n                <FormField\r\n                  control={form.control}\r\n                  name=\"geolocation\"\r\n                  render={({ field }) => (\r\n                    <FormItem>\r\n                      <FormLabel>Geolocation</FormLabel>\r\n                      <FormControl>\r\n                        <Input placeholder=\"40.7128,-74.0060\" {...field} />\r\n                      </FormControl>\r\n                      <FormDescription>\r\n                        Browser geolocation as latitude,longitude (e.g., \"40.7128,-74.0060\")\r\n                      </FormDescription>\r\n                      <FormMessage />\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n\r\n                <FormField\r\n                  control={form.control}\r\n                  name=\"language\"\r\n                  render={({ field }) => (\r\n                    <FormItem>\r\n                      <FormLabel>Browser Language</FormLabel>\r\n                      <FormControl>\r\n                        <Input placeholder=\"en-US\" {...field} />\r\n                      </FormControl>\r\n                      <FormDescription>\r\n                        Browser language setting (e.g., \"en-US\", \"es-ES\")\r\n                      </FormDescription>\r\n                      <FormMessage />\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n\r\n                <FormField\r\n                  control={form.control}\r\n                  name=\"color_scheme\"\r\n                  render={({ field }) => (\r\n                    <FormItem>\r\n                      <FormLabel>Color Scheme</FormLabel>\r\n                      <Select onValueChange={field.onChange} value={field.value}>\r\n                        <FormControl>\r\n                          <SelectTrigger>\r\n                            <SelectValue placeholder=\"Select color scheme\" />\r\n                          </SelectTrigger>\r\n                        </FormControl>\r\n                        <SelectContent>\r\n                          <SelectItem value=\"light\">Light</SelectItem>\r\n                          <SelectItem value=\"dark\">Dark</SelectItem>\r\n                        </SelectContent>\r\n                      </Select>\r\n                      <FormDescription>\r\n                        Browser color scheme preference\r\n                      </FormDescription>\r\n                      <FormMessage />\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n              </TabsContent>\r\n\r\n              <TabsContent value=\"integration\" className=\"space-y-4\">\r\n                <FormField\r\n                  control={form.control}\r\n                  name=\"project_id\"\r\n                  render={({ field }) => (\r\n                    <FormItem>\r\n                      <FormLabel>Project ID</FormLabel>\r\n                      <FormControl>\r\n                        <Input placeholder=\"Enter project ID\" {...field} />\r\n                      </FormControl>\r\n                      <FormDescription>\r\n                        QAK project to associate with this recording\r\n                      </FormDescription>\r\n                      <FormMessage />\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n\r\n                <FormField\r\n                  control={form.control}\r\n                  name=\"test_suite\"\r\n                  render={({ field }) => (\r\n                    <FormItem>\r\n                      <FormLabel>Test Suite</FormLabel>\r\n                      <FormControl>\r\n                        <Input placeholder=\"Enter test suite name\" {...field} />\r\n                      </FormControl>\r\n                      <FormDescription>\r\n                        Test suite name for organizing the generated test case\r\n                      </FormDescription>\r\n                      <FormMessage />\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n\r\n                <FormField\r\n                  control={form.control}\r\n                  name=\"user_story\"\r\n                  render={({ field }) => (\r\n                    <FormItem>\r\n                      <FormLabel>User Story</FormLabel>\r\n                      <FormControl>\r\n                        <Textarea \r\n                          placeholder=\"As a user, I want to...\"\r\n                          className=\"min-h-[100px]\"\r\n                          {...field} \r\n                        />\r\n                      </FormControl>\r\n                      <FormDescription>\r\n                        User story or description for this test case\r\n                      </FormDescription>\r\n                      <FormMessage />\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n\r\n                <Separator />\r\n\r\n                <div className=\"space-y-4\">\r\n                  <h4 className=\"text-sm font-medium\">State Management</h4>\r\n                  \r\n                  <FormField\r\n                    control={form.control}\r\n                    name=\"load_storage\"\r\n                    render={({ field }) => (\r\n                      <FormItem>\r\n                        <FormLabel>Load Storage State</FormLabel>\r\n                        <FormControl>\r\n                          <Input placeholder=\"/path/to/storage-state.json\" {...field} />\r\n                        </FormControl>\r\n                        <FormDescription>\r\n                          Load browser storage state from file (cookies, localStorage, etc.)\r\n                        </FormDescription>\r\n                        <FormMessage />\r\n                      </FormItem>\r\n                    )}\r\n                  />\r\n\r\n                  <FormField\r\n                    control={form.control}\r\n                    name=\"save_storage\"\r\n                    render={({ field }) => (\r\n                      <FormItem>\r\n                        <FormLabel>Save Storage State</FormLabel>\r\n                        <FormControl>\r\n                          <Input placeholder=\"/path/to/save-storage.json\" {...field} />\r\n                        </FormControl>\r\n                        <FormDescription>\r\n                          Save browser storage state to file after recording\r\n                        </FormDescription>\r\n                        <FormMessage />\r\n                      </FormItem>\r\n                    )}\r\n                  />\r\n                </div>\r\n              </TabsContent>\r\n            </Tabs>\r\n\r\n            <DialogFooter>\r\n              <Button type=\"button\" variant=\"outline\" onClick={() => onOpenChange(false)}>\r\n                Cancel\r\n              </Button>\r\n              <Button type=\"submit\" disabled={isStarting}>\r\n                {isStarting ? \"Starting Session...\" : \"Start Recording\"}\r\n              </Button>\r\n            </DialogFooter>\r\n          </form>\r\n        </Form>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAEA;AAQA;AASA;AACA;AACA;AACA;AASA;AACA;AArCA;;;;;;;;;;;;;;AAyCA,MAAM,aAAa,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1B,KAAK,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ,GAAG,EAAE,CAAC,iLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAC9C,iBAAiB,iLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAc;QAAc;QAAU;QAAQ;KAAS;IAChF,QAAQ,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC3B,eAAe,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,aAAa,QAAQ,GAAG,EAAE,CAAC,iLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IACrE,UAAU,iLAAA,CAAA,IAAC,CAAC,OAAO;IACnB,UAAU,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,aAAa,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,6BAA6B,QAAQ,GAAG,EAAE,CAAC,iLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IACnF,UAAU,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,cAAc,iLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAS;KAAO,EAAE,QAAQ;IAChD,cAAc,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACjC,cAAc,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACjC,YAAY,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,YAAY,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,YAAY,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AACjC;AAWO,SAAS,iBAAiB,EAC/B,IAAI,EACJ,YAAY,EACZ,cAAc,EACd,UAAU,EACY;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAY;QAC7B,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,iBAAiB;YACjB,UAAU;YACV,KAAK;YACL,eAAe;YACf,aAAa;YACb,cAAc;YACd,cAAc;YACd,YAAY;YACZ,YAAY;YACZ,YAAY;QACd;IACF;IAEA,MAAM,WAAW,CAAC;QAChB,MAAM,UAAoC;YACxC,GAAG,IAAI;YACP,KAAK,KAAK,GAAG,IAAI;YACjB,eAAe,KAAK,aAAa,IAAI;YACrC,aAAa,KAAK,WAAW,IAAI;YACjC,cAAc,KAAK,YAAY,IAAI;YACnC,cAAc,KAAK,YAAY,IAAI;YACnC,YAAY,KAAK,UAAU,IAAI;YAC/B,YAAY,KAAK,UAAU,IAAI;YAC/B,YAAY,KAAK,UAAU,IAAI;QACjC;QACA,eAAe;IACjB;IAEA,MAAM,gBAAgB;QACpB;YAAE,OAAO;YAAkB,OAAO;QAAiB;QACnD;YAAE,OAAO;YAAmB,OAAO;QAAkB;QACrD;YAAE,OAAO;YAAkB,OAAO;QAAiB;QACnD;YAAE,OAAO;YAAa,OAAO;QAAY;QACzC;YAAE,OAAO;YAAiB,OAAO;QAAgB;QACjD;YAAE,OAAO;YAAY,OAAO;QAAW;QACvC;YAAE,OAAO;YAAc,OAAO;QAAa;QAC3C;YAAE,OAAO;YAAW,OAAO;QAAU;KACtC;IAED,MAAM,YAAY;QAChB;YAAE,OAAO;YAAoB,OAAO;QAAmB;QACvD;YAAE,OAAO;YAAuB,OAAO;QAAsB;QAC7D;YAAE,OAAO;YAAiB,OAAO;QAAgB;QACjD;YAAE,OAAO;YAAgB,OAAO;QAAe;QAC/C;YAAE,OAAO;YAAc,OAAO;QAAa;QAC3C;YAAE,OAAO;YAAiB,OAAO;QAAgB;QACjD;YAAE,OAAO;YAAoB,OAAO;QAAmB;KACxD;IAED,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;sCAAC;;;;;;sCACb,8OAAC,kIAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAKrB,8OAAC,gIAAA,CAAA,OAAI;oBAAE,GAAG,IAAI;8BACZ,cAAA,8OAAC;wBAAK,UAAU,KAAK,YAAY,CAAC;wBAAW,WAAU;;0CACrD,8OAAC,gIAAA,CAAA,OAAI;gCAAC,OAAO;gCAAW,eAAe;;kDACrC,8OAAC,gIAAA,CAAA,WAAQ;wCAAC,WAAU;;0DAClB,8OAAC,gIAAA,CAAA,cAAW;gDAAC,OAAM;0DAAQ;;;;;;0DAC3B,8OAAC,gIAAA,CAAA,cAAW;gDAAC,OAAM;0DAAU;;;;;;0DAC7B,8OAAC,gIAAA,CAAA,cAAW;gDAAC,OAAM;0DAAc;;;;;;;;;;;;kDAGnC,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAQ,WAAU;;0DACnC,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0EACP,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,kIAAA,CAAA,SAAM;gEAAC,eAAe,MAAM,QAAQ;gEAAE,cAAc,MAAM,KAAK;;kFAC9D,8OAAC,gIAAA,CAAA,cAAW;kFACV,cAAA,8OAAC,kIAAA,CAAA,gBAAa;sFACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gFAAC,aAAY;;;;;;;;;;;;;;;;kFAG7B,8OAAC,kIAAA,CAAA,gBAAa;;0FACZ,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAa;;;;;;0FAC/B,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAa;;;;;;0FAC/B,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAS;;;;;;0FAC3B,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAO;;;;;;0FACzB,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAS;;;;;;;;;;;;;;;;;;0EAG/B,8OAAC,gIAAA,CAAA,kBAAe;0EAAC;;;;;;0EAGjB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0DAKlB,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0EACP,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;oEAAC,aAAY;oEAAuB,GAAG,KAAK;;;;;;;;;;;0EAEpD,8OAAC,gIAAA,CAAA,kBAAe;0EAAC;;;;;;0EAGjB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;kDAOpB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAU,WAAU;;0DACrC,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0EACP,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,kIAAA,CAAA,SAAM;gEAAC,eAAe,MAAM,QAAQ;gEAAE,OAAO,MAAM,KAAK;;kFACvD,8OAAC,gIAAA,CAAA,cAAW;kFACV,cAAA,8OAAC,kIAAA,CAAA,gBAAa;sFACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gFAAC,aAAY;;;;;;;;;;;;;;;;kFAG7B,8OAAC,kIAAA,CAAA,gBAAa;kFACX,cAAc,GAAG,CAAC,CAAC,uBAClB,8OAAC,kIAAA,CAAA,aAAU;gFAAoB,OAAO,OAAO,KAAK;0FAC/C,OAAO,KAAK;+EADE,OAAO,KAAK;;;;;;;;;;;;;;;;0EAMnC,8OAAC,gIAAA,CAAA,kBAAe;0EAAC;;;;;;0EAGjB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0DAKlB,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0EACP,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;oEAAC,aAAY;oEAAa,GAAG,KAAK;;;;;;;;;;;0EAE1C,8OAAC,gIAAA,CAAA,kBAAe;0EAAC;;;;;;0EAGjB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0DAKlB,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0EACP,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,kIAAA,CAAA,SAAM;gEAAC,eAAe,MAAM,QAAQ;gEAAE,OAAO,MAAM,KAAK;;kFACvD,8OAAC,gIAAA,CAAA,cAAW;kFACV,cAAA,8OAAC,kIAAA,CAAA,gBAAa;sFACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gFAAC,aAAY;;;;;;;;;;;;;;;;kFAG7B,8OAAC,kIAAA,CAAA,gBAAa;kFACX,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC,kIAAA,CAAA,aAAU;gFAAsB,OAAO,SAAS,KAAK;0FACnD,SAAS,KAAK;+EADA,SAAS,KAAK;;;;;;;;;;;;;;;;0EAMrC,8OAAC,gIAAA,CAAA,kBAAe;0EAAC;;;;;;0EAGjB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0DAKlB,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0EACP,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;oEAAC,aAAY;oEAAoB,GAAG,KAAK;;;;;;;;;;;0EAEjD,8OAAC,gIAAA,CAAA,kBAAe;0EAAC;;;;;;0EAGjB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0DAKlB,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0EACP,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;oEAAC,aAAY;oEAAS,GAAG,KAAK;;;;;;;;;;;0EAEtC,8OAAC,gIAAA,CAAA,kBAAe;0EAAC;;;;;;0EAGjB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0DAKlB,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0EACP,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,kIAAA,CAAA,SAAM;gEAAC,eAAe,MAAM,QAAQ;gEAAE,OAAO,MAAM,KAAK;;kFACvD,8OAAC,gIAAA,CAAA,cAAW;kFACV,cAAA,8OAAC,kIAAA,CAAA,gBAAa;sFACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gFAAC,aAAY;;;;;;;;;;;;;;;;kFAG7B,8OAAC,kIAAA,CAAA,gBAAa;;0FACZ,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAQ;;;;;;0FAC1B,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAO;;;;;;;;;;;;;;;;;;0EAG7B,8OAAC,gIAAA,CAAA,kBAAe;0EAAC;;;;;;0EAGjB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;kDAMpB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAc,WAAU;;0DACzC,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0EACP,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;oEAAC,aAAY;oEAAoB,GAAG,KAAK;;;;;;;;;;;0EAEjD,8OAAC,gIAAA,CAAA,kBAAe;0EAAC;;;;;;0EAGjB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0DAKlB,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0EACP,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;oEAAC,aAAY;oEAAyB,GAAG,KAAK;;;;;;;;;;;0EAEtD,8OAAC,gIAAA,CAAA,kBAAe;0EAAC;;;;;;0EAGjB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0DAKlB,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0EACP,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;oEACP,aAAY;oEACZ,WAAU;oEACT,GAAG,KAAK;;;;;;;;;;;0EAGb,8OAAC,gIAAA,CAAA,kBAAe;0EAAC;;;;;;0EAGjB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0DAKlB,8OAAC,qIAAA,CAAA,YAAS;;;;;0DAEV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAsB;;;;;;kEAEpC,8OAAC,gIAAA,CAAA,YAAS;wDACR,SAAS,KAAK,OAAO;wDACrB,MAAK;wDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kFACP,8OAAC,gIAAA,CAAA,YAAS;kFAAC;;;;;;kFACX,8OAAC,gIAAA,CAAA,cAAW;kFACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;4EAAC,aAAY;4EAA+B,GAAG,KAAK;;;;;;;;;;;kFAE5D,8OAAC,gIAAA,CAAA,kBAAe;kFAAC;;;;;;kFAGjB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kEAKlB,8OAAC,gIAAA,CAAA,YAAS;wDACR,SAAS,KAAK,OAAO;wDACrB,MAAK;wDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kFACP,8OAAC,gIAAA,CAAA,YAAS;kFAAC;;;;;;kFACX,8OAAC,gIAAA,CAAA,cAAW;kFACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;4EAAC,aAAY;4EAA8B,GAAG,KAAK;;;;;;;;;;;kFAE3D,8OAAC,gIAAA,CAAA,kBAAe;kFAAC;;;;;;kFAGjB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQxB,8OAAC,kIAAA,CAAA,eAAY;;kDACX,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAQ;wCAAU,SAAS,IAAM,aAAa;kDAAQ;;;;;;kDAG5E,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,UAAU;kDAC7B,aAAa,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtD", "debugId": null}}, {"offset": {"line": 2427, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst ScrollArea = React.forwardRef<\r\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\r\n>(({ className, children, ...props }, ref) => (\r\n  <ScrollAreaPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\"relative overflow-hidden\", className)}\r\n    {...props}\r\n  >\r\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\r\n      {children}\r\n    </ScrollAreaPrimitive.Viewport>\r\n    <ScrollBar />\r\n    <ScrollAreaPrimitive.Corner />\r\n  </ScrollAreaPrimitive.Root>\r\n))\r\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\r\n\r\nconst ScrollBar = React.forwardRef<\r\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\r\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\r\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\r\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\r\n    ref={ref}\r\n    orientation={orientation}\r\n    className={cn(\r\n      \"flex touch-none select-none transition-colors\",\r\n      orientation === \"vertical\" &&\r\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\r\n      orientation === \"horizontal\" &&\r\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\r\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\r\n))\r\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\r\n\r\nexport { ScrollArea, ScrollBar }\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,0KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,8OAAC,0KAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,8OAAC;;;;;0BACD,8OAAC,0KAAA,CAAA,SAA0B;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,0KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,8OAAC,0KAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,sDACF,gBAAgB,gBACd,wDACF;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,0KAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;AAGnD,UAAU,WAAW,GAAG,0KAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2495, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/app/codegen/components/SessionsList.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { Play, Square, CheckCircle, XCircle, Clock, Monitor } from 'lucide-react';\r\n\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { ScrollArea } from '@/components/ui/scroll-area';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\n\r\nimport type { CodegenSessionInfo } from '@/lib/types';\r\n\r\ninterface SessionsListProps {\r\n  sessions: Array<{\r\n    session_id: string;\r\n    status: string;\r\n    target_language: string;\r\n    url?: string;\r\n    created_at: string;\r\n    updated_at: string;\r\n  }>;\r\n  isLoading: boolean;\r\n  selectedSessionId: string | null;\r\n  onSelectSession: (sessionId: string) => void;\r\n  onStopSession: (sessionId: string) => void;\r\n  isStoppingSession: boolean;\r\n}\r\n\r\nconst getStatusIcon = (status: string) => {\r\n  switch (status) {\r\n    case 'starting':\r\n      return <Clock className=\"h-4 w-4\" />;\r\n    case 'running':\r\n      return <Play className=\"h-4 w-4\" />;\r\n    case 'completed':\r\n      return <CheckCircle className=\"h-4 w-4\" />;\r\n    case 'failed':\r\n      return <XCircle className=\"h-4 w-4\" />;\r\n    case 'stopped':\r\n      return <Square className=\"h-4 w-4\" />;\r\n    default:\r\n      return <Monitor className=\"h-4 w-4\" />;\r\n  }\r\n};\r\n\r\nconst getStatusColor = (status: string) => {\r\n  switch (status) {\r\n    case 'starting':\r\n      return 'secondary';\r\n    case 'running':\r\n      return 'default';\r\n    case 'completed':\r\n      return 'success';\r\n    case 'failed':\r\n      return 'destructive';\r\n    case 'stopped':\r\n      return 'outline';\r\n    default:\r\n      return 'secondary';\r\n  }\r\n};\r\n\r\nconst getLanguageColor = (language: string) => {\r\n  switch (language) {\r\n    case 'javascript':\r\n      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';\r\n    case 'typescript':\r\n      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';\r\n    case 'python':\r\n      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';\r\n    case 'java':\r\n      return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';\r\n    case 'csharp':\r\n      return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';\r\n    default:\r\n      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';\r\n  }\r\n};\r\n\r\nconst formatTime = (dateString: string) => {\r\n  return new Date(dateString).toLocaleTimeString('en-US', {\r\n    hour12: false,\r\n    hour: '2-digit',\r\n    minute: '2-digit',\r\n    second: '2-digit'\r\n  });\r\n};\r\n\r\nexport function SessionsList({\r\n  sessions,\r\n  isLoading,\r\n  selectedSessionId,\r\n  onSelectSession,\r\n  onStopSession,\r\n  isStoppingSession\r\n}: SessionsListProps) {\r\n  if (isLoading) {\r\n    return (\r\n      <Card>\r\n        <CardHeader>\r\n          <CardTitle>Active Sessions</CardTitle>\r\n          <CardDescription>Loading sessions...</CardDescription>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"space-y-3\">\r\n            {[1, 2, 3].map((i) => (\r\n              <div key={i} className=\"flex items-center space-x-4\">\r\n                <Skeleton className=\"h-12 w-12 rounded-lg\" />\r\n                <div className=\"space-y-2 flex-1\">\r\n                  <Skeleton className=\"h-4 w-[200px]\" />\r\n                  <Skeleton className=\"h-4 w-[100px]\" />\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle className=\"flex items-center gap-2\">\r\n          <Monitor className=\"h-5 w-5\" />\r\n          Recording Sessions\r\n        </CardTitle>\r\n        <CardDescription>\r\n          {sessions.length} session{sessions.length !== 1 ? 's' : ''} available\r\n        </CardDescription>\r\n      </CardHeader>\r\n      <CardContent>\r\n        <ScrollArea className=\"h-[600px] pr-4\">\r\n          {sessions.length === 0 ? (\r\n            <div className=\"flex items-center justify-center h-32 text-muted-foreground\">\r\n              <div className=\"text-center\">\r\n                <Monitor className=\"h-12 w-12 mx-auto mb-4 opacity-50\" />\r\n                <p>No recording sessions</p>\r\n                <p className=\"text-sm\">Start a new session to begin interactive recording</p>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"space-y-3\">\r\n              {sessions.map((session) => (\r\n                <Card \r\n                  key={session.session_id}\r\n                  className={`cursor-pointer transition-all hover:shadow-md ${\r\n                    selectedSessionId === session.session_id \r\n                      ? 'ring-2 ring-primary bg-muted/50' \r\n                      : ''\r\n                  }`}\r\n                  onClick={() => onSelectSession(session.session_id)}\r\n                >\r\n                  <CardContent className=\"p-4\">\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <div className=\"flex items-center gap-3 flex-1\">\r\n                        <div className=\"flex-shrink-0\">\r\n                          {getStatusIcon(session.status)}\r\n                        </div>\r\n                        <div className=\"flex-1 min-w-0\">\r\n                          <div className=\"flex items-center gap-2 mb-1\">\r\n                            <Badge \r\n                              variant={getStatusColor(session.status) as any}\r\n                              className=\"text-xs\"\r\n                            >\r\n                              {session.status}\r\n                            </Badge>\r\n                            <Badge \r\n                              className={`text-xs ${getLanguageColor(session.target_language)}`}\r\n                              variant=\"outline\"\r\n                            >\r\n                              {session.target_language}\r\n                            </Badge>\r\n                          </div>\r\n                          <p className=\"text-sm font-medium truncate\">\r\n                            Session {session.session_id.slice(0, 8)}...\r\n                          </p>\r\n                          {session.url && (\r\n                            <p className=\"text-xs text-muted-foreground truncate\">\r\n                              {session.url}\r\n                            </p>\r\n                          )}\r\n                          <p className=\"text-xs text-muted-foreground\">\r\n                            Started: {formatTime(session.created_at)}\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n                      \r\n                      {session.status === 'running' && (\r\n                        <Button\r\n                          variant=\"outline\"\r\n                          size=\"sm\"\r\n                          onClick={(e) => {\r\n                            e.stopPropagation();\r\n                            onStopSession(session.session_id);\r\n                          }}\r\n                          disabled={isStoppingSession}\r\n                          className=\"ml-2\"\r\n                        >\r\n                          <Square className=\"h-3 w-3 mr-1\" />\r\n                          Stop\r\n                        </Button>\r\n                      )}\r\n                    </div>\r\n                  </CardContent>\r\n                </Card>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </ScrollArea>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AATA;;;;;;;;AA6BA,MAAM,gBAAgB,CAAC;IACrB,OAAQ;QACN,KAAK;YACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QAC1B,KAAK;YACH,qBAAO,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;QACzB,KAAK;YACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;QAChC,KAAK;YACH,qBAAO,8OAAC,4MAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;QAC5B,KAAK;YACH,qBAAO,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;QAC3B;YACE,qBAAO,8OAAC,wMAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;IAC9B;AACF;AAEA,MAAM,iBAAiB,CAAC;IACtB,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,MAAM,mBAAmB,CAAC;IACxB,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,MAAM,aAAa,CAAC;IAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;QACtD,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;AACF;AAEO,SAAS,aAAa,EAC3B,QAAQ,EACR,SAAS,EACT,iBAAiB,EACjB,eAAe,EACf,aAAa,EACb,iBAAiB,EACC;IAClB,IAAI,WAAW;QACb,qBACE,8OAAC,gIAAA,CAAA,OAAI;;8BACH,8OAAC,gIAAA,CAAA,aAAU;;sCACT,8OAAC,gIAAA,CAAA,YAAS;sCAAC;;;;;;sCACX,8OAAC,gIAAA,CAAA,kBAAe;sCAAC;;;;;;;;;;;;8BAEnB,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAG;4BAAG;yBAAE,CAAC,GAAG,CAAC,CAAC,kBACd,8OAAC;gCAAY,WAAU;;kDACrB,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;+BAJd;;;;;;;;;;;;;;;;;;;;;IAYtB;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;;0BACH,8OAAC,gIAAA,CAAA,aAAU;;kCACT,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,wMAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAGjC,8OAAC,gIAAA,CAAA,kBAAe;;4BACb,SAAS,MAAM;4BAAC;4BAAS,SAAS,MAAM,KAAK,IAAI,MAAM;4BAAG;;;;;;;;;;;;;0BAG/D,8OAAC,gIAAA,CAAA,cAAW;0BACV,cAAA,8OAAC,0IAAA,CAAA,aAAU;oBAAC,WAAU;8BACnB,SAAS,MAAM,KAAK,kBACnB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,8OAAC;8CAAE;;;;;;8CACH,8OAAC;oCAAE,WAAU;8CAAU;;;;;;;;;;;;;;;;6CAI3B,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC,gIAAA,CAAA,OAAI;gCAEH,WAAW,CAAC,8CAA8C,EACxD,sBAAsB,QAAQ,UAAU,GACpC,oCACA,IACJ;gCACF,SAAS,IAAM,gBAAgB,QAAQ,UAAU;0CAEjD,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,cAAc,QAAQ,MAAM;;;;;;kEAE/B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEACJ,SAAS,eAAe,QAAQ,MAAM;wEACtC,WAAU;kFAET,QAAQ,MAAM;;;;;;kFAEjB,8OAAC,iIAAA,CAAA,QAAK;wEACJ,WAAW,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,eAAe,GAAG;wEACjE,SAAQ;kFAEP,QAAQ,eAAe;;;;;;;;;;;;0EAG5B,8OAAC;gEAAE,WAAU;;oEAA+B;oEACjC,QAAQ,UAAU,CAAC,KAAK,CAAC,GAAG;oEAAG;;;;;;;4DAEzC,QAAQ,GAAG,kBACV,8OAAC;gEAAE,WAAU;0EACV,QAAQ,GAAG;;;;;;0EAGhB,8OAAC;gEAAE,WAAU;;oEAAgC;oEACjC,WAAW,QAAQ,UAAU;;;;;;;;;;;;;;;;;;;4CAK5C,QAAQ,MAAM,KAAK,2BAClB,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,cAAc,QAAQ,UAAU;gDAClC;gDACA,UAAU;gDACV,WAAU;;kEAEV,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;+BAtDtC,QAAQ,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;AAoEzC", "debugId": null}}, {"offset": {"line": 2940, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/app/codegen/components/SessionDetails.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from 'react';\r\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\r\nimport { \r\n  Download, \r\n  RefreshCw, \r\n  Code, \r\n  FileText, \r\n  Clock, \r\n  Calendar,\r\n  Monitor,\r\n  Copy,\r\n  Check,\r\n  ExternalLink,\r\n  Play,\r\n  ArrowRight,\r\n  BarChart3,\r\n  CheckCircle2,\r\n  XCircle,\r\n  Info,\r\n  Camera,\r\n  Eye\r\n} from 'lucide-react';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\r\nimport { ScrollArea } from '@/components/ui/scroll-area';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\nimport { useToast } from '@/hooks/use-toast';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Textarea } from '@/components/ui/textarea';\r\n\r\nimport {\r\n  getCodegenSession,\r\n  getCodegenGeneratedCode,\r\n  convertCodegenToTestcase,\r\n  executeCodegenTest,\r\n  getCodegenExecution,\r\n  stopCodegenExecution\r\n} from '@/lib/api';\r\n\r\nimport type { \r\n  CodegenSessionInfo, \r\n  CodegenTestCaseRequest,\r\n  CodegenExecutionInfo \r\n} from '@/lib/types';\r\n\r\ninterface SessionDetailsProps {\r\n  sessionId: string;\r\n  onRefresh: () => void;\r\n}\r\n\r\nexport function SessionDetails({ sessionId, onRefresh }: SessionDetailsProps) {\r\n  const [copiedCode, setCopiedCode] = useState(false);\r\n  const [showConvertDialog, setShowConvertDialog] = useState(false);\r\n  const [showExecuteDialog, setShowExecuteDialog] = useState(false);\r\n  const [activeExecution, setActiveExecution] = useState<string | null>(null);\r\n  const [convertFormData, setConvertFormData] = useState({\r\n    test_name: '',\r\n    test_description: '',\r\n    project_id: '',\r\n    test_suite: '',\r\n    user_story: '',\r\n    framework: 'playwright'\r\n  });\r\n  const { toast } = useToast();\r\n  const queryClient = useQueryClient();\r\n\r\n  const { data: session, isLoading } = useQuery({\r\n    queryKey: ['codegen-session', sessionId],\r\n    queryFn: () => getCodegenSession(sessionId),\r\n    refetchInterval: 5000,\r\n    enabled: !!sessionId,\r\n  });\r\n\r\n  const { data: codeData, isLoading: codeLoading } = useQuery({\r\n    queryKey: ['codegen-code', sessionId],\r\n    queryFn: () => getCodegenGeneratedCode(sessionId),\r\n    enabled: !!sessionId && (session?.status === 'completed' || session?.status === 'stopped'),\r\n  });\r\n\r\n  // Execution query - only when there's an active execution\r\n  const { data: executionData } = useQuery({\r\n    queryKey: ['codegen-execution', activeExecution],\r\n    queryFn: () => getCodegenExecution(activeExecution!),\r\n    enabled: !!activeExecution,\r\n    refetchInterval: 2000, // Refetch more frequently for execution status\r\n  });\r\n\r\n  const convertMutation = useMutation({\r\n    mutationFn: convertCodegenToTestcase,\r\n    onSuccess: (data) => {\r\n      toast({\r\n        title: \"Test Case Created\",\r\n        description: `Successfully converted session to test case: ${data.test_name}`,\r\n      });\r\n      setShowConvertDialog(false);\r\n      queryClient.invalidateQueries({ queryKey: ['projects'] });\r\n    },\r\n    onError: (error) => {\r\n      toast({\r\n        title: \"Conversion Failed\",\r\n        description: error.message,\r\n        variant: \"destructive\",\r\n      });\r\n    },\r\n  });\r\n\r\n  const executeMutation = useMutation({\r\n    mutationFn: executeCodegenTest,\r\n    onSuccess: (data) => {\r\n      toast({\r\n        title: \"Test Execution Started\",\r\n        description: \"Your test is now running with browser-use\",\r\n      });\r\n      setActiveExecution(data.execution_id);\r\n      setShowExecuteDialog(false);\r\n    },\r\n    onError: (error) => {\r\n      toast({\r\n        title: \"Execution Failed\",\r\n        description: error.message,\r\n        variant: \"destructive\",\r\n      });\r\n    },\r\n  });\r\n\r\n  const stopExecutionMutation = useMutation({\r\n    mutationFn: stopCodegenExecution,\r\n    onSuccess: () => {\r\n      toast({\r\n        title: \"Execution Stopped\",\r\n        description: \"Test execution has been stopped\",\r\n      });\r\n      queryClient.invalidateQueries({ queryKey: ['codegen-execution', activeExecution] });\r\n    },\r\n    onError: (error) => {\r\n      toast({\r\n        title: \"Stop Failed\",\r\n        description: error.message,\r\n        variant: \"destructive\",\r\n      });\r\n    },\r\n  });\r\n\r\n  const handleCopyCode = async () => {\r\n    if (codeData?.generated_code) {\r\n      await navigator.clipboard.writeText(codeData.generated_code);\r\n      setCopiedCode(true);\r\n      setTimeout(() => setCopiedCode(false), 2000);\r\n      toast({\r\n        title: \"Code Copied\",\r\n        description: \"Generated code copied to clipboard\",\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleConvert = () => {\r\n    const request: CodegenTestCaseRequest = {\r\n      session_id: sessionId,\r\n      test_name: convertFormData.test_name,\r\n      test_description: convertFormData.test_description,\r\n      project_id: convertFormData.project_id,\r\n      test_suite: convertFormData.test_suite || undefined,\r\n      user_story: convertFormData.user_story || undefined,\r\n      framework: convertFormData.framework,\r\n      include_assertions: true,\r\n      add_error_handling: true,\r\n    };\r\n    convertMutation.mutate(request);\r\n  };\r\n\r\n  const handleExecute = () => {\r\n    executeMutation.mutate({ session_id: sessionId });\r\n  };\r\n\r\n  const handleStopExecution = () => {\r\n    if (activeExecution) {\r\n      stopExecutionMutation.mutate(activeExecution);\r\n    }\r\n  };\r\n\r\n  const formatDateTime = (dateString: string) => {\r\n    return new Date(dateString).toLocaleString('en-US', {\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric',\r\n      hour: '2-digit',\r\n      minute: '2-digit',\r\n      second: '2-digit',\r\n      hour12: false\r\n    });\r\n  };\r\n\r\n  const getStatusColor = (status: string) => {\r\n    switch (status) {\r\n      case 'starting':\r\n        return 'secondary';\r\n      case 'running':\r\n        return 'default';\r\n      case 'completed':\r\n        return 'success';\r\n      case 'failed':\r\n        return 'destructive';\r\n      case 'stopped':\r\n        return 'outline';\r\n      default:\r\n        return 'secondary';\r\n    }\r\n  };\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <Card>\r\n        <CardHeader>\r\n          <Skeleton className=\"h-6 w-48\" />\r\n          <Skeleton className=\"h-4 w-32\" />\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"space-y-4\">\r\n            <Skeleton className=\"h-20 w-full\" />\r\n            <Skeleton className=\"h-40 w-full\" />\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  if (!session) {\r\n    return (\r\n      <Card>\r\n        <CardContent className=\"flex items-center justify-center h-64 text-muted-foreground\">\r\n          Session not found\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <Card>\r\n        <CardHeader>\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <CardTitle className=\"flex items-center gap-2\">\r\n                <Monitor className=\"h-5 w-5\" />\r\n                Session Details\r\n              </CardTitle>\r\n              <CardDescription>\r\n                Session {session.session_id.slice(0, 8)}...\r\n              </CardDescription>\r\n            </div>\r\n            <div className=\"flex items-center gap-2\">\r\n              <Badge variant={getStatusColor(session.status) as any}>\r\n                {session.status}\r\n              </Badge>\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                onClick={onRefresh}\r\n              >\r\n                <RefreshCw className=\"h-4 w-4\" />\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <Tabs defaultValue=\"info\" className=\"space-y-4\">\r\n            <TabsList className=\"grid w-full grid-cols-5\">\r\n              <TabsTrigger value=\"info\">\r\n                <FileText className=\"h-4 w-4 mr-2\" />\r\n                Info\r\n              </TabsTrigger>\r\n              <TabsTrigger value=\"code\" disabled={!codeData?.generated_code}>\r\n                <Code className=\"h-4 w-4 mr-2\" />\r\n                Code\r\n              </TabsTrigger>\r\n              <TabsTrigger value=\"execute\" disabled={!codeData?.generated_code}>\r\n                <Play className=\"h-4 w-4 mr-2\" />\r\n                Execute\r\n              </TabsTrigger>\r\n              <TabsTrigger value=\"results\" disabled={!executionData || executionData.status === 'starting'}>\r\n                <BarChart3 className=\"h-4 w-4 mr-2\" />\r\n                Results\r\n              </TabsTrigger>\r\n              <TabsTrigger value=\"convert\" disabled={session.status !== 'completed' && session.status !== 'stopped'}>\r\n                <ArrowRight className=\"h-4 w-4 mr-2\" />\r\n                Convert\r\n              </TabsTrigger>\r\n            </TabsList>\r\n\r\n            <TabsContent value=\"info\" className=\"space-y-4\">\r\n              <div className=\"grid grid-cols-2 gap-4\">\r\n                <div className=\"space-y-2\">\r\n                  <Label className=\"text-sm font-medium text-muted-foreground\">Status</Label>\r\n                  <Badge variant={getStatusColor(session.status) as any} className=\"w-fit\">\r\n                    {session.status}\r\n                  </Badge>\r\n                </div>\r\n                <div className=\"space-y-2\">\r\n                  <Label className=\"text-sm font-medium text-muted-foreground\">Language</Label>\r\n                  <Badge variant=\"outline\" className=\"w-fit\">\r\n                    {session.target_language}\r\n                  </Badge>\r\n                </div>\r\n              </div>\r\n\r\n              {session.url && (\r\n                <div className=\"space-y-2\">\r\n                  <Label className=\"text-sm font-medium text-muted-foreground\">Target URL</Label>\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Input value={session.url} readOnly className=\"font-mono text-sm\" />\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      onClick={() => window.open(session.url, '_blank')}\r\n                    >\r\n                      <ExternalLink className=\"h-4 w-4\" />\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              <div className=\"grid grid-cols-2 gap-4\">\r\n                <div className=\"space-y-2\">\r\n                  <Label className=\"text-sm font-medium text-muted-foreground flex items-center gap-2\">\r\n                    <Calendar className=\"h-4 w-4\" />\r\n                    Created\r\n                  </Label>\r\n                  <Input \r\n                    value={formatDateTime(session.created_at)} \r\n                    readOnly \r\n                    className=\"text-sm\"\r\n                  />\r\n                </div>\r\n                <div className=\"space-y-2\">\r\n                  <Label className=\"text-sm font-medium text-muted-foreground flex items-center gap-2\">\r\n                    <Clock className=\"h-4 w-4\" />\r\n                    Updated\r\n                  </Label>\r\n                  <Input \r\n                    value={formatDateTime(session.updated_at)} \r\n                    readOnly \r\n                    className=\"text-sm\"\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              {session.completed_at && (\r\n                <div className=\"space-y-2\">\r\n                  <Label className=\"text-sm font-medium text-muted-foreground\">Completed</Label>\r\n                  <Input \r\n                    value={formatDateTime(session.completed_at)} \r\n                    readOnly \r\n                    className=\"text-sm\"\r\n                  />\r\n                </div>\r\n              )}\r\n\r\n              {session.error_message && (\r\n                <div className=\"space-y-2\">\r\n                  <Label className=\"text-sm font-medium text-destructive\">Error</Label>\r\n                  <Textarea \r\n                    value={session.error_message} \r\n                    readOnly \r\n                    className=\"text-sm font-mono text-destructive\"\r\n                    rows={3}\r\n                  />\r\n                </div>\r\n              )}\r\n\r\n              {session.command_used && (\r\n                <div className=\"space-y-2\">\r\n                  <Label className=\"text-sm font-medium text-muted-foreground\">Command</Label>\r\n                  <Input \r\n                    value={session.command_used} \r\n                    readOnly \r\n                    className=\"text-sm font-mono\"\r\n                  />\r\n                </div>\r\n              )}\r\n            </TabsContent>\r\n\r\n            <TabsContent value=\"code\" className=\"space-y-4\">\r\n              {codeLoading ? (\r\n                <Skeleton className=\"h-64 w-full\" />\r\n              ) : codeData?.generated_code ? (\r\n                <div className=\"space-y-4\">\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <Label className=\"text-sm font-medium\">Generated Code</Label>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      onClick={handleCopyCode}\r\n                    >\r\n                      {copiedCode ? (\r\n                        <Check className=\"h-4 w-4 mr-2\" />\r\n                      ) : (\r\n                        <Copy className=\"h-4 w-4 mr-2\" />\r\n                      )}\r\n                      {copiedCode ? 'Copied' : 'Copy'}\r\n                    </Button>\r\n                  </div>\r\n                  <ScrollArea className=\"h-96 w-full rounded border\">\r\n                    <pre className=\"p-4 text-sm font-mono\">\r\n                      <code>{codeData.generated_code}</code>\r\n                    </pre>\r\n                  </ScrollArea>\r\n                </div>\r\n              ) : (\r\n                <div className=\"flex items-center justify-center h-32 text-muted-foreground\">\r\n                  <div className=\"text-center\">\r\n                    <Code className=\"h-12 w-12 mx-auto mb-4 opacity-50\" />\r\n                    <p>No code generated yet</p>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </TabsContent>\r\n\r\n            <TabsContent value=\"execute\" className=\"space-y-4\">\r\n              {!activeExecution ? (\r\n                <div className=\"text-center py-8\">\r\n                  <Play className=\"h-12 w-12 mx-auto mb-4 opacity-50\" />\r\n                  <h3 className=\"text-lg font-medium mb-2\">Execute with Browser-Use</h3>\r\n                  <p className=\"text-sm text-muted-foreground mb-4\">\r\n                    Run your recorded test using browser-use AI agent for automated execution\r\n                  </p>\r\n                  <Button \r\n                    onClick={() => setShowExecuteDialog(true)}\r\n                    disabled={!codeData?.generated_code || executeMutation.isPending}\r\n                  >\r\n                    <Play className=\"h-4 w-4 mr-2\" />\r\n                    {executeMutation.isPending ? 'Starting...' : 'Execute Test'}\r\n                  </Button>\r\n                </div>\r\n              ) : (\r\n                <div className=\"space-y-4\">\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <h3 className=\"text-lg font-medium\">Test Execution</h3>\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <Badge variant={\r\n                        executionData?.status === 'running' ? 'default' :\r\n                        executionData?.status === 'completed' ? 'default' :\r\n                        executionData?.status === 'failed' ? 'destructive' : 'secondary'\r\n                      }>\r\n                        {executionData?.status || 'loading'}\r\n                      </Badge>\r\n                      {executionData?.status === 'running' && (\r\n                        <Button\r\n                          variant=\"outline\"\r\n                          size=\"sm\"\r\n                          onClick={handleStopExecution}\r\n                          disabled={stopExecutionMutation.isPending}\r\n                        >\r\n                          <RefreshCw className=\"h-4 w-4 mr-2\" />\r\n                          Stop\r\n                        </Button>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n\r\n                  {executionData && (\r\n                    <div className=\"grid grid-cols-2 gap-4\">\r\n                      <div className=\"space-y-2\">\r\n                        <Label className=\"text-sm font-medium text-muted-foreground\">Execution ID</Label>\r\n                        <Input \r\n                          value={executionData.execution_id} \r\n                          readOnly \r\n                          className=\"text-sm font-mono\"\r\n                        />\r\n                      </div>\r\n\r\n                      <div className=\"space-y-2\">\r\n                        <Label className=\"text-sm font-medium text-muted-foreground\">Status</Label>\r\n                        <Input \r\n                          value={executionData.status} \r\n                          readOnly \r\n                          className=\"text-sm\"\r\n                        />\r\n                      </div>\r\n\r\n                      <div className=\"space-y-2\">\r\n                        <Label className=\"text-sm font-medium text-muted-foreground\">Created</Label>\r\n                        <Input \r\n                          value={formatDateTime(executionData.created_at)} \r\n                          readOnly \r\n                          className=\"text-sm\"\r\n                        />\r\n                      </div>\r\n\r\n                      <div className=\"space-y-2\">\r\n                        <Label className=\"text-sm font-medium text-muted-foreground\">Updated</Label>\r\n                        <Input \r\n                          value={formatDateTime(executionData.updated_at)} \r\n                          readOnly \r\n                          className=\"text-sm\"\r\n                        />\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  {executionData?.status === 'running' && (\r\n                    <div className=\"space-y-2\">\r\n                      <Label className=\"text-sm font-medium text-muted-foreground\">Target URL</Label>\r\n                      <Input \r\n                        value={executionData.target_url || ''} \r\n                        readOnly \r\n                        className=\"text-sm font-mono\"\r\n                      />\r\n                    </div>\r\n                  )}\r\n\r\n                  {executionData?.status === 'completed' && (\r\n                    <div className=\"space-y-2\">\r\n                      <Label className=\"text-sm font-medium text-muted-foreground\">Result</Label>\r\n                      <Badge variant={executionData.result?.success ? 'default' : 'destructive'} className=\"w-fit\">\r\n                        {executionData.result?.success ? 'Passed' : 'Failed'}\r\n                      </Badge>\r\n                      {executionData.result?.analysis && (\r\n                        <p className=\"text-sm text-muted-foreground\">{executionData.result.analysis}</p>\r\n                      )}\r\n                    </div>\r\n                  )}\r\n\r\n                  {executionData?.status === 'failed' && executionData.error && (\r\n                    <div className=\"space-y-2\">\r\n                      <Label className=\"text-sm font-medium text-muted-foreground\">Error</Label>\r\n                      <div className=\"p-3 bg-destructive/10 border border-destructive/20 rounded-md\">\r\n                        <p className=\"text-sm text-destructive\">{executionData.error}</p>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  <div className=\"flex justify-end gap-2\">\r\n                    <Button \r\n                      variant=\"outline\" \r\n                      onClick={() => setActiveExecution(null)}\r\n                    >\r\n                      New Execution\r\n                    </Button>\r\n                    {executionData?.history && executionData.history.length > 0 && (\r\n                      <Button variant=\"outline\">\r\n                        <Download className=\"h-4 w-4 mr-2\" />\r\n                        Download History\r\n                      </Button>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </TabsContent>\r\n\r\n            <TabsContent value=\"results\" className=\"space-y-4\">\r\n              {!executionData ? (\r\n                <div className=\"text-center py-8\">\r\n                  <BarChart3 className=\"h-12 w-12 mx-auto mb-4 opacity-50\" />\r\n                  <h3 className=\"text-lg font-medium mb-2\">No Results Available</h3>\r\n                  <p className=\"text-sm text-muted-foreground\">\r\n                    Execute your test first to see detailed results\r\n                  </p>\r\n                </div>\r\n              ) : (\r\n                <div className=\"space-y-6\">\r\n                  {/* Overall Summary */}\r\n                  <Card>\r\n                    <CardHeader>\r\n                      <CardTitle className=\"flex items-center gap-2\">\r\n                        <BarChart3 className=\"h-5 w-5\" />\r\n                        Execution Summary\r\n                      </CardTitle>\r\n                    </CardHeader>\r\n                    <CardContent>\r\n                      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n                        <Card className=\"bg-muted/50\">\r\n                          <CardHeader className=\"pb-2\">\r\n                            <CardDescription>Status</CardDescription>\r\n                            <CardTitle className=\"text-xl flex items-center gap-2\">\r\n                              {executionData.status === 'completed' ? (\r\n                                <CheckCircle2 className=\"h-6 w-6 text-green-500\" />\r\n                              ) : executionData.status === 'failed' ? (\r\n                                <XCircle className=\"h-6 w-6 text-red-500\" />\r\n                              ) : (\r\n                                <Info className=\"h-6 w-6 text-blue-500\" />\r\n                              )}\r\n                              {executionData.status}\r\n                            </CardTitle>\r\n                          </CardHeader>\r\n                        </Card>\r\n                        \r\n                        <Card className=\"bg-muted/50\">\r\n                          <CardHeader className=\"pb-2\">\r\n                            <CardDescription>Success Rate</CardDescription>\r\n                            <CardTitle className=\"text-xl\">\r\n                              {executionData.result?.success ? '100%' : '0%'}\r\n                            </CardTitle>\r\n                          </CardHeader>\r\n                        </Card>\r\n\r\n                        <Card className=\"bg-muted/50\">\r\n                          <CardHeader className=\"pb-2\">\r\n                            <CardDescription>Total Steps</CardDescription>\r\n                            <CardTitle className=\"text-xl\">\r\n                              {executionData.result?.total_steps || 0}\r\n                            </CardTitle>\r\n                          </CardHeader>\r\n                        </Card>\r\n                      </div>\r\n                    </CardContent>\r\n                  </Card>\r\n\r\n                  {/* Result Analysis */}\r\n                  {executionData.result && (\r\n                    <Card>\r\n                      <CardHeader>\r\n                        <CardTitle>Analysis</CardTitle>\r\n                      </CardHeader>\r\n                      <CardContent>\r\n                        <div className=\"space-y-4\">\r\n                          <div className=\"p-4 rounded-md border-l-4 border-l-primary bg-muted/20\">\r\n                            <p className=\"text-sm font-medium mb-2\">Result</p>\r\n                            <p className=\"text-sm\">{executionData.result.analysis}</p>\r\n                          </div>\r\n                          \r\n                          {executionData.result.details && (\r\n                            <div className=\"grid grid-cols-1 gap-4\">\r\n                              <div className=\"space-y-2\">\r\n                                <Label className=\"text-sm font-medium\">Execution Summary</Label>\r\n                                <p className=\"text-sm text-muted-foreground\">\r\n                                  {executionData.result.details.execution_summary}\r\n                                </p>\r\n                              </div>\r\n                              \r\n                              {executionData.result.details.history_type && (\r\n                                <div className=\"space-y-2\">\r\n                                  <Label className=\"text-sm font-medium\">History Type</Label>\r\n                                  <Badge variant=\"outline\">\r\n                                    {executionData.result.details.history_type}\r\n                                  </Badge>\r\n                                </div>\r\n                              )}\r\n                            </div>\r\n                          )}\r\n                        </div>\r\n                      </CardContent>\r\n                    </Card>\r\n                  )}\r\n\r\n                  {/* Detailed Steps */}\r\n                  {executionData.result?.steps && executionData.result.steps.length > 0 && (\r\n                    <Card>\r\n                      <CardHeader>\r\n                        <CardTitle className=\"flex items-center gap-2\">\r\n                          <Play className=\"h-5 w-5\" />\r\n                          Execution Steps\r\n                        </CardTitle>\r\n                        <CardDescription>\r\n                          Detailed breakdown of each step executed\r\n                        </CardDescription>\r\n                      </CardHeader>\r\n                      <CardContent>\r\n                        <div className=\"space-y-3\">\r\n                          {executionData.result.steps.map((step, index) => (\r\n                            <div\r\n                              key={index}\r\n                              className=\"flex items-start gap-4 p-4 border rounded-lg hover:bg-muted/50 transition-colors\"\r\n                            >\r\n                              <div className=\"flex-shrink-0\">\r\n                                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${\r\n                                  step.status === 'completed' ? 'bg-green-100 text-green-700' :\r\n                                  step.status === 'error' ? 'bg-red-100 text-red-700' :\r\n                                  'bg-gray-100 text-gray-700'\r\n                                }`}>\r\n                                  {step.status === 'completed' ? (\r\n                                    <CheckCircle2 className=\"h-4 w-4\" />\r\n                                  ) : step.status === 'error' ? (\r\n                                    <XCircle className=\"h-4 w-4\" />\r\n                                  ) : (\r\n                                    step.step_number\r\n                                  )}\r\n                                </div>\r\n                              </div>\r\n                              <div className=\"flex-1 min-w-0\">\r\n                                <div className=\"flex items-center gap-2 mb-1\">\r\n                                  <p className=\"text-sm font-medium\">\r\n                                    Step {step.step_number}: {step.action}\r\n                                  </p>\r\n                                  <Badge variant={step.status === 'completed' ? 'default' : step.status === 'error' ? 'destructive' : 'secondary'}>\r\n                                    {step.status}\r\n                                  </Badge>\r\n                                </div>\r\n                                <p className=\"text-sm text-muted-foreground\">\r\n                                  {step.description}\r\n                                </p>\r\n                                {step.timestamp && (\r\n                                  <p className=\"text-xs text-muted-foreground mt-1\">\r\n                                    {new Date(step.timestamp).toLocaleString()}\r\n                                  </p>\r\n                                )}\r\n                              </div>\r\n                            </div>\r\n                          ))}\r\n                        </div>\r\n                      </CardContent>\r\n                    </Card>\r\n                  )}\r\n\r\n                  {/* Screenshots Gallery */}\r\n                  {executionData.screenshots && executionData.screenshots.length > 0 && (\r\n                    <Card>\r\n                      <CardHeader>\r\n                        <CardTitle className=\"flex items-center gap-2\">\r\n                          <Camera className=\"h-5 w-5\" />\r\n                          Screenshots\r\n                        </CardTitle>\r\n                        <CardDescription>\r\n                          Visual evidence of each step execution\r\n                        </CardDescription>\r\n                      </CardHeader>\r\n                      <CardContent>\r\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n                          {executionData.screenshots.map((screenshot, index) => (\r\n                            <div key={index} className=\"space-y-2\">\r\n                              <div className=\"relative group\">\r\n                                <img\r\n                                  src={screenshot}\r\n                                  alt={`Step ${index + 1} screenshot`}\r\n                                  className=\"w-full h-48 object-cover rounded-lg border cursor-pointer hover:opacity-80 transition-opacity\"\r\n                                  onClick={() => window.open(screenshot, '_blank')}\r\n                                />\r\n                                <div className=\"absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center\">\r\n                                  <Eye className=\"h-6 w-6 text-white\" />\r\n                                </div>\r\n                              </div>\r\n                              <div className=\"text-center\">\r\n                                <p className=\"text-sm font-medium\">Step {index + 1}</p>\r\n                                <p className=\"text-xs text-muted-foreground\">\r\n                                  Click to view full size\r\n                                </p>\r\n                              </div>\r\n                            </div>\r\n                          ))}\r\n                        </div>\r\n                      </CardContent>\r\n                    </Card>\r\n                  )}\r\n\r\n                  {/* Error Details */}\r\n                  {executionData.error && (\r\n                    <Card className=\"border-destructive\">\r\n                      <CardHeader>\r\n                        <CardTitle className=\"text-destructive flex items-center gap-2\">\r\n                          <XCircle className=\"h-5 w-5\" />\r\n                          Error Details\r\n                        </CardTitle>\r\n                      </CardHeader>\r\n                      <CardContent>\r\n                        <div className=\"p-4 bg-destructive/10 border border-destructive/20 rounded-md\">\r\n                          <p className=\"text-sm text-destructive font-mono\">{executionData.error}</p>\r\n                        </div>\r\n                      </CardContent>\r\n                    </Card>\r\n                  )}\r\n\r\n                  {/* Generated Code Display */}\r\n                  {executionData.generated_code && (\r\n                    <Card>\r\n                      <CardHeader>\r\n                        <CardTitle>Executed Code</CardTitle>\r\n                        <CardDescription>\r\n                          The Playwright code that was converted and executed\r\n                        </CardDescription>\r\n                      </CardHeader>\r\n                      <CardContent>\r\n                        <ScrollArea className=\"h-64 w-full rounded border\">\r\n                          <pre className=\"p-4 text-sm font-mono\">\r\n                            <code>{executionData.generated_code}</code>\r\n                          </pre>\r\n                        </ScrollArea>\r\n                      </CardContent>\r\n                    </Card>\r\n                  )}\r\n\r\n                  {/* Browser Configuration */}\r\n                  {executionData.browser_config && Object.keys(executionData.browser_config).length > 0 && (\r\n                    <Card>\r\n                      <CardHeader>\r\n                        <CardTitle>Browser Configuration</CardTitle>\r\n                      </CardHeader>\r\n                      <CardContent>\r\n                        <ScrollArea className=\"h-32 w-full rounded border\">\r\n                          <pre className=\"p-4 text-sm font-mono\">\r\n                            <code>{JSON.stringify(executionData.browser_config, null, 2)}</code>\r\n                          </pre>\r\n                        </ScrollArea>\r\n                      </CardContent>\r\n                    </Card>\r\n                  )}\r\n\r\n                  {/* Execution Timeline */}\r\n                  <Card>\r\n                    <CardHeader>\r\n                      <CardTitle>Timeline</CardTitle>\r\n                    </CardHeader>\r\n                    <CardContent>\r\n                      <div className=\"space-y-4\">\r\n                        <div className=\"flex items-center justify-between p-3 border rounded-md\">\r\n                          <div className=\"flex items-center gap-3\">\r\n                            <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\r\n                            <div>\r\n                              <p className=\"text-sm font-medium\">Execution Started</p>\r\n                              <p className=\"text-xs text-muted-foreground\">\r\n                                {formatDateTime(executionData.created_at)}\r\n                              </p>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                        \r\n                        <div className=\"flex items-center justify-between p-3 border rounded-md\">\r\n                          <div className=\"flex items-center gap-3\">\r\n                            <div className={`w-2 h-2 rounded-full ${\r\n                              executionData.status === 'completed' ? 'bg-green-500' : \r\n                              executionData.status === 'failed' ? 'bg-red-500' : \r\n                              'bg-yellow-500'\r\n                            }`}></div>\r\n                            <div>\r\n                              <p className=\"text-sm font-medium\">\r\n                                Execution {executionData.status === 'completed' ? 'Completed' : \r\n                                         executionData.status === 'failed' ? 'Failed' : 'In Progress'}\r\n                              </p>\r\n                              <p className=\"text-xs text-muted-foreground\">\r\n                                {formatDateTime(executionData.updated_at)}\r\n                              </p>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </CardContent>\r\n                  </Card>\r\n\r\n                  {/* Action Buttons */}\r\n                  <div className=\"flex justify-end gap-2\">\r\n                    <Button \r\n                      variant=\"outline\" \r\n                      onClick={() => setActiveExecution(null)}\r\n                    >\r\n                      New Execution\r\n                    </Button>\r\n                    <Button \r\n                      variant=\"outline\"\r\n                      onClick={() => {\r\n                        const data = JSON.stringify(executionData, null, 2);\r\n                        const blob = new Blob([data], { type: 'application/json' });\r\n                        const url = URL.createObjectURL(blob);\r\n                        const a = document.createElement('a');\r\n                        a.href = url;\r\n                        a.download = `codegen-execution-${executionData.execution_id}.json`;\r\n                        document.body.appendChild(a);\r\n                        a.click();\r\n                        document.body.removeChild(a);\r\n                        URL.revokeObjectURL(url);\r\n                      }}\r\n                    >\r\n                      <Download className=\"h-4 w-4 mr-2\" />\r\n                      Download Results\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </TabsContent>\r\n\r\n            <TabsContent value=\"convert\" className=\"space-y-4\">\r\n              <div className=\"text-center py-8\">\r\n                <ArrowRight className=\"h-12 w-12 mx-auto mb-4 opacity-50\" />\r\n                <h3 className=\"text-lg font-medium mb-2\">Convert to QAK Test Case</h3>\r\n                <p className=\"text-sm text-muted-foreground mb-4\">\r\n                  Transform your recorded interactions into a reusable QAK test case that can be executed with browser-use\r\n                </p>\r\n                <Button \r\n                  onClick={() => setShowConvertDialog(true)}\r\n                  disabled={session.status !== 'completed' && session.status !== 'stopped'}\r\n                >\r\n                  <Play className=\"h-4 w-4 mr-2\" />\r\n                  Convert to Test Case\r\n                </Button>\r\n              </div>\r\n            </TabsContent>\r\n          </Tabs>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Execute Dialog */}\r\n      <Dialog open={showExecuteDialog} onOpenChange={setShowExecuteDialog}>\r\n        <DialogContent className=\"max-w-md\">\r\n          <DialogHeader>\r\n            <DialogTitle>Execute Test with Browser-Use</DialogTitle>\r\n            <DialogDescription>\r\n              Run your recorded test using AI-powered browser automation\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n\r\n          <div className=\"space-y-4\">\r\n            <div className=\"p-4 bg-muted/50 rounded-lg space-y-2\">\r\n              <h4 className=\"font-medium text-sm\">What will happen:</h4>\r\n              <ul className=\"text-sm text-muted-foreground space-y-1\">\r\n                <li>• Your Playwright code will be converted to natural language instructions</li>\r\n                <li>• A browser-use AI agent will execute the test steps</li>\r\n                <li>• You'll see real-time progress and results</li>\r\n              </ul>\r\n            </div>\r\n\r\n            <div className=\"space-y-2\">\r\n              <Label htmlFor=\"execution-headless\">Browser Mode</Label>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <input\r\n                  type=\"checkbox\"\r\n                  id=\"execution-headless\"\r\n                  className=\"rounded\"\r\n                />\r\n                <label htmlFor=\"execution-headless\" className=\"text-sm\">\r\n                  Run in headless mode (hidden browser)\r\n                </label>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <DialogFooter>\r\n            <Button\r\n              variant=\"outline\"\r\n              onClick={() => setShowExecuteDialog(false)}\r\n            >\r\n              Cancel\r\n            </Button>\r\n            <Button\r\n              onClick={handleExecute}\r\n              disabled={executeMutation.isPending}\r\n            >\r\n              {executeMutation.isPending ? 'Starting...' : 'Execute Test'}\r\n            </Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      {/* Convert Dialog */}\r\n      <Dialog open={showConvertDialog} onOpenChange={setShowConvertDialog}>\r\n        <DialogContent className=\"max-w-md\">\r\n          <DialogHeader>\r\n            <DialogTitle>Convert to Test Case</DialogTitle>\r\n            <DialogDescription>\r\n              Create a new test case from this CodeGen session\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n          <div className=\"space-y-4\">\r\n            <div className=\"space-y-2\">\r\n              <Label htmlFor=\"test_name\">Test Name *</Label>\r\n              <Input\r\n                id=\"test_name\"\r\n                value={convertFormData.test_name}\r\n                onChange={(e) => setConvertFormData({ ...convertFormData, test_name: e.target.value })}\r\n                placeholder=\"e.g., User Login Flow\"\r\n              />\r\n            </div>\r\n            <div className=\"space-y-2\">\r\n              <Label htmlFor=\"test_description\">Description</Label>\r\n              <Textarea\r\n                id=\"test_description\"\r\n                value={convertFormData.test_description}\r\n                onChange={(e) => setConvertFormData({ ...convertFormData, test_description: e.target.value })}\r\n                placeholder=\"Describe what this test does...\"\r\n                rows={3}\r\n              />\r\n            </div>\r\n            <div className=\"space-y-2\">\r\n              <Label htmlFor=\"project_id\">Project ID *</Label>\r\n              <Input\r\n                id=\"project_id\"\r\n                value={convertFormData.project_id}\r\n                onChange={(e) => setConvertFormData({ ...convertFormData, project_id: e.target.value })}\r\n                placeholder=\"e.g., my-web-app\"\r\n              />\r\n            </div>\r\n            <div className=\"space-y-2\">\r\n              <Label htmlFor=\"test_suite\">Test Suite</Label>\r\n              <Input\r\n                id=\"test_suite\"\r\n                value={convertFormData.test_suite}\r\n                onChange={(e) => setConvertFormData({ ...convertFormData, test_suite: e.target.value })}\r\n                placeholder=\"e.g., authentication\"\r\n              />\r\n            </div>\r\n          </div>\r\n          <DialogFooter>\r\n            <Button \r\n              variant=\"outline\" \r\n              onClick={() => setShowConvertDialog(false)}\r\n            >\r\n              Cancel\r\n            </Button>\r\n            <Button \r\n              onClick={handleConvert}\r\n              disabled={!convertFormData.test_name || !convertFormData.project_id || convertMutation.isPending}\r\n            >\r\n              {convertMutation.isPending ? 'Converting...' : 'Convert'}\r\n            </Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      {/* Execute Dialog */}\r\n      <Dialog open={showExecuteDialog} onOpenChange={setShowExecuteDialog}>\r\n        <DialogContent className=\"max-w-md\">\r\n          <DialogHeader>\r\n            <DialogTitle>Execute Test</DialogTitle>\r\n            <DialogDescription>\r\n              Run the generated test case in your preferred browser\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n          <div className=\"space-y-4\">\r\n            <div className=\"space-y-2\">\r\n              <Label className=\"text-sm font-medium text-muted-foreground\">Status</Label>\r\n              <Badge variant={getStatusColor(executionData?.status || 'starting') as any} className=\"w-fit\">\r\n                {executionData?.status || 'starting'}\r\n              </Badge>\r\n            </div>\r\n\r\n            {executionData?.status === 'running' && (\r\n              <div className=\"space-y-2\">\r\n                <Label className=\"text-sm font-medium text-muted-foreground\">Target URL</Label>\r\n                <Input \r\n                  value={executionData.target_url || ''} \r\n                  readOnly \r\n                  className=\"text-sm font-mono\"\r\n                />\r\n              </div>\r\n            )}\r\n\r\n            {executionData?.status === 'completed' && (\r\n              <div className=\"space-y-2\">\r\n                <Label className=\"text-sm font-medium text-muted-foreground\">Result</Label>\r\n                <Badge variant={executionData.result?.success ? 'default' : 'destructive'} className=\"w-fit\">\r\n                  {executionData.result?.success ? 'Passed' : 'Failed'}\r\n                </Badge>\r\n                {executionData.result?.analysis && (\r\n                  <p className=\"text-sm text-muted-foreground\">{executionData.result.analysis}</p>\r\n                )}\r\n              </div>\r\n            )}\r\n\r\n            {executionData?.status === 'failed' && executionData.error && (\r\n              <div className=\"space-y-2\">\r\n                <Label className=\"text-sm font-medium text-muted-foreground\">Error</Label>\r\n                <div className=\"p-3 bg-destructive/10 border border-destructive/20 rounded-md\">\r\n                  <p className=\"text-sm text-destructive\">{executionData.error}</p>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            <div className=\"flex justify-end gap-2\">\r\n              {activeExecution && executionData?.status !== 'completed' ? (\r\n                <Button\r\n                  onClick={handleStopExecution}\r\n                  disabled={stopExecutionMutation.isPending}\r\n                >\r\n                  {stopExecutionMutation.isPending ? 'Stopping...' : 'Stop Execution'}\r\n                </Button>\r\n              ) : (\r\n                <Button \r\n                  onClick={handleExecute}\r\n                  disabled={executeMutation.isPending}\r\n                >\r\n                  {executeMutation.isPending ? 'Executing...' : 'Run Test'}\r\n                </Button>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AACA;AACA;AAEA;AA5CA;;;;;;;;;;;;;;;;;AAgEO,SAAS,eAAe,EAAE,SAAS,EAAE,SAAS,EAAuB;IAC1E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrD,WAAW;QACX,kBAAkB;QAClB,YAAY;QACZ,YAAY;QACZ,YAAY;QACZ,WAAW;IACb;IACA,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,EAAE,MAAM,OAAO,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QAC5C,UAAU;YAAC;YAAmB;SAAU;QACxC,SAAS,IAAM,CAAA,GAAA,iHAAA,CAAA,oBAAiB,AAAD,EAAE;QACjC,iBAAiB;QACjB,SAAS,CAAC,CAAC;IACb;IAEA,MAAM,EAAE,MAAM,QAAQ,EAAE,WAAW,WAAW,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QAC1D,UAAU;YAAC;YAAgB;SAAU;QACrC,SAAS,IAAM,CAAA,GAAA,iHAAA,CAAA,0BAAuB,AAAD,EAAE;QACvC,SAAS,CAAC,CAAC,aAAa,CAAC,SAAS,WAAW,eAAe,SAAS,WAAW,SAAS;IAC3F;IAEA,0DAA0D;IAC1D,MAAM,EAAE,MAAM,aAAa,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,UAAU;YAAC;YAAqB;SAAgB;QAChD,SAAS,IAAM,CAAA,GAAA,iHAAA,CAAA,sBAAmB,AAAD,EAAE;QACnC,SAAS,CAAC,CAAC;QACX,iBAAiB;IACnB;IAEA,MAAM,kBAAkB,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QAClC,YAAY,iHAAA,CAAA,2BAAwB;QACpC,WAAW,CAAC;YACV,MAAM;gBACJ,OAAO;gBACP,aAAa,CAAC,6CAA6C,EAAE,KAAK,SAAS,EAAE;YAC/E;YACA,qBAAqB;YACrB,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;QACzD;QACA,SAAS,CAAC;YACR,MAAM;gBACJ,OAAO;gBACP,aAAa,MAAM,OAAO;gBAC1B,SAAS;YACX;QACF;IACF;IAEA,MAAM,kBAAkB,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QAClC,YAAY,iHAAA,CAAA,qBAAkB;QAC9B,WAAW,CAAC;YACV,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YACA,mBAAmB,KAAK,YAAY;YACpC,qBAAqB;QACvB;QACA,SAAS,CAAC;YACR,MAAM;gBACJ,OAAO;gBACP,aAAa,MAAM,OAAO;gBAC1B,SAAS;YACX;QACF;IACF;IAEA,MAAM,wBAAwB,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACxC,YAAY,iHAAA,CAAA,uBAAoB;QAChC,WAAW;YACT,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YACA,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAqB;iBAAgB;YAAC;QACnF;QACA,SAAS,CAAC;YACR,MAAM;gBACJ,OAAO;gBACP,aAAa,MAAM,OAAO;gBAC1B,SAAS;YACX;QACF;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,UAAU,gBAAgB;YAC5B,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC,SAAS,cAAc;YAC3D,cAAc;YACd,WAAW,IAAM,cAAc,QAAQ;YACvC,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,MAAM,UAAkC;YACtC,YAAY;YACZ,WAAW,gBAAgB,SAAS;YACpC,kBAAkB,gBAAgB,gBAAgB;YAClD,YAAY,gBAAgB,UAAU;YACtC,YAAY,gBAAgB,UAAU,IAAI;YAC1C,YAAY,gBAAgB,UAAU,IAAI;YAC1C,WAAW,gBAAgB,SAAS;YACpC,oBAAoB;YACpB,oBAAoB;QACtB;QACA,gBAAgB,MAAM,CAAC;IACzB;IAEA,MAAM,gBAAgB;QACpB,gBAAgB,MAAM,CAAC;YAAE,YAAY;QAAU;IACjD;IAEA,MAAM,sBAAsB;QAC1B,IAAI,iBAAiB;YACnB,sBAAsB,MAAM,CAAC;QAC/B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,cAAc,CAAC,SAAS;YAClD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC,gIAAA,CAAA,OAAI;;8BACH,8OAAC,gIAAA,CAAA,aAAU;;sCACT,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;8BAEtB,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC,oIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAK9B;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC,gIAAA,CAAA,OAAI;sBACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BAA8D;;;;;;;;;;;IAK3F;IAEA,qBACE;;0BACE,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,wMAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAGjC,8OAAC,gIAAA,CAAA,kBAAe;;gDAAC;gDACN,QAAQ,UAAU,CAAC,KAAK,CAAC,GAAG;gDAAG;;;;;;;;;;;;;8CAG5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAS,eAAe,QAAQ,MAAM;sDAC1C,QAAQ,MAAM;;;;;;sDAEjB,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;sDAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAK7B,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,cAAa;4BAAO,WAAU;;8CAClC,8OAAC,gIAAA,CAAA,WAAQ;oCAAC,WAAU;;sDAClB,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;;8DACjB,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAO,UAAU,CAAC,UAAU;;8DAC7C,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAU,UAAU,CAAC,UAAU;;8DAChD,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAU,UAAU,CAAC,iBAAiB,cAAc,MAAM,KAAK;;8DAChF,8OAAC,kNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGxC,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAU,UAAU,QAAQ,MAAM,KAAK,eAAe,QAAQ,MAAM,KAAK;;8DAC1F,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;8CAK3C,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAO,WAAU;;sDAClC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,WAAU;sEAA4C;;;;;;sEAC7D,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAS,eAAe,QAAQ,MAAM;4DAAU,WAAU;sEAC9D,QAAQ,MAAM;;;;;;;;;;;;8DAGnB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,WAAU;sEAA4C;;;;;;sEAC7D,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAChC,QAAQ,eAAe;;;;;;;;;;;;;;;;;;wCAK7B,QAAQ,GAAG,kBACV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,WAAU;8DAA4C;;;;;;8DAC7D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,OAAO,QAAQ,GAAG;4DAAE,QAAQ;4DAAC,WAAU;;;;;;sEAC9C,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IAAM,OAAO,IAAI,CAAC,QAAQ,GAAG,EAAE;sEAExC,cAAA,8OAAC,sNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAMhC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,WAAU;;8EACf,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAY;;;;;;;sEAGlC,8OAAC,iIAAA,CAAA,QAAK;4DACJ,OAAO,eAAe,QAAQ,UAAU;4DACxC,QAAQ;4DACR,WAAU;;;;;;;;;;;;8DAGd,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,WAAU;;8EACf,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAAY;;;;;;;sEAG/B,8OAAC,iIAAA,CAAA,QAAK;4DACJ,OAAO,eAAe,QAAQ,UAAU;4DACxC,QAAQ;4DACR,WAAU;;;;;;;;;;;;;;;;;;wCAKf,QAAQ,YAAY,kBACnB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,WAAU;8DAA4C;;;;;;8DAC7D,8OAAC,iIAAA,CAAA,QAAK;oDACJ,OAAO,eAAe,QAAQ,YAAY;oDAC1C,QAAQ;oDACR,WAAU;;;;;;;;;;;;wCAKf,QAAQ,aAAa,kBACpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,WAAU;8DAAuC;;;;;;8DACxD,8OAAC,oIAAA,CAAA,WAAQ;oDACP,OAAO,QAAQ,aAAa;oDAC5B,QAAQ;oDACR,WAAU;oDACV,MAAM;;;;;;;;;;;;wCAKX,QAAQ,YAAY,kBACnB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,WAAU;8DAA4C;;;;;;8DAC7D,8OAAC,iIAAA,CAAA,QAAK;oDACJ,OAAO,QAAQ,YAAY;oDAC3B,QAAQ;oDACR,WAAU;;;;;;;;;;;;;;;;;;8CAMlB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAO,WAAU;8CACjC,4BACC,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;+CAClB,UAAU,+BACZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAsB;;;;;;kEACvC,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;;4DAER,2BACC,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;qFAEjB,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAEjB,aAAa,WAAW;;;;;;;;;;;;;0DAG7B,8OAAC,0IAAA,CAAA,aAAU;gDAAC,WAAU;0DACpB,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;kEAAM,SAAS,cAAc;;;;;;;;;;;;;;;;;;;;;6DAKpC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;8CAMX,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAU,WAAU;8CACpC,CAAC,gCACA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAG,WAAU;0DAA2B;;;;;;0DACzC,8OAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAGlD,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,IAAM,qBAAqB;gDACpC,UAAU,CAAC,UAAU,kBAAkB,gBAAgB,SAAS;;kEAEhE,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDACf,gBAAgB,SAAS,GAAG,gBAAgB;;;;;;;;;;;;6DAIjD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAsB;;;;;;kEACpC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SACL,eAAe,WAAW,YAAY,YACtC,eAAe,WAAW,cAAc,YACxC,eAAe,WAAW,WAAW,gBAAgB;0EAEpD,eAAe,UAAU;;;;;;4DAE3B,eAAe,WAAW,2BACzB,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS;gEACT,UAAU,sBAAsB,SAAS;;kFAEzC,8OAAC,gNAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;4CAO7C,+BACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,WAAU;0EAA4C;;;;;;0EAC7D,8OAAC,iIAAA,CAAA,QAAK;gEACJ,OAAO,cAAc,YAAY;gEACjC,QAAQ;gEACR,WAAU;;;;;;;;;;;;kEAId,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,WAAU;0EAA4C;;;;;;0EAC7D,8OAAC,iIAAA,CAAA,QAAK;gEACJ,OAAO,cAAc,MAAM;gEAC3B,QAAQ;gEACR,WAAU;;;;;;;;;;;;kEAId,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,WAAU;0EAA4C;;;;;;0EAC7D,8OAAC,iIAAA,CAAA,QAAK;gEACJ,OAAO,eAAe,cAAc,UAAU;gEAC9C,QAAQ;gEACR,WAAU;;;;;;;;;;;;kEAId,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,WAAU;0EAA4C;;;;;;0EAC7D,8OAAC,iIAAA,CAAA,QAAK;gEACJ,OAAO,eAAe,cAAc,UAAU;gEAC9C,QAAQ;gEACR,WAAU;;;;;;;;;;;;;;;;;;4CAMjB,eAAe,WAAW,2BACzB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,WAAU;kEAA4C;;;;;;kEAC7D,8OAAC,iIAAA,CAAA,QAAK;wDACJ,OAAO,cAAc,UAAU,IAAI;wDACnC,QAAQ;wDACR,WAAU;;;;;;;;;;;;4CAKf,eAAe,WAAW,6BACzB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,WAAU;kEAA4C;;;;;;kEAC7D,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAS,cAAc,MAAM,EAAE,UAAU,YAAY;wDAAe,WAAU;kEAClF,cAAc,MAAM,EAAE,UAAU,WAAW;;;;;;oDAE7C,cAAc,MAAM,EAAE,0BACrB,8OAAC;wDAAE,WAAU;kEAAiC,cAAc,MAAM,CAAC,QAAQ;;;;;;;;;;;;4CAKhF,eAAe,WAAW,YAAY,cAAc,KAAK,kBACxD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,WAAU;kEAA4C;;;;;;kEAC7D,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAE,WAAU;sEAA4B,cAAc,KAAK;;;;;;;;;;;;;;;;;0DAKlE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,SAAS,IAAM,mBAAmB;kEACnC;;;;;;oDAGA,eAAe,WAAW,cAAc,OAAO,CAAC,MAAM,GAAG,mBACxD,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;;0EACd,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;8CASjD,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAU,WAAU;8CACpC,CAAC,8BACA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,8OAAC;gDAAG,WAAU;0DAA2B;;;;;;0DACzC,8OAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;6DAK/C,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC,gIAAA,CAAA,OAAI;;kEACH,8OAAC,gIAAA,CAAA,aAAU;kEACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;;8EACnB,8OAAC,kNAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;gEAAY;;;;;;;;;;;;kEAIrC,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,gIAAA,CAAA,OAAI;oEAAC,WAAU;8EACd,cAAA,8OAAC,gIAAA,CAAA,aAAU;wEAAC,WAAU;;0FACpB,8OAAC,gIAAA,CAAA,kBAAe;0FAAC;;;;;;0FACjB,8OAAC,gIAAA,CAAA,YAAS;gFAAC,WAAU;;oFAClB,cAAc,MAAM,KAAK,4BACxB,8OAAC,qNAAA,CAAA,eAAY;wFAAC,WAAU;;;;;+FACtB,cAAc,MAAM,KAAK,yBAC3B,8OAAC,4MAAA,CAAA,UAAO;wFAAC,WAAU;;;;;6GAEnB,8OAAC,kMAAA,CAAA,OAAI;wFAAC,WAAU;;;;;;oFAEjB,cAAc,MAAM;;;;;;;;;;;;;;;;;;8EAK3B,8OAAC,gIAAA,CAAA,OAAI;oEAAC,WAAU;8EACd,cAAA,8OAAC,gIAAA,CAAA,aAAU;wEAAC,WAAU;;0FACpB,8OAAC,gIAAA,CAAA,kBAAe;0FAAC;;;;;;0FACjB,8OAAC,gIAAA,CAAA,YAAS;gFAAC,WAAU;0FAClB,cAAc,MAAM,EAAE,UAAU,SAAS;;;;;;;;;;;;;;;;;8EAKhD,8OAAC,gIAAA,CAAA,OAAI;oEAAC,WAAU;8EACd,cAAA,8OAAC,gIAAA,CAAA,aAAU;wEAAC,WAAU;;0FACpB,8OAAC,gIAAA,CAAA,kBAAe;0FAAC;;;;;;0FACjB,8OAAC,gIAAA,CAAA,YAAS;gFAAC,WAAU;0FAClB,cAAc,MAAM,EAAE,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4CASjD,cAAc,MAAM,kBACnB,8OAAC,gIAAA,CAAA,OAAI;;kEACH,8OAAC,gIAAA,CAAA,aAAU;kEACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sEAAC;;;;;;;;;;;kEAEb,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;sFAA2B;;;;;;sFACxC,8OAAC;4EAAE,WAAU;sFAAW,cAAc,MAAM,CAAC,QAAQ;;;;;;;;;;;;gEAGtD,cAAc,MAAM,CAAC,OAAO,kBAC3B,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,iIAAA,CAAA,QAAK;oFAAC,WAAU;8FAAsB;;;;;;8FACvC,8OAAC;oFAAE,WAAU;8FACV,cAAc,MAAM,CAAC,OAAO,CAAC,iBAAiB;;;;;;;;;;;;wEAIlD,cAAc,MAAM,CAAC,OAAO,CAAC,YAAY,kBACxC,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,iIAAA,CAAA,QAAK;oFAAC,WAAU;8FAAsB;;;;;;8FACvC,8OAAC,iIAAA,CAAA,QAAK;oFAAC,SAAQ;8FACZ,cAAc,MAAM,CAAC,OAAO,CAAC,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4CAY3D,cAAc,MAAM,EAAE,SAAS,cAAc,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,mBAClE,8OAAC,gIAAA,CAAA,OAAI;;kEACH,8OAAC,gIAAA,CAAA,aAAU;;0EACT,8OAAC,gIAAA,CAAA,YAAS;gEAAC,WAAU;;kFACnB,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAY;;;;;;;0EAG9B,8OAAC,gIAAA,CAAA,kBAAe;0EAAC;;;;;;;;;;;;kEAInB,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC;4DAAI,WAAU;sEACZ,cAAc,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrC,8OAAC;oEAEC,WAAU;;sFAEV,8OAAC;4EAAI,WAAU;sFACb,cAAA,8OAAC;gFAAI,WAAW,CAAC,0EAA0E,EACzF,KAAK,MAAM,KAAK,cAAc,gCAC9B,KAAK,MAAM,KAAK,UAAU,4BAC1B,6BACA;0FACC,KAAK,MAAM,KAAK,4BACf,8OAAC,qNAAA,CAAA,eAAY;oFAAC,WAAU;;;;;2FACtB,KAAK,MAAM,KAAK,wBAClB,8OAAC,4MAAA,CAAA,UAAO;oFAAC,WAAU;;;;;2FAEnB,KAAK,WAAW;;;;;;;;;;;sFAItB,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAE,WAAU;;gGAAsB;gGAC3B,KAAK,WAAW;gGAAC;gGAAG,KAAK,MAAM;;;;;;;sGAEvC,8OAAC,iIAAA,CAAA,QAAK;4FAAC,SAAS,KAAK,MAAM,KAAK,cAAc,YAAY,KAAK,MAAM,KAAK,UAAU,gBAAgB;sGACjG,KAAK,MAAM;;;;;;;;;;;;8FAGhB,8OAAC;oFAAE,WAAU;8FACV,KAAK,WAAW;;;;;;gFAElB,KAAK,SAAS,kBACb,8OAAC;oFAAE,WAAU;8FACV,IAAI,KAAK,KAAK,SAAS,EAAE,cAAc;;;;;;;;;;;;;mEAhCzC;;;;;;;;;;;;;;;;;;;;;4CA4ChB,cAAc,WAAW,IAAI,cAAc,WAAW,CAAC,MAAM,GAAG,mBAC/D,8OAAC,gIAAA,CAAA,OAAI;;kEACH,8OAAC,gIAAA,CAAA,aAAU;;0EACT,8OAAC,gIAAA,CAAA,YAAS;gEAAC,WAAU;;kFACnB,8OAAC,sMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAY;;;;;;;0EAGhC,8OAAC,gIAAA,CAAA,kBAAe;0EAAC;;;;;;;;;;;;kEAInB,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC;4DAAI,WAAU;sEACZ,cAAc,WAAW,CAAC,GAAG,CAAC,CAAC,YAAY,sBAC1C,8OAAC;oEAAgB,WAAU;;sFACzB,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFACC,KAAK;oFACL,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,WAAW,CAAC;oFACnC,WAAU;oFACV,SAAS,IAAM,OAAO,IAAI,CAAC,YAAY;;;;;;8FAEzC,8OAAC;oFAAI,WAAU;8FACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;wFAAC,WAAU;;;;;;;;;;;;;;;;;sFAGnB,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAE,WAAU;;wFAAsB;wFAAM,QAAQ;;;;;;;8FACjD,8OAAC;oFAAE,WAAU;8FAAgC;;;;;;;;;;;;;mEAdvC;;;;;;;;;;;;;;;;;;;;;4CA0BnB,cAAc,KAAK,kBAClB,8OAAC,gIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,8OAAC,gIAAA,CAAA,aAAU;kEACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;;8EACnB,8OAAC,4MAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;gEAAY;;;;;;;;;;;;kEAInC,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAE,WAAU;0EAAsC,cAAc,KAAK;;;;;;;;;;;;;;;;;;;;;;4CAO7E,cAAc,cAAc,kBAC3B,8OAAC,gIAAA,CAAA,OAAI;;kEACH,8OAAC,gIAAA,CAAA,aAAU;;0EACT,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,gIAAA,CAAA,kBAAe;0EAAC;;;;;;;;;;;;kEAInB,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC,0IAAA,CAAA,aAAU;4DAAC,WAAU;sEACpB,cAAA,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;8EAAM,cAAc,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;4CAQ5C,cAAc,cAAc,IAAI,OAAO,IAAI,CAAC,cAAc,cAAc,EAAE,MAAM,GAAG,mBAClF,8OAAC,gIAAA,CAAA,OAAI;;kEACH,8OAAC,gIAAA,CAAA,aAAU;kEACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sEAAC;;;;;;;;;;;kEAEb,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC,0IAAA,CAAA,aAAU;4DAAC,WAAU;sEACpB,cAAA,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;8EAAM,KAAK,SAAS,CAAC,cAAc,cAAc,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAQpE,8OAAC,gIAAA,CAAA,OAAI;;kEACH,8OAAC,gIAAA,CAAA,aAAU;kEACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sEAAC;;;;;;;;;;;kEAEb,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;;;;;0FACf,8OAAC;;kGACC,8OAAC;wFAAE,WAAU;kGAAsB;;;;;;kGACnC,8OAAC;wFAAE,WAAU;kGACV,eAAe,cAAc,UAAU;;;;;;;;;;;;;;;;;;;;;;;8EAMhD,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAW,CAAC,qBAAqB,EACpC,cAAc,MAAM,KAAK,cAAc,iBACvC,cAAc,MAAM,KAAK,WAAW,eACpC,iBACA;;;;;;0FACF,8OAAC;;kGACC,8OAAC;wFAAE,WAAU;;4FAAsB;4FACtB,cAAc,MAAM,KAAK,cAAc,cACzC,cAAc,MAAM,KAAK,WAAW,WAAW;;;;;;;kGAE1D,8OAAC;wFAAE,WAAU;kGACV,eAAe,cAAc,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAUtD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,SAAS,IAAM,mBAAmB;kEACnC;;;;;;kEAGD,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,SAAS;4DACP,MAAM,OAAO,KAAK,SAAS,CAAC,eAAe,MAAM;4DACjD,MAAM,OAAO,IAAI,KAAK;gEAAC;6DAAK,EAAE;gEAAE,MAAM;4DAAmB;4DACzD,MAAM,MAAM,IAAI,eAAe,CAAC;4DAChC,MAAM,IAAI,SAAS,aAAa,CAAC;4DACjC,EAAE,IAAI,GAAG;4DACT,EAAE,QAAQ,GAAG,CAAC,kBAAkB,EAAE,cAAc,YAAY,CAAC,KAAK,CAAC;4DACnE,SAAS,IAAI,CAAC,WAAW,CAAC;4DAC1B,EAAE,KAAK;4DACP,SAAS,IAAI,CAAC,WAAW,CAAC;4DAC1B,IAAI,eAAe,CAAC;wDACtB;;0EAEA,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;8CAQ/C,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAU,WAAU;8CACrC,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,8OAAC;gDAAG,WAAU;0DAA2B;;;;;;0DACzC,8OAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAGlD,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,IAAM,qBAAqB;gDACpC,UAAU,QAAQ,MAAM,KAAK,eAAe,QAAQ,MAAM,KAAK;;kEAE/D,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU7C,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAmB,cAAc;0BAC7C,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,8OAAC,kIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAKrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsB;;;;;;sDACpC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;8CAIR,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAqB;;;;;;sDACpC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,WAAU;;;;;;8DAEZ,8OAAC;oDAAM,SAAQ;oDAAqB,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAO9D,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,qBAAqB;8CACrC;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU,gBAAgB,SAAS;8CAElC,gBAAgB,SAAS,GAAG,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;0BAOrD,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAmB,cAAc;0BAC7C,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,8OAAC,kIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAIrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAY;;;;;;sDAC3B,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,gBAAgB,SAAS;4CAChC,UAAU,CAAC,IAAM,mBAAmB;oDAAE,GAAG,eAAe;oDAAE,WAAW,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACpF,aAAY;;;;;;;;;;;;8CAGhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAmB;;;;;;sDAClC,8OAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,OAAO,gBAAgB,gBAAgB;4CACvC,UAAU,CAAC,IAAM,mBAAmB;oDAAE,GAAG,eAAe;oDAAE,kBAAkB,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC3F,aAAY;4CACZ,MAAM;;;;;;;;;;;;8CAGV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAa;;;;;;sDAC5B,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,gBAAgB,UAAU;4CACjC,UAAU,CAAC,IAAM,mBAAmB;oDAAE,GAAG,eAAe;oDAAE,YAAY,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACrF,aAAY;;;;;;;;;;;;8CAGhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAa;;;;;;sDAC5B,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,gBAAgB,UAAU;4CACjC,UAAU,CAAC,IAAM,mBAAmB;oDAAE,GAAG,eAAe;oDAAE,YAAY,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACrF,aAAY;;;;;;;;;;;;;;;;;;sCAIlB,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,qBAAqB;8CACrC;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU,CAAC,gBAAgB,SAAS,IAAI,CAAC,gBAAgB,UAAU,IAAI,gBAAgB,SAAS;8CAE/F,gBAAgB,SAAS,GAAG,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;0BAOvD,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAmB,cAAc;0BAC7C,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,8OAAC,kIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAIrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAA4C;;;;;;sDAC7D,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAS,eAAe,eAAe,UAAU;4CAAoB,WAAU;sDACnF,eAAe,UAAU;;;;;;;;;;;;gCAI7B,eAAe,WAAW,2BACzB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAA4C;;;;;;sDAC7D,8OAAC,iIAAA,CAAA,QAAK;4CACJ,OAAO,cAAc,UAAU,IAAI;4CACnC,QAAQ;4CACR,WAAU;;;;;;;;;;;;gCAKf,eAAe,WAAW,6BACzB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAA4C;;;;;;sDAC7D,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAS,cAAc,MAAM,EAAE,UAAU,YAAY;4CAAe,WAAU;sDAClF,cAAc,MAAM,EAAE,UAAU,WAAW;;;;;;wCAE7C,cAAc,MAAM,EAAE,0BACrB,8OAAC;4CAAE,WAAU;sDAAiC,cAAc,MAAM,CAAC,QAAQ;;;;;;;;;;;;gCAKhF,eAAe,WAAW,YAAY,cAAc,KAAK,kBACxD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAA4C;;;;;;sDAC7D,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;0DAA4B,cAAc,KAAK;;;;;;;;;;;;;;;;;8CAKlE,8OAAC;oCAAI,WAAU;8CACZ,mBAAmB,eAAe,WAAW,4BAC5C,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU,sBAAsB,SAAS;kDAExC,sBAAsB,SAAS,GAAG,gBAAgB;;;;;6DAGrD,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU,gBAAgB,SAAS;kDAElC,gBAAgB,SAAS,GAAG,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShE", "debugId": null}}, {"offset": {"line": 5755, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Progress = React.forwardRef<\r\n  React.ElementRef<typeof ProgressPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\r\n>(({ className, value, ...props }, ref) => (\r\n  <ProgressPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ProgressPrimitive.Indicator\r\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\r\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\r\n    />\r\n  </ProgressPrimitive.Root>\r\n))\r\nProgress.displayName = ProgressPrimitive.Root.displayName\r\n\r\nexport { Progress }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,oKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,oKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 5794, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/app/codegen/components/StatsOverview.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { \r\n  Activity, \r\n  Clock, \r\n  Code, \r\n  TrendingUp, \r\n  Users, \r\n  CheckCircle, \r\n  XCircle,\r\n  BarChart3,\r\n  Calendar\r\n} from 'lucide-react';\r\n\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Progress } from '@/components/ui/progress';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\n\r\nimport type { CodegenStatsResponse } from '@/lib/types';\r\n\r\ninterface StatsOverviewProps {\r\n  stats?: CodegenStatsResponse;\r\n}\r\n\r\nexport function StatsOverview({ stats }: StatsOverviewProps) {\r\n  const formatDuration = (seconds?: number) => {\r\n    if (!seconds) return 'N/A';\r\n    const minutes = Math.floor(seconds / 60);\r\n    const remainingSeconds = Math.floor(seconds % 60);\r\n    return `${minutes}m ${remainingSeconds}s`;\r\n  };\r\n\r\n  const formatDateTime = (dateString?: string) => {\r\n    if (!dateString) return 'Never';\r\n    return new Date(dateString).toLocaleString('en-US', {\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric',\r\n      hour: '2-digit',\r\n      minute: '2-digit',\r\n    });\r\n  };\r\n\r\n  const getSuccessRate = () => {\r\n    if (!stats || stats.total_sessions === 0) return 0;\r\n    return Math.round((stats.completed_sessions / stats.total_sessions) * 100);\r\n  };\r\n\r\n  const getLanguageEntries = () => {\r\n    if (!stats?.sessions_by_language) return [];\r\n    return Object.entries(stats.sessions_by_language)\r\n      .sort(([,a], [,b]) => b - a)\r\n      .slice(0, 5); // Top 5 languages\r\n  };\r\n\r\n  const getLanguageColor = (language: string) => {\r\n    switch (language) {\r\n      case 'javascript':\r\n        return 'bg-yellow-500';\r\n      case 'typescript':\r\n        return 'bg-blue-500';\r\n      case 'python':\r\n        return 'bg-green-500';\r\n      case 'java':\r\n        return 'bg-orange-500';\r\n      case 'csharp':\r\n        return 'bg-purple-500';\r\n      default:\r\n        return 'bg-gray-500';\r\n    }\r\n  };\r\n\r\n  if (!stats) {\r\n    return (\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n        {[1, 2, 3, 4, 5, 6].map((i) => (\r\n          <Card key={i}>\r\n            <CardHeader>\r\n              <Skeleton className=\"h-5 w-32\" />\r\n              <Skeleton className=\"h-4 w-24\" />\r\n            </CardHeader>\r\n            <CardContent>\r\n              <Skeleton className=\"h-8 w-16\" />\r\n            </CardContent>\r\n          </Card>\r\n        ))}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Overview Cards */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\r\n        <Card>\r\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n            <CardTitle className=\"text-sm font-medium\">Total Sessions</CardTitle>\r\n            <Activity className=\"h-4 w-4 text-muted-foreground\" />\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"text-2xl font-bold\">{stats.total_sessions}</div>\r\n            <p className=\"text-xs text-muted-foreground\">\r\n              {stats.active_sessions} currently active\r\n            </p>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Card>\r\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n            <CardTitle className=\"text-sm font-medium\">Success Rate</CardTitle>\r\n            <TrendingUp className=\"h-4 w-4 text-muted-foreground\" />\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"text-2xl font-bold\">{getSuccessRate()}%</div>\r\n            <Progress value={getSuccessRate()} className=\"mt-2\" />\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Card>\r\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n            <CardTitle className=\"text-sm font-medium\">Completed</CardTitle>\r\n            <CheckCircle className=\"h-4 w-4 text-green-600\" />\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"text-2xl font-bold text-green-600\">\r\n              {stats.completed_sessions}\r\n            </div>\r\n            <p className=\"text-xs text-muted-foreground\">\r\n              {stats.failed_sessions} failed\r\n            </p>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Card>\r\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n            <CardTitle className=\"text-sm font-medium\">Avg Duration</CardTitle>\r\n            <Clock className=\"h-4 w-4 text-muted-foreground\" />\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"text-2xl font-bold\">\r\n              {formatDuration(stats.avg_session_duration)}\r\n            </div>\r\n            <p className=\"text-xs text-muted-foreground\">\r\n              per session\r\n            </p>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n\r\n      {/* Detailed Stats */}\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n        {/* Language Distribution */}\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle className=\"flex items-center gap-2\">\r\n              <Code className=\"h-5 w-5\" />\r\n              Language Distribution\r\n            </CardTitle>\r\n            <CardDescription>\r\n              Sessions by target programming language\r\n            </CardDescription>\r\n          </CardHeader>\r\n          <CardContent>\r\n            {getLanguageEntries().length === 0 ? (\r\n              <div className=\"flex items-center justify-center h-32 text-muted-foreground\">\r\n                <div className=\"text-center\">\r\n                  <BarChart3 className=\"h-8 w-8 mx-auto mb-2 opacity-50\" />\r\n                  <p className=\"text-sm\">No data available</p>\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              <div className=\"space-y-4\">\r\n                {getLanguageEntries().map(([language, count]) => {\r\n                  const percentage = stats.total_sessions > 0 \r\n                    ? Math.round((count / stats.total_sessions) * 100) \r\n                    : 0;\r\n                  \r\n                  return (\r\n                    <div key={language} className=\"space-y-2\">\r\n                      <div className=\"flex items-center justify-between text-sm\">\r\n                        <div className=\"flex items-center gap-2\">\r\n                          <div \r\n                            className={`w-3 h-3 rounded-full ${getLanguageColor(language)}`}\r\n                          />\r\n                          <span className=\"capitalize font-medium\">{language}</span>\r\n                        </div>\r\n                        <div className=\"flex items-center gap-2\">\r\n                          <span className=\"text-muted-foreground\">{count}</span>\r\n                          <Badge variant=\"outline\" className=\"text-xs\">\r\n                            {percentage}%\r\n                          </Badge>\r\n                        </div>\r\n                      </div>\r\n                      <Progress value={percentage} className=\"h-2\" />\r\n                    </div>\r\n                  );\r\n                })}\r\n              </div>\r\n            )}\r\n          </CardContent>\r\n        </Card>\r\n\r\n        {/* Session Status Breakdown */}\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle className=\"flex items-center gap-2\">\r\n              <Users className=\"h-5 w-5\" />\r\n              Session Status\r\n            </CardTitle>\r\n            <CardDescription>\r\n              Breakdown of session completion status\r\n            </CardDescription>\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"space-y-4\">\r\n              <div className=\"flex items-center justify-between p-3 bg-green-50 dark:bg-green-950 rounded-lg\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <CheckCircle className=\"h-5 w-5 text-green-600\" />\r\n                  <span className=\"font-medium\">Completed</span>\r\n                </div>\r\n                <Badge variant=\"secondary\" className=\"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300\">\r\n                  {stats.completed_sessions}\r\n                </Badge>\r\n              </div>\r\n\r\n              <div className=\"flex items-center justify-between p-3 bg-red-50 dark:bg-red-950 rounded-lg\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <XCircle className=\"h-5 w-5 text-red-600\" />\r\n                  <span className=\"font-medium\">Failed</span>\r\n                </div>\r\n                <Badge variant=\"secondary\" className=\"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300\">\r\n                  {stats.failed_sessions}\r\n                </Badge>\r\n              </div>\r\n\r\n              <div className=\"flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-950 rounded-lg\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <Activity className=\"h-5 w-5 text-blue-600\" />\r\n                  <span className=\"font-medium\">Active</span>\r\n                </div>\r\n                <Badge variant=\"secondary\" className=\"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300\">\r\n                  {stats.active_sessions}\r\n                </Badge>\r\n              </div>\r\n\r\n              {stats.avg_generated_lines && (\r\n                <div className=\"mt-6 p-3 bg-muted rounded-lg\">\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <Code className=\"h-4 w-4 text-muted-foreground\" />\r\n                      <span className=\"text-sm font-medium\">Avg Lines Generated</span>\r\n                    </div>\r\n                    <span className=\"text-lg font-bold\">\r\n                      {Math.round(stats.avg_generated_lines)}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              <div className=\"mt-6 p-3 bg-muted rounded-lg\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Calendar className=\"h-4 w-4 text-muted-foreground\" />\r\n                    <span className=\"text-sm font-medium\">Last Session</span>\r\n                  </div>\r\n                  <span className=\"text-sm\">\r\n                    {formatDateTime(stats.last_session_at)}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;AACA;AAlBA;;;;;;;AA0BO,SAAS,cAAc,EAAE,KAAK,EAAsB;IACzD,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,SAAS,OAAO;QACrB,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;QACrC,MAAM,mBAAmB,KAAK,KAAK,CAAC,UAAU;QAC9C,OAAO,GAAG,QAAQ,EAAE,EAAE,iBAAiB,CAAC,CAAC;IAC3C;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,YAAY,OAAO;QACxB,OAAO,IAAI,KAAK,YAAY,cAAc,CAAC,SAAS;YAClD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,SAAS,MAAM,cAAc,KAAK,GAAG,OAAO;QACjD,OAAO,KAAK,KAAK,CAAC,AAAC,MAAM,kBAAkB,GAAG,MAAM,cAAc,GAAI;IACxE;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,OAAO,sBAAsB,OAAO,EAAE;QAC3C,OAAO,OAAO,OAAO,CAAC,MAAM,oBAAoB,EAC7C,IAAI,CAAC,CAAC,GAAE,EAAE,EAAE,GAAE,EAAE,GAAK,IAAI,GACzB,KAAK,CAAC,GAAG,IAAI,kBAAkB;IACpC;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,CAAC,OAAO;QACV,qBACE,8OAAC;YAAI,WAAU;sBACZ;gBAAC;gBAAG;gBAAG;gBAAG;gBAAG;gBAAG;aAAE,CAAC,GAAG,CAAC,CAAC,kBACvB,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;;8CACT,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;sCAEtB,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;mBANb;;;;;;;;;;IAYnB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAEtB,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;kDAAsB,MAAM,cAAc;;;;;;kDACzD,8OAAC;wCAAE,WAAU;;4CACV,MAAM,eAAe;4CAAC;;;;;;;;;;;;;;;;;;;kCAK7B,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;0CAExB,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;;4CAAsB;4CAAiB;;;;;;;kDACtD,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,OAAO;wCAAkB,WAAU;;;;;;;;;;;;;;;;;;kCAIjD,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;0CAEzB,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;kDACZ,MAAM,kBAAkB;;;;;;kDAE3B,8OAAC;wCAAE,WAAU;;4CACV,MAAM,eAAe;4CAAC;;;;;;;;;;;;;;;;;;;kCAK7B,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;0CAEnB,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;kDACZ,eAAe,MAAM,oBAAoB;;;;;;kDAE5C,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;;;0BAQnD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAG9B,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;0CACT,qBAAqB,MAAM,KAAK,kBAC/B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,8OAAC;gDAAE,WAAU;0DAAU;;;;;;;;;;;;;;;;yDAI3B,8OAAC;oCAAI,WAAU;8CACZ,qBAAqB,GAAG,CAAC,CAAC,CAAC,UAAU,MAAM;wCAC1C,MAAM,aAAa,MAAM,cAAc,GAAG,IACtC,KAAK,KAAK,CAAC,AAAC,QAAQ,MAAM,cAAc,GAAI,OAC5C;wCAEJ,qBACE,8OAAC;4CAAmB,WAAU;;8DAC5B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEACC,WAAW,CAAC,qBAAqB,EAAE,iBAAiB,WAAW;;;;;;8EAEjE,8OAAC;oEAAK,WAAU;8EAA0B;;;;;;;;;;;;sEAE5C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAyB;;;;;;8EACzC,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAU,WAAU;;wEAChC;wEAAW;;;;;;;;;;;;;;;;;;;8DAIlB,8OAAC,oIAAA,CAAA,WAAQ;oDAAC,OAAO;oDAAY,WAAU;;;;;;;2CAf/B;;;;;oCAkBd;;;;;;;;;;;;;;;;;kCAOR,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAG/B,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,8OAAC;4DAAK,WAAU;sEAAc;;;;;;;;;;;;8DAEhC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;8DAClC,MAAM,kBAAkB;;;;;;;;;;;;sDAI7B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,4MAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;sEACnB,8OAAC;4DAAK,WAAU;sEAAc;;;;;;;;;;;;8DAEhC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;8DAClC,MAAM,eAAe;;;;;;;;;;;;sDAI1B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;4DAAK,WAAU;sEAAc;;;;;;;;;;;;8DAEhC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;8DAClC,MAAM,eAAe;;;;;;;;;;;;wCAIzB,MAAM,mBAAmB,kBACxB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;gEAAK,WAAU;0EAAsB;;;;;;;;;;;;kEAExC,8OAAC;wDAAK,WAAU;kEACb,KAAK,KAAK,CAAC,MAAM,mBAAmB;;;;;;;;;;;;;;;;;sDAM7C,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;gEAAK,WAAU;0EAAsB;;;;;;;;;;;;kEAExC,8OAAC;wDAAK,WAAU;kEACb,eAAe,MAAM,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzD", "debugId": null}}, {"offset": {"line": 6627, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Tomas/qak/web/src/app/codegen/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\r\nimport { Plus, Play, Square, Download, Trash2, RefreshCw, Monitor, Activity } from 'lucide-react';\r\n\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\r\nimport { useToast } from '@/hooks/use-toast';\r\n\r\nimport {\r\n  startCodegenSession,\r\n  stopCodegenSession,\r\n  listCodegenSessions,\r\n  getCodegenStats,\r\n  getCodegenHealth,\r\n  bulkCleanupCodegenSessions\r\n} from '@/lib/api';\r\n\r\nimport type {\r\n  CodegenSessionInfo,\r\n  CodegenStatsResponse,\r\n  CodegenHealthResponse,\r\n  PlaywrightCodegenRequest\r\n} from '@/lib/types';\r\n\r\nimport { NewSessionDialog } from './components/NewSessionDialog';\r\nimport { SessionsList } from './components/SessionsList';\r\nimport { SessionDetails } from './components/SessionDetails';\r\nimport { StatsOverview } from './components/StatsOverview';\r\n\r\nexport default function CodeGenPage() {\r\n  const [selectedSessionId, setSelectedSessionId] = useState<string | null>(null);\r\n  const [showNewSessionDialog, setShowNewSessionDialog] = useState(false);\r\n  const { toast } = useToast();\r\n  const queryClient = useQueryClient();\r\n\r\n  // Queries\r\n  const { data: sessions, isLoading: sessionsLoading } = useQuery({\r\n    queryKey: ['codegen-sessions'],\r\n    queryFn: listCodegenSessions,\r\n    refetchInterval: 5000, // Refresh every 5 seconds\r\n  });\r\n\r\n  const { data: stats } = useQuery({\r\n    queryKey: ['codegen-stats'],\r\n    queryFn: getCodegenStats,\r\n    refetchInterval: 10000, // Refresh every 10 seconds\r\n  });\r\n\r\n  const { data: health } = useQuery({\r\n    queryKey: ['codegen-health'],\r\n    queryFn: getCodegenHealth,\r\n    refetchInterval: 30000, // Refresh every 30 seconds\r\n  });\r\n\r\n  // Mutations\r\n  const startSessionMutation = useMutation({\r\n    mutationFn: startCodegenSession,\r\n    onSuccess: (data) => {\r\n      toast({\r\n        title: \"Recording Started\",\r\n        description: `Session ${data.session_id.slice(0, 8)}... started in headless mode - recording will begin automatically!`,\r\n      });\r\n      queryClient.invalidateQueries({ queryKey: ['codegen-sessions'] });\r\n      setSelectedSessionId(data.session_id);\r\n      setShowNewSessionDialog(false);\r\n    },\r\n    onError: (error) => {\r\n      toast({\r\n        title: \"Failed to Start Recording\",\r\n        description: error.message,\r\n        variant: \"destructive\",\r\n      });\r\n    },\r\n  });\r\n\r\n  const stopSessionMutation = useMutation({\r\n    mutationFn: stopCodegenSession,\r\n    onSuccess: () => {\r\n      toast({\r\n        title: \"Recording Stopped\",\r\n        description: \"Session completed - check the generated code in the details panel\",\r\n      });\r\n      queryClient.invalidateQueries({ queryKey: ['codegen-sessions'] });\r\n    },\r\n    onError: (error) => {\r\n      toast({\r\n        title: \"Error Stopping Recording\",\r\n        description: error.message,\r\n        variant: \"destructive\",\r\n      });\r\n    },\r\n  });\r\n\r\n  const bulkCleanupMutation = useMutation({\r\n    mutationFn: bulkCleanupCodegenSessions,\r\n    onSuccess: (data) => {\r\n      toast({\r\n        title: \"Cleanup Complete\",\r\n        description: `Cleaned up ${data.sessions_cleaned} sessions`,\r\n      });\r\n      queryClient.invalidateQueries({ queryKey: ['codegen-sessions'] });\r\n    },\r\n    onError: (error) => {\r\n      toast({\r\n        title: \"Error\",\r\n        description: error.message,\r\n        variant: \"destructive\",\r\n      });\r\n    },\r\n  });\r\n\r\n  const handleStartSession = (request: PlaywrightCodegenRequest) => {\r\n    startSessionMutation.mutate(request);\r\n  };\r\n\r\n  const handleStopSession = (sessionId: string) => {\r\n    stopSessionMutation.mutate(sessionId);\r\n  };\r\n\r\n  const handleBulkCleanup = () => {\r\n    bulkCleanupMutation.mutate();\r\n  };\r\n\r\n  const handleRefresh = () => {\r\n    queryClient.invalidateQueries({ queryKey: ['codegen-sessions'] });\r\n    queryClient.invalidateQueries({ queryKey: ['codegen-stats'] });\r\n  };\r\n\r\n  return (\r\n    <div className=\"container mx-auto p-6 space-y-6\">\r\n      {/* Header */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <div>\r\n          <h1 className=\"text-3xl font-bold tracking-tight\">Automated Test Recorder</h1>\r\n          <p className=\"text-muted-foreground mt-2\">\r\n            Record browser interactions in headless mode and automatically generate test code\r\n          </p>\r\n        </div>\r\n        <div className=\"flex items-center gap-2\">\r\n          <Button \r\n            variant=\"outline\" \r\n            size=\"sm\" \r\n            onClick={handleRefresh}\r\n            disabled={sessionsLoading}\r\n          >\r\n            <RefreshCw className=\"h-4 w-4 mr-2\" />\r\n            Refresh\r\n          </Button>\r\n          <Button \r\n            variant=\"outline\" \r\n            size=\"sm\" \r\n            onClick={handleBulkCleanup}\r\n            disabled={bulkCleanupMutation.isPending}\r\n          >\r\n            <Trash2 className=\"h-4 w-4 mr-2\" />\r\n            Cleanup\r\n          </Button>\r\n          <Button \r\n            onClick={() => setShowNewSessionDialog(true)}\r\n            disabled={startSessionMutation.isPending}\r\n          >\r\n            <Plus className=\"h-4 w-4 mr-2\" />\r\n            Start Recording\r\n          </Button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Health Status */}\r\n      {health && (\r\n        <Card>\r\n          <CardHeader className=\"pb-3\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <CardTitle className=\"text-sm font-medium\">Service Status</CardTitle>\r\n              <Badge variant={health.playwright_available ? \"default\" : \"destructive\"}>\r\n                <Activity className=\"h-3 w-3 mr-1\" />\r\n                {health.playwright_available ? \"Ready\" : \"Unavailable\"}\r\n              </Badge>\r\n            </div>\r\n          </CardHeader>\r\n          <CardContent className=\"pt-0\">\r\n            <div className=\"flex items-center gap-4 text-sm text-muted-foreground\">\r\n              <div>Playwright: {health.playwright_version || \"Not available\"}</div>\r\n              <div>Active Sessions: {health.active_sessions}</div>\r\n              <div>Total Sessions: {health.total_sessions}</div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      )}\r\n\r\n      {/* How it Works */}\r\n      <Card className=\"bg-muted/30\">\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <Monitor className=\"h-5 w-5\" />\r\n            How Automated Recording Works\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\r\n            <div className=\"text-center space-y-2\">\r\n              <div className=\"w-8 h-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center mx-auto text-sm font-medium\">\r\n                1\r\n              </div>\r\n              <h3 className=\"font-medium\">Start Recording</h3>\r\n              <p className=\"text-sm text-muted-foreground\">\r\n                Click \"Start Recording\" to begin automated recording in headless mode\r\n              </p>\r\n            </div>\r\n            <div className=\"text-center space-y-2\">\r\n              <div className=\"w-8 h-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center mx-auto text-sm font-medium\">\r\n                2\r\n              </div>\r\n              <h3 className=\"font-medium\">Automated Recording</h3>\r\n              <p className=\"text-sm text-muted-foreground\">\r\n                Playwright automatically records interactions based on the provided URL and configuration\r\n              </p>\r\n            </div>\r\n            <div className=\"text-center space-y-2\">\r\n              <div className=\"w-8 h-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center mx-auto text-sm font-medium\">\r\n                3\r\n              </div>\r\n              <h3 className=\"font-medium\">Generate Code</h3>\r\n              <p className=\"text-sm text-muted-foreground\">\r\n                Stop recording to get test automation code, then convert to QAK test case\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Main Content */}\r\n      <Tabs defaultValue=\"sessions\" className=\"space-y-4\">\r\n        <TabsList>\r\n          <TabsTrigger value=\"sessions\">\r\n            <Monitor className=\"h-4 w-4 mr-2\" />\r\n            Sessions\r\n          </TabsTrigger>\r\n          <TabsTrigger value=\"stats\">\r\n            <Activity className=\"h-4 w-4 mr-2\" />\r\n            Statistics\r\n          </TabsTrigger>\r\n        </TabsList>\r\n\r\n        <TabsContent value=\"sessions\" className=\"space-y-4\">\r\n          {/* Active Session Alert */}\r\n          {sessions?.sessions?.some(s => s.status === 'running') && (\r\n            <Card className=\"border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950\">\r\n              <CardContent className=\"pt-6\">\r\n                <div className=\"flex items-center gap-3\">\r\n                  <div className=\"w-3 h-3 rounded-full bg-green-500 animate-pulse\" />\r\n                  <div>\r\n                    <p className=\"font-medium text-green-800 dark:text-green-300\">\r\n                      Recording session active\r\n                    </p>\r\n                    <p className=\"text-sm text-green-600 dark:text-green-400\">\r\n                      Headless browser is running - recording interactions automatically\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          )}\r\n\r\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n            {/* Sessions List */}\r\n            <div className=\"space-y-4\">\r\n              <SessionsList\r\n                sessions={sessions?.sessions || []}\r\n                isLoading={sessionsLoading}\r\n                selectedSessionId={selectedSessionId}\r\n                onSelectSession={setSelectedSessionId}\r\n                onStopSession={handleStopSession}\r\n                isStoppingSession={stopSessionMutation.isPending}\r\n              />\r\n            </div>\r\n\r\n            {/* Session Details */}\r\n            <div className=\"space-y-4\">\r\n              {selectedSessionId ? (\r\n                <SessionDetails\r\n                  sessionId={selectedSessionId}\r\n                  onRefresh={handleRefresh}\r\n                />\r\n              ) : (\r\n                <Card>\r\n                  <CardContent className=\"flex items-center justify-center h-64 text-muted-foreground\">\r\n                    Select a session to view details\r\n                  </CardContent>\r\n                </Card>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </TabsContent>\r\n\r\n        <TabsContent value=\"stats\" className=\"space-y-4\">\r\n          <StatsOverview stats={stats} />\r\n        </TabsContent>\r\n      </Tabs>\r\n\r\n      {/* New Session Dialog */}\r\n      <NewSessionDialog\r\n        open={showNewSessionDialog}\r\n        onOpenChange={setShowNewSessionDialog}\r\n        onStartSession={handleStartSession}\r\n        isStarting={startSessionMutation.isPending}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AAEA;AAgBA;AACA;AACA;AACA;AA/BA;;;;;;;;;;;;;;;AAiCe,SAAS;IACtB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1E,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,UAAU;IACV,MAAM,EAAE,MAAM,QAAQ,EAAE,WAAW,eAAe,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QAC9D,UAAU;YAAC;SAAmB;QAC9B,SAAS,iHAAA,CAAA,sBAAmB;QAC5B,iBAAiB;IACnB;IAEA,MAAM,EAAE,MAAM,KAAK,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QAC/B,UAAU;YAAC;SAAgB;QAC3B,SAAS,iHAAA,CAAA,kBAAe;QACxB,iBAAiB;IACnB;IAEA,MAAM,EAAE,MAAM,MAAM,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QAChC,UAAU;YAAC;SAAiB;QAC5B,SAAS,iHAAA,CAAA,mBAAgB;QACzB,iBAAiB;IACnB;IAEA,YAAY;IACZ,MAAM,uBAAuB,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACvC,YAAY,iHAAA,CAAA,sBAAmB;QAC/B,WAAW,CAAC;YACV,MAAM;gBACJ,OAAO;gBACP,aAAa,CAAC,QAAQ,EAAE,KAAK,UAAU,CAAC,KAAK,CAAC,GAAG,GAAG,kEAAkE,CAAC;YACzH;YACA,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAmB;YAAC;YAC/D,qBAAqB,KAAK,UAAU;YACpC,wBAAwB;QAC1B;QACA,SAAS,CAAC;YACR,MAAM;gBACJ,OAAO;gBACP,aAAa,MAAM,OAAO;gBAC1B,SAAS;YACX;QACF;IACF;IAEA,MAAM,sBAAsB,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACtC,YAAY,iHAAA,CAAA,qBAAkB;QAC9B,WAAW;YACT,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YACA,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAmB;YAAC;QACjE;QACA,SAAS,CAAC;YACR,MAAM;gBACJ,OAAO;gBACP,aAAa,MAAM,OAAO;gBAC1B,SAAS;YACX;QACF;IACF;IAEA,MAAM,sBAAsB,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACtC,YAAY,iHAAA,CAAA,6BAA0B;QACtC,WAAW,CAAC;YACV,MAAM;gBACJ,OAAO;gBACP,aAAa,CAAC,WAAW,EAAE,KAAK,gBAAgB,CAAC,SAAS,CAAC;YAC7D;YACA,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAmB;YAAC;QACjE;QACA,SAAS,CAAC;YACR,MAAM;gBACJ,OAAO;gBACP,aAAa,MAAM,OAAO;gBAC1B,SAAS;YACX;QACF;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,qBAAqB,MAAM,CAAC;IAC9B;IAEA,MAAM,oBAAoB,CAAC;QACzB,oBAAoB,MAAM,CAAC;IAC7B;IAEA,MAAM,oBAAoB;QACxB,oBAAoB,MAAM;IAC5B;IAEA,MAAM,gBAAgB;QACpB,YAAY,iBAAiB,CAAC;YAAE,UAAU;gBAAC;aAAmB;QAAC;QAC/D,YAAY,iBAAiB,CAAC;YAAE,UAAU;gBAAC;aAAgB;QAAC;IAC9D;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAI5C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGxC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU,oBAAoB,SAAS;;kDAEvC,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGrC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,IAAM,wBAAwB;gCACvC,UAAU,qBAAqB,SAAS;;kDAExC,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;YAOtC,wBACC,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAsB;;;;;;8CAC3C,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAS,OAAO,oBAAoB,GAAG,YAAY;;sDACxD,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCACnB,OAAO,oBAAoB,GAAG,UAAU;;;;;;;;;;;;;;;;;;kCAI/C,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;wCAAI;wCAAa,OAAO,kBAAkB,IAAI;;;;;;;8CAC/C,8OAAC;;wCAAI;wCAAkB,OAAO,eAAe;;;;;;;8CAC7C,8OAAC;;wCAAI;wCAAiB,OAAO,cAAc;;;;;;;;;;;;;;;;;;;;;;;;0BAOnD,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAInC,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAuH;;;;;;sDAGtI,8OAAC;4CAAG,WAAU;sDAAc;;;;;;sDAC5B,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAI/C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAuH;;;;;;sDAGtI,8OAAC;4CAAG,WAAU;sDAAc;;;;;;sDAC5B,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAI/C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAuH;;;;;;sDAGtI,8OAAC;4CAAG,WAAU;sDAAc;;;;;;sDAC5B,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASrD,8OAAC,gIAAA,CAAA,OAAI;gBAAC,cAAa;gBAAW,WAAU;;kCACtC,8OAAC,gIAAA,CAAA,WAAQ;;0CACP,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;;kDACjB,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGtC,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;;kDACjB,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAKzC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAW,WAAU;;4BAErC,UAAU,UAAU,KAAK,CAAA,IAAK,EAAE,MAAM,KAAK,4BAC1C,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAiD;;;;;;kEAG9D,8OAAC;wDAAE,WAAU;kEAA6C;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASpE,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oJAAA,CAAA,eAAY;4CACX,UAAU,UAAU,YAAY,EAAE;4CAClC,WAAW;4CACX,mBAAmB;4CACnB,iBAAiB;4CACjB,eAAe;4CACf,mBAAmB,oBAAoB,SAAS;;;;;;;;;;;kDAKpD,8OAAC;wCAAI,WAAU;kDACZ,kCACC,8OAAC,sJAAA,CAAA,iBAAc;4CACb,WAAW;4CACX,WAAW;;;;;iEAGb,8OAAC,gIAAA,CAAA,OAAI;sDACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;0DAA8D;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS/F,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAQ,WAAU;kCACnC,cAAA,8OAAC,qJAAA,CAAA,gBAAa;4BAAC,OAAO;;;;;;;;;;;;;;;;;0BAK1B,8OAAC,wJAAA,CAAA,mBAAgB;gBACf,MAAM;gBACN,cAAc;gBACd,gBAAgB;gBAChB,YAAY,qBAAqB,SAAS;;;;;;;;;;;;AAIlD", "debugId": null}}]}