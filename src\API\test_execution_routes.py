"""Rutas de API para ejecución de tests."""

import asyncio
import concurrent.futures
import os
from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import JSONResponse

from src.API.models import SmokeTestRequest, FullTestRequest
from src.Core.test_service import TestService
from src.Utilities.test_executors import run_smoke_test_sync, run_full_test_sync
from src.Utilities.response_transformers import (
    transform_backend_response_to_frontend_format,
    clean_data_for_json_serialization
)
# Importar utilidades de CAPTCHA
from src.Utilities.captcha_helper import (
    CaptchaConfig, 
    execute_with_captcha_handling,
    get_captcha_config
)

# Router para ejecución de tests
router = APIRouter(prefix="/api/tests", tags=["test-execution"])


def get_test_service():
    """Crea y devuelve una instancia del servicio de pruebas."""
    return TestService(api_key=os.environ.get("GOOGLE_API_KEY"))


@router.post("/smoke", summary="Ejecutar smoke test")
async def run_smoke_test(request: SmokeTestRequest):
    """Ejecuta un smoke test con las instrucciones proporcionadas."""
    try:
        # Preparar configuración
        configuration_data = None
        if request.configuration:
            # Convertir el modelo Pydantic a diccionario
            configuration_data = request.configuration.model_dump(exclude_none=True)
        
        # Ejecutar el test en un hilo separado para evitar conflictos de bucle de eventos
        loop = asyncio.get_event_loop()
        with concurrent.futures.ThreadPoolExecutor() as executor:
            result = await loop.run_in_executor(
                executor,
                run_smoke_test_sync,
                request.instructions,
                request.url,
                request.user_story,
                request.config_id,
                configuration_data
            )

        # Verificar que tengamos un resultado válido
        if result is None:
            return JSONResponse(
                status_code=500,
                content={"error": "No se recibió resultado del test"}
            )

        # Transformar la respuesta al formato esperado por el frontend
        transformed_result = transform_backend_response_to_frontend_format(result)

        # Limpiar los datos para serialización JSON
        cleaned_result = clean_data_for_json_serialization(transformed_result)

        # Si el resultado indica fallo pero se ejecutó correctamente (sin 500)
        if isinstance(cleaned_result, dict) and cleaned_result.get('metadata', {}).get('success') is False:
            # Devolver el error pero con código 200 para indicar que la API funcionó
            return JSONResponse(content=cleaned_result)

        # Resultado exitoso
        return JSONResponse(content=cleaned_result)
        
    except Exception as e:
        error_msg = str(e)
        print(f"Error en endpoint smoke test: {error_msg}")
        
        # Solo devolver 500 si realmente hay un error de la API, no del test
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": f"Error interno del servidor: {error_msg}"
            }
        )


@router.post("/full", summary="Ejecutar test completo")
async def run_full_test(request: FullTestRequest):
    """Ejecuta un test completo con el escenario Gherkin proporcionado."""
    try:
        # Ejecutar el test en un hilo separado para evitar conflictos de bucle de eventos
        loop = asyncio.get_event_loop()
        with concurrent.futures.ThreadPoolExecutor() as executor:
            result = await loop.run_in_executor(
                executor,
                run_full_test_sync,
                request.gherkin_scenario,
                request.url
            )

        # Transformar la respuesta al formato esperado por el frontend
        transformed_result = transform_backend_response_to_frontend_format(result)

        # Limpiar los datos para serialización JSON
        cleaned_result = clean_data_for_json_serialization(transformed_result)
        return JSONResponse(content=cleaned_result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/summarize", summary="Resumir resultados de prueba")
async def summarize_test_results(request, test_service = Depends(get_test_service)):
    """Genera un resumen de los resultados de prueba usando IA."""
    try:
        from src.API.models import SummarizeRequest
        from src.Core.test_service import TestService
        import os
        
        # Crear servicio si no está disponible
        if test_service is None:
            test_service = TestService(api_key=os.environ.get("GOOGLE_API_KEY"))
        
        # Validar el request
        if hasattr(request, 'test_results'):
            summary = test_service.summarize_test_results(request.test_results)
        else:
            raise HTTPException(status_code=400, detail="Invalid request format - missing test_results")
            
        return {"summary": summary}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/smoke-captcha", summary="Ejecutar smoke test con manejo de CAPTCHA")
async def run_smoke_test_with_captcha(request: SmokeTestRequest):
    """Ejecuta un smoke test con manejo especializado de CAPTCHAs de Google."""
    try:
        from src.Utilities.browser_helper import controller
        
        # Determinar tipo de sitio basado en URL para configuración óptima
        site_type = 'general_web'
        if request.url and 'google.com' in request.url:
            site_type = 'google_services'
        elif request.url and any(domain in request.url for domain in ['google', 'recaptcha']):
            site_type = 'google_search'
        
        # Obtener configuración específica para CAPTCHAs
        captcha_config = get_captcha_config(
            site_type=site_type,
            headless=False,  # Modo visible para CAPTCHAs
            max_steps=50,
            wait_between_actions=2.5,
            use_vision=True
        )
        
        # Configurar proxy si se proporciona en la configuración
        proxy_config = None
        if request.configuration and hasattr(request.configuration, 'proxy'):
            proxy_config = request.configuration.proxy
        
        # Ejecutar con manejo de CAPTCHA
        result = await execute_with_captcha_handling(
            scenario=request.instructions,
            controller_instance=controller,
            api_key=os.environ.get("GOOGLE_API_KEY"),
            proxy_config=proxy_config,
            url=request.url,
            max_retries=3
        )
        
        # Transformar resultado al formato esperado por el frontend
        if result is None:
            raise HTTPException(status_code=500, detail="Test execution returned no result")
        
        # Limpiar y transformar la respuesta
        cleaned_result = clean_data_for_json_serialization(result)
        
        response_data = {
            "success": True,
            "message": "CAPTCHA-aware smoke test executed successfully",
            "result": cleaned_result,
            "captcha_handling": True
        }
        
        return JSONResponse(content=response_data)
        
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "message": f"CAPTCHA-aware smoke test execution failed: {str(e)}",
                "error": str(e),
                "captcha_handling": True
            }
        )
