"""
Configuraciones predefinidas para diferentes contextos de uso del browser helper.
"""

import os
from typing import List, Optional, Dict, Any
from pathlib import Path

def load_env_file():
    """Carga variables de entorno desde el archivo .env manualmente."""
    try:
        # Intentar importar python-dotenv
        from dotenv import load_dotenv
        load_dotenv()
        return True
    except ImportError:
        # Si no está disponible python-dotenv, cargar manualmente
        env_path = Path(__file__).parent.parent.parent / ".env"
        if env_path.exists():
            with open(env_path, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
            return True
    return False

# Cargar variables de entorno al importar el módulo
load_env_file()

try:
    from src.Utilities.browser_helper import BrowserHelperConfig
    from src.Utilities.captcha_helper import CaptchaConfig
except ImportError:
    # Fallback if there are import issues
    class BrowserHelperConfig:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)
    
    class CaptchaConfig(BrowserHelperConfig):
        pass


class BrowserConfigurations:
    """Clase con configuraciones predefinidas para diferentes escenarios."""

    @staticmethod
    def get_ci_cd_config(**overrides) -> BrowserHelperConfig:
        """Configuración optimizada para CI/CD - velocidad máxima."""
        defaults = {
            "headless": True,
            "user_data_dir": None,  # Usar perfil temporal para evitar conflictos de SingletonLock
            "use_vision": False,  # Desactivar visión para máxima velocidad
            "enable_memory": False,  # Sin memoria para velocidad
            "deterministic_rendering": False,
            "highlight_elements": False,
            "viewport_expansion": 0,
            "minimum_wait_page_load_time": 0.1,
            "wait_for_network_idle_page_load_time": 0.2,
            "maximum_wait_page_load_time": 3.0,
            "wait_between_actions": 0.05,
            "max_steps": 25,
            "max_failures": 1,  # Fallar rápido en CI/CD
            "retry_delay": 2,
            "model_provider": "gemini",
            "temperature": 0.0
        }
        defaults.update(overrides)
        return BrowserHelperConfig(**defaults)

    @staticmethod
    def get_smoke_test_config(**overrides) -> BrowserHelperConfig:
        """Configuración para smoke tests - balance entre velocidad y confiabilidad."""
        # Verificar si existe configuración de embedding para habilitar memoria
        embedding_model = os.getenv("EMBEDDING_MODEL")
        embedding_provider = os.getenv("EMBEDDING_PROVIDER", "gemini")
        enable_memory = embedding_model is not None and embedding_model.strip() != ""
        
        defaults = {
            "headless": False,
            "user_data_dir": None,  # Usar perfil temporal para evitar conflictos de SingletonLock
            "use_vision": True,
            "enable_memory": enable_memory,  # Habilitar memoria si hay modelo de embedding
            "deterministic_rendering": True,
            "highlight_elements": True,
            "viewport_expansion": 500,  # Increased viewport for better element detection
            "minimum_wait_page_load_time": 0.5,  # Slightly increased for stability
            "wait_for_network_idle_page_load_time": 1.0,  # Increased for better page stability
            "maximum_wait_page_load_time": 12.0,  # Increased for slow-loading pages
            "wait_between_actions": 1.0,  # Increased to allow page stabilization and avoid API limits
            "max_steps": 30,
            "max_failures": 5,  # Increased to handle more retries
            "retry_delay": 10,  # Increased to avoid Gemini quota limits
            "model_provider": "gemini",
            "temperature": 0.1,  # Slightly increased for more diverse element selection strategies
            # Configuración de embeddings si está disponible
            "embedder_provider": embedding_provider if enable_memory else None,
            "embedder_model": embedding_model if enable_memory else None,
            "embedder_dims": int(os.getenv("EMBEDDING_DIMS", "768")) if enable_memory else None,
            "memory_interval": 5 if enable_memory else None,  # Resumir cada 5 pasos para smoke tests
            "memory_agent_id": "smoke_test_agent" if enable_memory else None
        }
        defaults.update(overrides)
        return BrowserHelperConfig(**defaults)

    @staticmethod
    def get_regression_test_config(**overrides) -> BrowserHelperConfig:
        """Configuración para tests de regresión - máxima confiabilidad."""
        defaults = {
            "headless": True,
            "user_data_dir": None,  # Usar perfil temporal para evitar conflictos de SingletonLock
            "use_vision": True,
            "enable_memory": True,
            "deterministic_rendering": True,
            "highlight_elements": False,
            "viewport_expansion": 800,
            "minimum_wait_page_load_time": 1.0,
            "wait_for_network_idle_page_load_time": 2.0,
            "maximum_wait_page_load_time": 20.0,
            "wait_between_actions": 1.0,
            "max_steps": 200,
            "max_failures": 5,
            "retry_delay": 10,
            "model_provider": "gemini",
            "temperature": 0.0,
            "generate_gif": True
        }
        defaults.update(overrides)
        return BrowserHelperConfig(**defaults)

    @staticmethod
    def get_development_config(**overrides) -> BrowserHelperConfig:
        """Configuración para desarrollo - debugging y observación."""
        defaults = {
            "headless": False,  # Visual para desarrollo
            "use_vision": True,
            "enable_memory": False,  # Simplificar para debugging
            "deterministic_rendering": True,
            "highlight_elements": True,
            "viewport_expansion": 1000,
            "minimum_wait_page_load_time": 1.0,
            "wait_for_network_idle_page_load_time": 2.0,
            "maximum_wait_page_load_time": 30.0,
            "wait_between_actions": 2.0,  # Lento para observación
            "max_steps": 50,
            "max_failures": 1,  # Fallar rápido para debugging
            "retry_delay": 5,
            "model_provider": "gemini",
            "temperature": 0.0,
            "save_conversation_path": "./debug_conversations",
            "generate_gif": True
        }
        defaults.update(overrides)
        return BrowserHelperConfig(**defaults)

    @staticmethod
    def get_production_config(allowed_domains: List[str], **overrides) -> BrowserHelperConfig:
        """Configuración para producción - seguridad y estabilidad."""
        defaults = {
            "headless": True,
            "user_data_dir": None,  # Usar perfil temporal para evitar conflictos de SingletonLock
            "allowed_domains": allowed_domains,
            "disable_security": False,
            "use_vision": True,
            "enable_memory": True,
            "deterministic_rendering": True,
            "highlight_elements": False,
            "viewport_expansion": 500,
            "minimum_wait_page_load_time": 0.8,
            "wait_for_network_idle_page_load_time": 1.5,
            "maximum_wait_page_load_time": 15.0,
            "wait_between_actions": 0.8,
            "max_steps": 100,
            "max_failures": 3,
            "retry_delay": 8,
            "model_provider": "gemini",
            "temperature": 0.0
        }
        defaults.update(overrides)
        return BrowserHelperConfig(**defaults)

    @staticmethod
    def get_web_interface_config(**overrides) -> BrowserHelperConfig:
        """Configuración para interfaz web - balance para usuarios."""
        defaults = {
            "headless": True,
            "user_data_dir": None,  # Usar perfil temporal para evitar conflictos de SingletonLock
            "use_vision": True,
            "enable_memory": True,
            "deterministic_rendering": True,
            "highlight_elements": False,
            "viewport_expansion": 600,
            "minimum_wait_page_load_time": 0.6,
            "wait_for_network_idle_page_load_time": 1.2,
            "maximum_wait_page_load_time": 12.0,
            "wait_between_actions": 0.6,
            "max_steps": 120,
            "max_failures": 3,
            "retry_delay": 8,
            "model_provider": "gemini",
            "temperature": 0.0,
            "generate_gif": False,
            "save_conversation_path": "./web_conversations"
        }
        defaults.update(overrides)
        return BrowserHelperConfig(**defaults)

    @staticmethod
    def get_api_testing_config(**overrides) -> BrowserHelperConfig:
        """Configuración para testing de APIs - enfoque en funcionalidad."""
        defaults = {
            "headless": True,
            "user_data_dir": None,  # Usar perfil temporal para evitar conflictos de SingletonLock
            "use_vision": False,  # APIs no necesitan visión
            "enable_memory": False,
            "deterministic_rendering": False,
            "highlight_elements": False,
            "viewport_expansion": 0,
            "minimum_wait_page_load_time": 0.2,
            "wait_for_network_idle_page_load_time": 0.5,
            "maximum_wait_page_load_time": 5.0,
            "wait_between_actions": 0.1,
            "max_steps": 40,
            "max_failures": 2,
            "retry_delay": 3,
            "model_provider": "gemini",
            "temperature": 0.0
        }
        defaults.update(overrides)
        return BrowserHelperConfig(**defaults)

    @staticmethod
    def get_load_testing_config(**overrides) -> BrowserHelperConfig:
        """Configuración para load testing - mínimo overhead."""
        defaults = {
            "headless": True,
            "user_data_dir": None,  # Usar perfil temporal para evitar conflictos de SingletonLock
            "use_vision": False,
            "enable_memory": False,
            "deterministic_rendering": False,
            "highlight_elements": False,
            "viewport_expansion": 0,
            "minimum_wait_page_load_time": 0.05,
            "wait_for_network_idle_page_load_time": 0.1,
            "maximum_wait_page_load_time": 2.0,
            "wait_between_actions": 0.01,
            "max_steps": 15,
            "max_failures": 1,
            "retry_delay": 1,
            "model_provider": "gemini",
            "temperature": 0.0
        }
        defaults.update(overrides)
        return BrowserHelperConfig(**defaults)

    @staticmethod
    def get_captcha_config(**overrides) -> CaptchaConfig:
        """Configuración especializada para manejo de CAPTCHAs de Google."""
        defaults = {
            # Configuración stealth anti-detección
            "stealth": True,
            "headless": False,  # Modo visible es mejor para CAPTCHAs
            
            # Configuración de tiempos más humanos
            "wait_between_actions": 3.0,
            "minimum_wait_page_load_time": 2.0,
            "wait_for_network_idle_page_load_time": 4.0,
            "maximum_wait_page_load_time": 20.0,
            
            # Configuración de navegador realista
            "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36",
            "viewport": {"width": 1366, "height": 768},
            "locale": "en-US",
            "timezone_id": "America/New_York",
            
            # Perfil persistente para mantener cookies y sessiones
            "user_data_dir": "~/.config/browseruse/profiles/captcha_profile",
            "profile_directory": "CaptchaSession",
            "keep_alive": True,
            
            # Dominios permitidos para Google/reCAPTCHA
            "allowed_domains": [
                "*.google.com",
                "*.recaptcha.net",
                "*.gstatic.com",
                "*.googleusercontent.com",
                "*.googleapis.com"
            ],
            
            # Configuración visual optimizada
            "use_vision": True,
            "highlight_elements": True,
            "viewport_expansion": 300,
            "deterministic_rendering": False,  # Más humano
            
            # Configuración de memoria y pasos
            "enable_memory": True,
            "max_steps": 40,  # Suficiente para manejo de CAPTCHA
            
            # Configuración de modelo
            "model_provider": "gemini",
            "use_vision": True,
            
            # Configuración de seguridad balanceada
            "disable_security": False,
            "bypass_csp": False,
        }
        
        merged = {**defaults, **overrides}
        return CaptchaConfig(**merged)

    @staticmethod
    def get_test_suite_config(**overrides) -> BrowserHelperConfig:
        """Configuración especializada para ejecución de suites completas - evitar límites de cuota API."""
        # Verificar si existe configuración de embedding para habilitar memoria
        embedding_model = os.getenv("EMBEDDING_MODEL")
        embedding_provider = os.getenv("EMBEDDING_PROVIDER", "gemini")
        enable_memory = embedding_model is not None and embedding_model.strip() != ""
        
        defaults = {
            "headless": True,
            "user_data_dir": None,  # Usar perfil temporal para evitar conflictos de SingletonLock
            "use_vision": True,
            "enable_memory": enable_memory,  # Habilitar memoria si hay modelo de embedding
            "deterministic_rendering": True,
            "highlight_elements": False,
            "viewport_expansion": 500,
            "minimum_wait_page_load_time": 1.0,  # Increased for suite stability
            "wait_for_network_idle_page_load_time": 2.0,  # Increased for better page stability
            "maximum_wait_page_load_time": 15.0,  # Increased for slow-loading pages
            "wait_between_actions": 2.0,  # Increased significantly for API quota management
            "max_steps": 30,
            "max_failures": 5,  # Increased to handle more retries
            "retry_delay": 15,  # Extended delay specifically for avoiding Gemini quota limits
            "model_provider": "gemini",
            "temperature": 0.1,  # Slightly increased for more diverse element selection strategies
            # Configuración de embeddings si está disponible
            "embedder_provider": embedding_provider if enable_memory else None,
            "embedder_model": embedding_model if enable_memory else None,
            "embedder_dims": int(os.getenv("EMBEDDING_DIMS", "768")) if enable_memory else None,
            "memory_interval": 8 if enable_memory else None,  # Extended interval for suite tests
            "memory_agent_id": "test_suite_agent" if enable_memory else None
        }
        defaults.update(overrides)
        return BrowserHelperConfig(**defaults)

class EnvironmentBasedConfig:
    """Configuraciones basadas en variables de entorno."""

    @staticmethod
    def get_config_for_environment() -> BrowserHelperConfig:
        """Obtiene configuración basada en variables de entorno."""
        env = os.getenv("BROWSER_TEST_ENV", "development").lower()

        if env == "ci":
            return BrowserConfigurations.get_ci_cd_config()
        elif env == "production":
            allowed_domains = os.getenv("ALLOWED_DOMAINS", "").split(",")
            allowed_domains = [d.strip() for d in allowed_domains if d.strip()]
            return BrowserConfigurations.get_production_config(allowed_domains)
        elif env == "staging":
            return BrowserConfigurations.get_regression_test_config(
                headless=True,
                max_steps=150
            )
        else:  # development
            return BrowserConfigurations.get_development_config()

    @staticmethod
    def get_model_provider_from_env() -> str:
        """Obtiene el proveedor de modelo desde variables de entorno."""
        if os.getenv("OPENAI_API_KEY"):
            return "openai"
        elif os.getenv("ANTHROPIC_API_KEY"):
            return "anthropic"
        elif os.getenv("GOOGLE_API_KEY"):
            return "gemini"
        else:
            return "gemini"  # Default


# Configuraciones de conveniencia
def get_config_by_type(config_type: str, **overrides) -> BrowserHelperConfig:
    """
    Obtiene configuración por tipo.

    Args:
        config_type: Tipo de configuración (ci, smoke, regression, dev, prod, web, api, load, captcha)
        **overrides: Parámetros para sobrescribir

    Returns:
        BrowserHelperConfig configurado
    """
    config_map = {
        "ci": BrowserConfigurations.get_ci_cd_config,
        "smoke": BrowserConfigurations.get_smoke_test_config,
        "regression": BrowserConfigurations.get_regression_test_config,
        "dev": BrowserConfigurations.get_development_config,
        "web": BrowserConfigurations.get_web_interface_config,
        "api": BrowserConfigurations.get_api_testing_config,
        "load": BrowserConfigurations.get_load_testing_config,
        "captcha": BrowserConfigurations.get_captcha_config,
        "test_suite": BrowserConfigurations.get_test_suite_config
    }

    if config_type == "prod":
        allowed_domains = overrides.pop("allowed_domains", [])
        return BrowserConfigurations.get_production_config(allowed_domains, **overrides)

    config_func = config_map.get(config_type, BrowserConfigurations.get_development_config)
    return config_func(**overrides)
