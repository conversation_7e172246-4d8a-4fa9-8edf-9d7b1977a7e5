
"""Rutas de API para generación de código y Gherkin."""

from fastapi import APIRouter, HTTPException, Depends

from src.API.models import GherkinRequest, CodeGenerationRequest
from src.Core.prompt_service import PromptService

# Router para generación de código y Gherkin
router = APIRouter(prefix="/api/generate", tags=["generation"])


def get_prompt_service():
    """Crea y devuelve una instancia del servicio de prompts."""
    return PromptService()


@router.post("/gherkin", summary="Generar escenario Gherkin")
async def create_gherkin_scenario(
    request: GherkinRequest,
    prompt_service: PromptService = Depends(get_prompt_service)
):
    """Genera un escenario Gherkin a partir de instrucciones."""
    try:
        from src.Utilities.response_cleaner import clean_gherkin_response

        # Use the new PromptService for Gherkin generation
        # Create context from the request
        context = {
            "instructions": request.instructions,
            "url": request.url,
            "user_story": request.user_story
        }
        
        # Generate test cases first, then <PERSON><PERSON>kin scenarios
        if request.user_story:
            # If we have a user story, enhance it and generate manual tests first
            enhanced_story = prompt_service.enhance_user_story(
                user_story=request.user_story,
                language=request.language
            )
            manual_tests = prompt_service.generate_manual_test_cases(
                enhanced_story=enhanced_story,
                language=request.language
            )
            gherkin = prompt_service.generate_gherkin(
                test_cases=manual_tests,
                language=request.language,
                **context
            )
        else:
            # For direct instructions, create a basic test case format
            test_cases = f"Test Case: {request.instructions}"
            gherkin = prompt_service.generate_gherkin(
                test_cases=test_cases,
                language=request.language,
                **context
            )

        # Clean the response
        clean_gherkin = clean_gherkin_response(gherkin)

        return {"gherkin": clean_gherkin}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/code", summary="Generar código de automatización")
async def generate_code(
    request: CodeGenerationRequest,
    prompt_service: PromptService = Depends(get_prompt_service)
):
    """Genera código de automatización para un framework específico."""
    try:
        # Use the new PromptService for code generation
        code = prompt_service.generate_code(
            framework=request.framework,
            gherkin_scenario=request.gherkin_scenario,
            history=request.test_history
        )
        return {"code": code}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
