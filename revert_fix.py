import os

# Leer el archivo
with open('src/Core/playwright_codegen_service.py', 'r', encoding='utf-8') as f:
    content = f.read()

# Reemplazar de vuelta al patrón original (3 dirname)
old_pattern = "os.path.dirname(os.path.dirname(os.path.abspath(__file__)))"
new_pattern = "os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))"

# Contar ocurrencias antes del cambio
count_before = content.count(old_pattern)
print(f"Ocurrencias encontradas: {count_before}")

# Hacer el reemplazo
content = content.replace(old_pattern, new_pattern)

# Contar ocurrencias después del cambio
count_after = content.count(old_pattern)
print(f"Ocurrencias restantes: {count_after}")

# Escribir el archivo
with open('src/Core/playwright_codegen_service.py', 'w', encoding='utf-8') as f:
    f.write(content)

print(f"Cambio revertido exitosamente. Se corrigieron {count_before - count_after} ocurrencias.")

# Verificar el directorio resultante
print(f"Directorio resultante: {os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath('src/Core/playwright_codegen_service.py'))))}")
