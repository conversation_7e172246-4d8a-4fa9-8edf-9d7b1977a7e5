"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { TestSuite, TestSuiteCreateInput, TestSuiteUpdateInput } from "@/lib/types";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { XIcon } from "lucide-react";


const suiteFormSchema = z.object({
  name: z.string().min(2, {
    message: "Suite name must be at least 2 characters.",
  }),
  description: z.string().min(5, {
    message: "Description must be at least 5 characters.",
  }),
  tags: z.array(z.string()).optional(),
});

type SuiteFormData = z.infer<typeof suiteFormSchema>;

interface SuiteFormProps {
  suite?: TestSuite;
  onSubmit: (data: TestSuiteCreateInput | TestSuiteUpdateInput) => Promise<void>;
  isSubmitting: boolean;
  submitButtonText?: string;
}

export function SuiteForm({
  suite,
  onSubmit,
  isSubmitting,
  submitButtonText = "Create Suite",
}: SuiteFormProps) {
  const [currentTag, setCurrentTag] = useState("");

  const form = useForm<SuiteFormData>({
    resolver: zodResolver(suiteFormSchema),
    defaultValues: {
      name: suite?.name || "",
      description: suite?.description || "",
      tags: suite?.tags || [],
    },
  });

  const handleAddTag = () => {
    if (currentTag.trim() !== "") {
      const currentTags = form.getValues("tags") || [];
      if(!currentTags.includes(currentTag.trim())) {
        form.setValue("tags", [...currentTags, currentTag.trim()]);
      }
      setCurrentTag("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    const currentTags = form.getValues("tags") || [];
    form.setValue("tags", currentTags.filter(tag => tag !== tagToRemove));
  };

  const handleSubmit = async (data: SuiteFormData) => {
    await onSubmit(data);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{suite ? "Edit Suite" : "Create New Suite"}</CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Suite Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Login & Authentication Tests" {...field} />
                  </FormControl>
                  <FormDescription>
                    A descriptive name for this group of tests.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Tests covering all aspects of user login and authentication."
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Details about what this test suite covers.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
             <FormField
              control={form.control}
              name="tags"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tags</FormLabel>
                   <div className="flex items-center gap-2">
                    <Input
                      placeholder="Add a tag (e.g., login, auth)"
                      value={currentTag}
                      onChange={(e) => setCurrentTag(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          handleAddTag();
                        }
                      }}
                    />
                    <Button type="button" variant="outline" onClick={handleAddTag}>Add Tag</Button>
                  </div>
                  <FormDescription>
                    Keywords to categorize your suite. Press Enter or click "Add Tag".
                  </FormDescription>
                  {field.value && field.value.length > 0 && (
                    <div className="flex flex-wrap gap-2 mt-2">
                      {field.value.map((tag) => (
                        <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                          {tag}
                           <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="h-4 w-4 text-muted-foreground hover:text-destructive"
                            onClick={() => handleRemoveTag(tag)}
                          >
                            <XIcon size={12} />
                            <span className="sr-only">Remove tag {tag}</span>
                          </Button>
                        </Badge>
                      ))}
                    </div>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type="submit" disabled={isSubmitting} className="w-full md:w-auto">
              {isSubmitting ? "Submitting..." : submitButtonText}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
