(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/[root of the server]__5325a7eb._.js", {

"[project]/node_modules/@tanstack/query-devtools/build/DevtoolsComponent/6BZD2SY2.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@tanstack_query-devtools_build_b9fb7b9e._.js",
  "static/chunks/node_modules_@tanstack_query-devtools_build_DevtoolsComponent_6BZD2SY2_432d6b97.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@tanstack/query-devtools/build/DevtoolsComponent/6BZD2SY2.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@tanstack/query-devtools/build/DevtoolsPanelComponent/G73NX4V5.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@tanstack_query-devtools_build_3e5c7cbd._.js",
  "static/chunks/dd92d_modules_@tanstack_query-devtools_build_DevtoolsPanelComponent_G73NX4V5_432d6b97.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@tanstack/query-devtools/build/DevtoolsPanelComponent/G73NX4V5.js [app-client] (ecmascript)");
    });
});
}}),
"[turbopack]/browser/dev/hmr-client/hmr-client.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/[turbopack]_browser_dev_hmr-client_hmr-client_ts_61dcf9ba._.js",
  "static/chunks/[turbopack]_browser_dev_hmr-client_hmr-client_ts_432d6b97._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[turbopack]/browser/dev/hmr-client/hmr-client.ts [app-client] (ecmascript)");
    });
});
}}),
}]);