"""Rutas de API para gestión de suites de pruebas."""

from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import JSONResponse

from src.Core.test_service import TestService
from src.API.models import (
    TestSuiteCreateRequest,
    TestSuiteUpdateRequest,
    TestSuiteResponse,
    SuccessResponse,
    SuiteExecutionResponse
)
import os

# Router para suites de pruebas
router = APIRouter(prefix="/api/projects/{project_id}/suites", tags=["test-suites"])


def get_test_service():
    """Crea y devuelve una instancia del servicio de pruebas."""
    return TestService(api_key=os.environ.get("GOOGLE_API_KEY"))


@router.post("/", response_model=TestSuiteResponse, summary="Crear suite de pruebas")
async def create_test_suite(
    project_id: str,
    request: TestSuiteCreateRequest,
    test_service: TestService = Depends(get_test_service)
):
    """Crea una nueva suite de pruebas en un proyecto."""
    try:
        suite_data = test_service.create_test_suite(
            project_id=project_id,
            name=request.name,
            description=request.description,
            tags=request.tags
        )
        if not suite_data:
            raise HTTPException(status_code=404, detail="Proyecto no encontrado")

        return TestSuiteResponse(**suite_data)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{suite_id}", response_model=TestSuiteResponse, summary="Obtener suite de pruebas")
async def get_test_suite(
    project_id: str,
    suite_id: str,
    test_service: TestService = Depends(get_test_service)
):
    """Obtiene una suite de pruebas por su ID."""
    try:
        suite_data = test_service.get_test_suite(project_id, suite_id)
        if not suite_data:
            raise HTTPException(status_code=404, detail="Suite de pruebas no encontrada")

        return TestSuiteResponse(**suite_data)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{suite_id}", response_model=TestSuiteResponse, summary="Actualizar suite de pruebas")
async def update_test_suite(
    project_id: str,
    suite_id: str,
    request: TestSuiteUpdateRequest,
    test_service: TestService = Depends(get_test_service)
):
    """Actualiza una suite de pruebas existente."""
    try:
        suite_data = test_service.update_test_suite(
            project_id=project_id,
            suite_id=suite_id,
            name=request.name,
            description=request.description,
            tags=request.tags
        )
        if not suite_data:
            raise HTTPException(status_code=404, detail="Suite de pruebas no encontrada")

        return TestSuiteResponse(**suite_data)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{suite_id}", response_model=SuccessResponse, summary="Eliminar suite de pruebas")
async def delete_test_suite(
    project_id: str,
    suite_id: str,
    test_service: TestService = Depends(get_test_service)
):
    """Elimina una suite de pruebas."""
    try:
        success = test_service.delete_test_suite(project_id, suite_id)
        if not success:
            raise HTTPException(status_code=404, detail="Suite de pruebas no encontrada")

        return SuccessResponse(message="Suite de pruebas eliminada exitosamente")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{suite_id}/execute", response_model=SuiteExecutionResponse,
            summary="Ejecutar suite completa", operation_id="execute_suite_by_id")
async def execute_test_suite(
    project_id: str,
    suite_id: str,
    test_service: TestService = Depends(get_test_service)
):
    """Ejecuta todos los casos de prueba de una suite."""
    try:
        result = await test_service.execute_test_suite(project_id, suite_id)

        if not result.get("success", False):
            raise HTTPException(status_code=400, detail=result.get("error", "Error en la ejecución"))

        return SuiteExecutionResponse(**result)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
