"""Modelos Pydantic para validación de datos en la API."""

from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field
from datetime import datetime


# === MODELOS PARA CONFIGURACIÓN DE PRUEBAS ===

class TestConfigurationRequest(BaseModel):
    """Modelo para configurar la ejecución de pruebas."""
    
    # Configuración del navegador
    headless: Optional[bool] = Field(None, description="Ejecutar en modo headless")
    use_vision: Optional[bool] = Field(True, description="Habilitar capacidades de visión")
    enable_memory: Optional[bool] = Field(False, description="Habilitar memoria del agente")
    highlight_elements: Optional[bool] = Field(True, description="Resaltar elementos interactivos")
    deterministic_rendering: Optional[bool] = Field(True, description="Habilitar renderizado determinista")
    disable_security: Optional[bool] = Field(False, description="Deshabilitar características de seguridad")
    
    # Configuración de rendimiento
    minimum_wait_page_load_time: Optional[float] = Field(0.5, description="Tiempo mínimo de espera para carga de página", ge=0)
    wait_for_network_idle_page_load_time: Optional[float] = Field(1.0, description="Tiempo de espera para inactividad de red", ge=0)
    maximum_wait_page_load_time: Optional[float] = Field(10.0, description="Tiempo máximo de espera para carga de página", ge=0)
    wait_between_actions: Optional[float] = Field(0.5, description="Tiempo de espera entre acciones", ge=0)
    viewport_expansion: Optional[int] = Field(500, description="Expansión del viewport en píxeles", ge=-1)
    
    # Configuración del agente
    max_steps: Optional[int] = Field(100, description="Número máximo de pasos", ge=1)
    max_failures: Optional[int] = Field(3, description="Número máximo de fallos permitidos", ge=1)
    retry_delay: Optional[float] = Field(5.0, description="Tiempo de espera entre reintentos", ge=0)
    temperature: Optional[float] = Field(0.0, description="Temperatura del modelo LLM", ge=0.0, le=2.0)
    
    # Configuración del modelo
    model_provider: Optional[str] = Field("gemini", description="Proveedor del modelo LLM")
    model_name: Optional[str] = Field(None, description="Nombre específico del modelo")
    
    # Configuración de memoria
    memory_agent_id: Optional[str] = Field(None, description="ID del agente para memoria")
    memory_interval: Optional[int] = Field(10, description="Intervalo de memoria", ge=1)
    
    # Configuración de embeddings
    embedder_provider: Optional[str] = Field(None, description="Proveedor de embeddings")
    embedder_model: Optional[str] = Field(None, description="Modelo de embeddings")
    embedder_dims: Optional[int] = Field(None, description="Dimensiones de embeddings", ge=1)
    
    # Configuración de dominios permitidos
    allowed_domains: Optional[List[str]] = Field(None, description="Lista de dominios permitidos")
    
    # Configuración de directorio de datos de usuario
    user_data_dir: Optional[str] = Field(None, description="Directorio de datos de usuario")
    
    # Configuración de viewport
    viewport: Optional[Dict[str, int]] = Field(None, description="Configuración del viewport")
    device_scale_factor: Optional[float] = Field(None, description="Factor de escala del dispositivo", ge=0.1)
    
    # Configuración de grabación
    record_video_dir: Optional[str] = Field(None, description="Directorio para grabación de video")
    trace_path: Optional[str] = Field(None, description="Ruta para trazas de depuración")
    save_conversation_path: Optional[str] = Field(None, description="Ruta para guardar conversaciones")
    generate_gif: Optional[bool] = Field(False, description="Generar GIF de la ejecución")
    
    # Configuración de sesión del navegador
    keep_alive: Optional[bool] = Field(False, description="Mantener el navegador vivo después de la ejecución")
    storage_state: Optional[str] = Field(None, description="Estado de almacenamiento del navegador")


class TestConfigurationResponse(BaseModel):
    """Modelo de respuesta para la configuración de pruebas."""
    
    config_id: str = Field(description="ID único de la configuración")
    name: str = Field(description="Nombre de la configuración")
    description: str = Field(description="Descripción de la configuración")
    config_type: str = Field(description="Tipo de configuración (ci, smoke, regression, dev, prod, web, api, load)")
    settings: Dict[str, Any] = Field(description="Configuraciones específicas")
    created_at: str = Field(description="Fecha de creación")
    updated_at: str = Field(description="Fecha de última actualización")
    is_default: bool = Field(False, description="Si es la configuración por defecto")


class TestConfigurationCreateRequest(BaseModel):
    """Modelo para crear una nueva configuración de pruebas."""
    
    name: str = Field(..., description="Nombre de la configuración", min_length=1, max_length=100)
    description: str = Field("", description="Descripción de la configuración", max_length=500)
    config_type: str = Field(..., description="Tipo de configuración")
    settings: TestConfigurationRequest = Field(..., description="Configuraciones específicas")
    is_default: bool = Field(False, description="Establecer como configuración por defecto")


class TestConfigurationUpdateRequest(BaseModel):
    """Modelo para actualizar una configuración de pruebas."""
    
    name: Optional[str] = Field(None, description="Nuevo nombre de la configuración", min_length=1, max_length=100)
    description: Optional[str] = Field(None, description="Nueva descripción de la configuración", max_length=500)
    config_type: Optional[str] = Field(None, description="Nuevo tipo de configuración")
    settings: Optional[TestConfigurationRequest] = Field(None, description="Nuevas configuraciones específicas")
    is_default: Optional[bool] = Field(None, description="Establecer como configuración por defecto")


class PredefinedConfigResponse(BaseModel):
    """Modelo de respuesta para configuraciones predefinidas."""
    
    config_type: str = Field(description="Tipo de configuración")
    name: str = Field(description="Nombre de la configuración")
    description: str = Field(description="Descripción de la configuración")
    settings: Dict[str, Any] = Field(description="Configuraciones por defecto")


# === MODELOS PARA GITHUB ===

class GitHubConfigRequest(BaseModel):
    """Modelo para configuración de GitHub."""
    enabled: bool = Field(False, description="Integración con GitHub habilitada")
    repo: Optional[str] = Field(None, description="Nombre del repositorio de GitHub")
    token: Optional[str] = Field(None, description="Token de acceso de GitHub")


# === MODELOS PARA PROYECTOS ===

class ProjectCreateRequest(BaseModel):
    """Modelo para crear un proyecto."""
    name: str = Field(..., description="Nombre del proyecto")
    description: Optional[str] = Field("", description="Descripción del proyecto")
    tags: Optional[List[str]] = Field([], description="Etiquetas del proyecto")
    github_config: Optional[GitHubConfigRequest] = Field(None, description="Configuración de GitHub")


class ProjectUpdateRequest(BaseModel):
    """Modelo para actualizar un proyecto."""
    name: Optional[str] = Field(None, description="Nuevo nombre del proyecto")
    description: Optional[str] = Field(None, description="Nueva descripción del proyecto")
    tags: Optional[List[str]] = Field(None, description="Nuevas etiquetas del proyecto")
    github_config: Optional[GitHubConfigRequest] = Field(None, description="Nueva configuración de GitHub")


class ProjectResponse(BaseModel):
    """Modelo para respuesta de proyecto."""
    project_id: str = Field(..., description="ID del proyecto")
    name: str = Field(..., description="Nombre del proyecto")
    description: str = Field(..., description="Descripción del proyecto")
    tags: List[str] = Field([], description="Etiquetas del proyecto")
    github_config: Optional[GitHubConfigRequest] = Field(None, description="Configuración de GitHub")
    created_at: str = Field(..., description="Fecha de creación")
    updated_at: str = Field(..., description="Fecha de última actualización")


# === MODELOS PARA SUITES DE PRUEBAS ===

class TestSuiteCreateRequest(BaseModel):
    """Modelo para crear una suite de pruebas."""
    name: str = Field(..., description="Nombre de la suite", min_length=1, max_length=200)
    description: str = Field("", description="Descripción de la suite", max_length=1000)
    tags: List[str] = Field(default_factory=list, description="Lista de etiquetas")


class TestSuiteUpdateRequest(BaseModel):
    """Modelo para actualizar una suite de pruebas."""
    name: Optional[str] = Field(None, description="Nuevo nombre de la suite", min_length=1, max_length=200)
    description: Optional[str] = Field(None, description="Nueva descripción de la suite", max_length=1000)
    tags: Optional[List[str]] = Field(None, description="Nueva lista de etiquetas")


class TestSuiteResponse(BaseModel):
    """Modelo de respuesta para una suite de pruebas."""
    suite_id: str
    name: str
    description: str
    tags: List[str]
    created_at: str
    updated_at: str
    test_cases: Dict[str, Any] = Field(default_factory=dict)


# === MODELOS PARA CASOS DE PRUEBA ===

class TestCaseCreateRequest(BaseModel):
    """Modelo para crear un caso de prueba."""
    name: str = Field(..., description="Nombre del caso de prueba", min_length=1, max_length=200)
    description: str = Field("", description="Descripción del caso de prueba", max_length=1000)
    instrucciones: str = Field("", description="Instrucciones para ejecutar la prueba", max_length=2000)
    historia_de_usuario: str = Field("", description="Historia de usuario", max_length=2000)
    gherkin: str = Field("", description="Escenario Gherkin", max_length=5000)
    url: str = Field("", description="URL para la prueba", max_length=500)
    tags: List[str] = Field(default_factory=list, description="Lista de etiquetas")


class TestCaseUpdateRequest(BaseModel):
    """Modelo para actualizar un caso de prueba."""
    name: Optional[str] = Field(None, description="Nuevo nombre del caso de prueba", min_length=1, max_length=200)
    description: Optional[str] = Field(None, description="Nueva descripción del caso de prueba", max_length=1000)
    instrucciones: Optional[str] = Field(None, description="Nuevas instrucciones para ejecutar la prueba", max_length=2000)
    historia_de_usuario: Optional[str] = Field(None, description="Nueva historia de usuario", max_length=2000)
    gherkin: Optional[str] = Field(None, description="Nuevo escenario Gherkin", max_length=5000)
    url: Optional[str] = Field(None, description="Nueva URL para la prueba", max_length=500)
    tags: Optional[List[str]] = Field(None, description="Nueva lista de etiquetas")


class TestCaseStatusUpdateRequest(BaseModel):
    """Modelo para actualizar el estado de un caso de prueba."""
    status: str = Field(..., description="Nuevo estado del caso de prueba")


class TestCaseResponse(BaseModel):
    """Modelo de respuesta para un caso de prueba."""
    test_id: str
    name: str
    description: str
    instrucciones: str
    historia_de_usuario: str
    gherkin: str
    url: str
    tags: List[str]
    created_at: str
    updated_at: str
    history_files: List[str] = Field(default_factory=list)
    status: str = "Not Executed"
    last_execution: Optional[str] = None
    code: str = ""
    framework: str = ""


# === MODELOS PARA EJECUCIÓN ===

class TestExecutionResponse(BaseModel):
    """Modelo de respuesta para la ejecución de un test."""
    success: bool
    test_id: Optional[str] = None
    test_name: Optional[str] = None
    result: Dict[str, Any] = Field(default_factory=dict)
    error: Optional[str] = None
    execution_time: Optional[str] = None


class SuiteExecutionResponse(BaseModel):
    """Modelo de respuesta para la ejecución de una suite."""
    success: bool
    suite_id: str
    suite_name: str
    total_tests: int
    passed: int
    failed: int
    results: List[TestExecutionResponse]
    execution_time: str
    error: Optional[str] = None


# === MODELOS GENÉRICOS ===

class SuccessResponse(BaseModel):
    """Modelo de respuesta genérica de éxito."""
    success: bool = True
    message: str = "Operación completada exitosamente"


class ErrorResponse(BaseModel):
    """Modelo de respuesta genérica de error."""
    success: bool = False
    error: str
    details: Optional[str] = None


class ListResponse(BaseModel):
    """Modelo de respuesta genérica para listas."""
    success: bool = True
    count: int
    items: List[Dict[str, Any]]


# === MODELOS PARA HISTORIALES ===

class TestHistoryResponse(BaseModel):
    """Modelo de respuesta para historial de pruebas."""
    test_id: str
    history_files: List[str]
    last_execution: Optional[str] = None
    status: str


# === MODELOS PARA GENERACIÓN DE CÓDIGO ===

class CodeGenerationRequest(BaseModel):
    """Modelo para solicitud de generación de código."""
    framework: str = Field(..., description="Framework para generar el código")
    gherkin_scenario: str = Field(..., description="Escenario Gherkin")
    test_history: Dict[str, Any] = Field(..., description="Historial de la prueba")


class CodeGenerationResponse(BaseModel):
    """Modelo de respuesta para generación de código."""
    success: bool = True
    framework: str
    code: str
    error: Optional[str] = None


# === MODELOS PARA GHERKIN ===

class GherkinRequest(BaseModel):
    """Modelo para solicitud de generación de escenario Gherkin."""
    instructions: str = Field(..., description="Instrucciones para la prueba")
    url: Optional[str] = Field(None, description="URL para la prueba (opcional)")
    user_story: Optional[str] = Field(None, description="Historia de usuario (opcional)")
    language: Optional[str] = Field("es", description="Idioma de respuesta ('es' o 'en')")


class GherkinResponse(BaseModel):
    """Modelo de respuesta para generación de Gherkin."""
    success: bool = True
    gherkin: str
    error: Optional[str] = None


# === MODELOS PARA SMOKE TESTS ===

class SmokeTestRequest(BaseModel):
    """Modelo para solicitud de smoke test."""
    instructions: str = Field(..., description="Instrucciones para la prueba")
    url: Optional[str] = Field(None, description="URL para la prueba (opcional)")
    user_story: Optional[str] = Field(None, description="Historia de usuario (opcional)")
    config_id: Optional[str] = Field(None, description="ID de configuración a usar (predefinida o personalizada)")
    configuration: Optional[TestConfigurationRequest] = Field(None, description="Configuración específica para la ejecución")


class FullTestRequest(BaseModel):
    """Modelo para solicitud de test completo."""
    gherkin_scenario: str = Field(..., description="Escenario Gherkin a ejecutar")
    url: Optional[str] = Field(None, description="URL para la prueba (opcional)")


# === MODELOS PARA SUMARIZACIÓN ===

class SummarizeRequest(BaseModel):
    """Modelo para solicitud de resumen de resultados de prueba."""
    test_results: str = Field(..., description="Resultados de la prueba a resumir")


class SummarizeResponse(BaseModel):
    """Modelo de respuesta para resumen de resultados."""
    summary: str = Field(..., description="Resumen generado por IA")


# === MODELOS PARA HISTORIAS DE USUARIO ===

class EnhanceStoryRequest(BaseModel):
    """Modelo para solicitud de mejora de historia de usuario."""
    user_story: str = Field(..., description="Historia de usuario a mejorar")
    language: Optional[str] = Field("es", description="Idioma de respuesta ('es' o 'en')")


class GenerateManualTestsRequest(BaseModel):
    """Modelo para solicitud de generación de casos de prueba manuales."""
    enhanced_story: str = Field(..., description="Historia de usuario mejorada")
    language: Optional[str] = Field("es", description="Idioma de respuesta ('es' o 'en')")


class GenerateGherkinRequest(BaseModel):
    """Modelo para solicitud de generación de escenarios Gherkin desde casos manuales."""
    manual_tests: str = Field(..., description="Casos de prueba manuales")
    language: Optional[str] = Field("es", description="Idioma de respuesta ('es' o 'en')")


class SaveHistoryRequest(BaseModel):
    """Modelo para solicitud de guardado de historial en proyecto."""
    project_id: str = Field(..., description="ID del proyecto")
    suite_id: str = Field(..., description="ID de la suite de pruebas")
    test_history: Dict[str, Any] = Field(..., description="Historial de la prueba")
    name: str = Field(..., description="Nombre del caso de prueba")
    description: str = Field(..., description="Descripción del caso de prueba")
    gherkin: str = Field(..., description="Escenario Gherkin")


# === MODELOS PARA PLAYWRIGHT CODEGEN ===

class PlaywrightCodegenRequest(BaseModel):
    """Modelo para solicitudes de generación de código con Playwright Codegen."""
    
    # URL y configuración básica
    url: Optional[str] = Field(None, description="URL inicial para grabación")
    target_language: str = Field("javascript", description="Lenguaje objetivo", pattern="^(javascript|typescript|python|java|csharp)$")
    
    # Configuración del navegador
    device: Optional[str] = Field(None, description="Dispositivo a emular")
    viewport_size: Optional[str] = Field(None, description="Tamaño del viewport (ancho,alto)", pattern=r"^\d+,\d+$")
    headless: bool = Field(True, description="Ejecutar en modo headless")
    
    # Configuración de entorno
    timezone: Optional[str] = Field(None, description="Zona horaria")
    geolocation: Optional[str] = Field(None, description="Ubicación geográfica (lat,lng)", pattern=r"^-?\d+\.?\d*,-?\d+\.?\d*$")
    language: Optional[str] = Field(None, description="Idioma del navegador")
    color_scheme: Optional[str] = Field(None, description="Esquema de colores", pattern="^(light|dark)$")
    
    # Gestión de estado
    load_storage: Optional[str] = Field(None, description="Ruta del archivo de estado a cargar")
    save_storage: Optional[str] = Field(None, description="Ruta donde guardar el estado")
    
    # Integración con QAK
    project_id: Optional[str] = Field(None, description="ID del proyecto QAK")
    test_suite: Optional[str] = Field(None, description="Suite de pruebas")
    user_story: Optional[str] = Field(None, description="Historia de usuario asociada")

class CodegenSessionInfo(BaseModel):
    """Información de una sesión de Playwright Codegen."""
    
    session_id: str = Field(description="ID único de la sesión")
    status: str = Field(description="Estado actual", pattern="^(starting|running|completed|failed|stopped)$")
    target_language: str = Field(description="Lenguaje objetivo")
    url: Optional[str] = Field(None, description="URL inicial")
    
    # Timestamps
    created_at: datetime = Field(description="Momento de creación")
    updated_at: datetime = Field(description="Última actualización")
    completed_at: Optional[datetime] = Field(None, description="Momento de completación")
    
    # Resultados
    generated_code: Optional[str] = Field(None, description="Código generado")
    error_message: Optional[str] = Field(None, description="Mensaje de error si falla")
    execution_results: Optional[List[Dict[str, Any]]] = Field(None, description="Resultados de la ejecución del test")
    
    # Metadatos
    artifacts_path: Optional[str] = Field(None, description="Ruta a artefactos generados")
    command_used: Optional[str] = Field(None, description="Comando ejecutado")
    project_integration: Optional[Dict[str, Any]] = Field(None, description="Datos de integración con proyecto QAK")

class CodegenTestCaseRequest(BaseModel):
    """Modelo para convertir código generado en caso de prueba QAK."""
    
    session_id: str = Field(description="ID de la sesión de codegen")
    test_name: str = Field(description="Nombre del caso de prueba")
    test_description: Optional[str] = Field(None, description="Descripción del caso de prueba")
    
    # Integración con QAK
    project_id: str = Field(description="ID del proyecto QAK")
    test_suite: Optional[str] = Field(None, description="Suite de pruebas")
    user_story: Optional[str] = Field(None, description="Historia de usuario asociada")
    
    # Configuración de conversión
    framework: str = Field("playwright", description="Framework objetivo")
    include_assertions: bool = Field(True, description="Incluir aserciones automáticas")
    add_error_handling: bool = Field(True, description="Agregar manejo de errores")
    
class CodegenStatsResponse(BaseModel):
    """Estadísticas de uso de Playwright Codegen."""
    
    total_sessions: int = Field(description="Total de sesiones creadas")
    active_sessions: int = Field(description="Sesiones actualmente activas")
    completed_sessions: int = Field(description="Sesiones completadas exitosamente")
    failed_sessions: int = Field(description="Sesiones fallidas")
    
    # Estadísticas por lenguaje
    sessions_by_language: Dict[str, int] = Field(description="Sesiones por lenguaje objetivo")
    
    # Promedios
    avg_session_duration: Optional[float] = Field(None, description="Duración promedio de sesión en segundos")
    avg_generated_lines: Optional[float] = Field(None, description="Líneas de código promedio generadas")
    
    # Última actividad
    last_session_at: Optional[datetime] = Field(None, description="Última sesión iniciada")


# === MODELOS PARA CONFIGURACIÓN DE GITHUB ===

class GitHubConfigRequest(BaseModel):
    """Modelo para configuración de GitHub."""
    enabled: bool = Field(False, description="GitHub integration enabled")
    repo: Optional[str] = Field(None, description="GitHub repository name")
    token: Optional[str] = Field(None, description="GitHub access token")
