# Backend Configuration
API_HOST=0.0.0.0
API_PORT=8000

# AI Provider Keys (Required)
GOOGLE_API_KEY=your_gemini_api_key_here

# Optional AI Providers
OPENAI_API_KEY=your_openai_key_here
ANTHROPIC_API_KEY=your_claude_key_here
GROQ_API_KEY=your_groq_key_here

# Frontend Configuration (will be set automatically in Render)
NEXT_PUBLIC_API_BASE_URL=https://your-backend-url.onrender.com/api

# API Keys
GEMINI_API_KEY=your_gemini_api_key_here
LLM_MODEL=gemini-2.5-flash-preview-05-20

# Optional AI Providers
OPENAI_API_KEY=
ANTHROPIC_API_KEY=
GROQ_API_KEY=
GITHUB_TOKEN=tu_github_token_personal
GITHUB_REPO=tu_usuario/tu_repositorio

# Embedding Model Configuration
# Text Embedding 004 de Gemini para memoria del browser
EMBEDDING_MODEL=models/text-embedding-004
EMBEDDING_PROVIDER=gemini
EMBEDDING_DIMS=768

# Language settings
# Options: en (English), es (Spanish)
PROMPT_LANGUAGE=es

# Logfire Observability
# Get your token from