"""
Prompt Markdown Parser for AgentQA
Parses Markdown files containing prompts and extracts structured data
"""

import re
from typing import Dict, List, Any
import logging

logger = logging.getLogger(__name__)
 
class PromptMarkdownParser:
    """Parses prompt files in Markdown format."""
    
    def __init__(self):
        """Initialize the parser."""
        # Pattern to match variables in format {variable_name}
        self.variable_pattern = re.compile(r'\{([a-zA-Z_][a-zA-Z0-9_]*)\}')
        
        # Required sections for a valid prompt file (now optional for flexibility)
        self.recommended_sections = [
            "Purpose",
            "Input Format", 
            "Output Format",
            "English Prompt",
            "Variables",
            "Examples"
        ]
        
        # Minimum required sections (more flexible)
        self.required_sections = [
            "English Prompt"  # Only this is truly required
        ]
    
    def parse_prompt_file(self, file_path: str) -> Dict[str, Any]:
        """Parse a prompt markdown file and extract sections.
        
        Args:
            file_path: Path to the markdown file
            
        Returns:
            Dict containing parsed sections
            
        Raises:
            FileNotFoundError: If file doesn't exist
            ValueError: If file structure is invalid
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except FileNotFoundError:
            raise FileNotFoundError(f"Prompt file not found: {file_path}")
        except Exception as e:
            raise ValueError(f"Error reading file {file_path}: {e}")
        
        return self.parse_markdown_content(content, file_path)
    
    def parse_markdown_content(self, content: str, source: str = "unknown") -> Dict[str, Any]:
        """Parse markdown content and extract sections.
        
        Args:
            content: Markdown content as string
            source: Source identifier for error reporting
            
        Returns:
            Dict containing parsed sections
        """
        sections = {}
        current_section = None
        current_subsection = None
        current_content = []
        
        lines = content.split('\n')
        
        for line in lines:
            # Check if this is a main section header (## Section Name)
            if line.startswith('## '):
                # Save previous section/subsection if exists
                self._save_current_section(sections, current_section, current_subsection, current_content)
                
                # Start new main section
                current_section = line[3:].strip()
                current_subsection = None
                current_content = []
            # Check if this is a subsection header (### Subsection Name)
            elif line.startswith('### '):
                # Save previous subsection if exists
                self._save_current_section(sections, current_section, current_subsection, current_content)
                
                # Start new subsection
                current_subsection = line[4:].strip()
                current_content = []
            else:
                # Add line to current section/subsection content
                if current_section:
                    current_content.append(line)
        
        # Save the last section/subsection
        self._save_current_section(sections, current_section, current_subsection, current_content)
        
        # Post-process to handle special cases for prompts
        sections = self._post_process_sections(sections)
        
        # Validate structure (now only warns, doesn't block)
        validation_errors = self.validate_structure(sections, source)
        if validation_errors:
            error_msg = f"Prompt structure warnings in {source}:\n" + '\n'.join(validation_errors)
            logger.warning(error_msg)  # Changed from error to warning
            # Don't raise error, just log warnings
        
        return sections
    
    def _save_current_section(self, sections: Dict[str, str], current_section: str, 
                             current_subsection: str, current_content: List[str]):
        """Helper method to save current section or subsection content."""
        if current_section and current_content:
            content = '\n'.join(current_content).strip()
            if current_subsection:
                # Use format "Section Subsection" for nested sections
                key = f"{current_section} {current_subsection}"
                sections[key] = content
            else:
                sections[current_section] = content
    
    def _post_process_sections(self, sections: Dict[str, str]) -> Dict[str, str]:
        """Post-process sections to handle special naming conventions."""
        processed = {}
        
        for key, content in sections.items():
            # Handle "Prompt English" -> "English Prompt" naming
            if key.startswith("Prompt "):
                language = key.replace("Prompt ", "")
                new_key = f"{language} Prompt"
                processed[new_key] = content
            else:
                processed[key] = content
        
        return processed
    
    def validate_structure(self, sections: Dict[str, str], source: str = "unknown") -> List[str]:
        """Validate that all required sections are present.
        
        Args:
            sections: Parsed sections dictionary
            source: Source identifier for error reporting
            
        Returns:
            List of validation error messages
        """
        errors = []
        
        # For now, just check that we have at least some content
        if not sections:
            errors.append("No sections found in prompt file")
        
        # Check that there's at least one prompt section (English or Spanish)
        has_prompt = any(section for section in sections.keys() if 'prompt' in section.lower())
        if not has_prompt:
            errors.append("No prompt sections found (should have 'English Prompt' or 'Spanish Prompt')")
        
        # Check that prompt sections have content
        for section_name, content in sections.items():
            if 'prompt' in section_name.lower() and not content.strip():
                errors.append(f"Empty prompt section: {section_name}")
        
        return errors
    
    def extract_variables(self, prompt_text: str) -> List[str]:
        """Extract variable names from prompt text.
        
        Args:
            prompt_text: Text containing variables in {variable_name} format
            
        Returns:
            List of unique variable names
        """
        matches = self.variable_pattern.findall(prompt_text)
        return list(set(matches))  # Remove duplicates
    
    def substitute_variables(self, prompt_text: str, **kwargs) -> str:
        """Substitute variables in prompt text with provided values.
        
        Args:
            prompt_text: Text containing variables in {variable_name} format
            **kwargs: Variable values to substitute
            
        Returns:
            Text with variables substituted
            
        Raises:
            ValueError: If required variables are missing
        """
        # Find all variables in the text
        required_vars = self.extract_variables(prompt_text)
        
        # Check that all required variables are provided
        missing_vars = [var for var in required_vars if var not in kwargs]
        if missing_vars:
            raise ValueError(f"Missing required variables: {missing_vars}")
        
        # Substitute variables
        result = prompt_text
        for var_name, value in kwargs.items():
            placeholder = "{" + var_name + "}"
            result = result.replace(placeholder, str(value))
        
        return result
    
    def extract_examples(self, sections: Dict[str, str]) -> List[Dict[str, str]]:
        """Extract examples from the Examples section.
        
        Args:
            sections: Parsed sections dictionary
            
        Returns:
            List of example dictionaries with 'input' and 'output' keys
        """
        examples = []
        
        if "Examples" not in sections:
            return examples
        
        examples_content = sections["Examples"]
        
        # Simple parsing - look for ### Input and ### Output patterns
        input_pattern = re.compile(r'### Input\s*\n```\s*\n(.*?)\n```', re.DOTALL)
        output_pattern = re.compile(r'### Output\s*\n```\s*\n(.*?)\n```', re.DOTALL)
        
        inputs = input_pattern.findall(examples_content)
        outputs = output_pattern.findall(examples_content)
        
        # Pair inputs with outputs
        for i, input_text in enumerate(inputs):
            output_text = outputs[i] if i < len(outputs) else ""
            examples.append({
                "input": input_text.strip(),
                "output": output_text.strip()
            })
        
        return examples
    
    def get_metadata_from_sections(self, sections: Dict[str, str]) -> Dict[str, Any]:
        """Extract metadata information from parsed sections.
        
        Args:
            sections: Parsed sections dictionary
            
        Returns:
            Dictionary containing metadata
        """
        metadata = {
            "purpose": sections.get("Purpose", "").strip(),
            "input_format": sections.get("Input Format", "").strip(),
            "output_format": sections.get("Output Format", "").strip(),
            "variables": [],
            "examples": self.extract_examples(sections),
            "validation_rules": []
        }
        
        # Extract variables from English prompt
        if "English Prompt" in sections:
            metadata["variables"] = self.extract_variables(sections["English Prompt"])
        
        # Parse validation rules if present
        if "Validation Rules" in sections:
            rules_text = sections["Validation Rules"]
            # Simple parsing - each line starting with - is a rule
            rules = [line[1:].strip() for line in rules_text.split('\n') 
                    if line.strip().startswith('-')]
            metadata["validation_rules"] = rules
        
        return metadata
